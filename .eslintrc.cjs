/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable no-undef */
// @ts-check
const { defineConfig } = require('eslint-define-config');

module.exports = defineConfig({
  env: {
    browser: true,
    node: true, // process.env.NODE_ENV
  },
  root: true,
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  extends: [
    'prettier',
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react-hooks/recommended',
  ],
  plugins: [
    '@typescript-eslint',
    // incrementally adopt rules as needed
    'import', // https://github.com/import-js/eslint-plugin-import
    'react', // https://github.com/jsx-eslint/eslint-plugin-react#list-of-supported-rules
    'react-hooks', // https://www.npmjs.com/package/eslint-plugin-react-hooks
  ],
  settings: { react: { version: 'detect' } },
  overrides: [
    {
      files: ['app/javascript/**/*.{js,jsx,ts,tsx}'],
      rules: {
        '@typescript-eslint/no-empty-function': ['off'],
        '@typescript-eslint/no-explicit-any': ['off'],
        '@typescript-eslint/naming-convention': [
          'error',
          {
            selector: 'typeLike',
            format: ['PascalCase'],
          },
        ],
        eqeqeq: ['error'],
        'import/no-anonymous-default-export': ['error'], // don't break HMR with anonymous components
        'no-empty-pattern': ['off'],
        'react-hooks/rules-of-hooks': ['error'],
        'react-hooks/exhaustive-deps': ['error'],
      },
    },
  ],
});

# See http://help.github.com/ignore-files/ for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile ~/.gitignore_global

# Folders
/.bundle
/log
/node_modules
/public/vite*
/tmp

# Files
.DS_Store
.env*
.eslintcache
.mise.local.toml
*.local.toml
*.pem
/config/master.key
/db/*.sqlite3
config/environments/*.local.yml
config/settings.local.yml
config/settings/*.local.yml
ssl.crt
ssl.key
tags
tsconfig.tsbuildinfo

# Vite uses dotenv and suggests to ignore local-only env files. See
# https://vitejs.dev/guide/env-and-mode.html#env-files
*.local

# rollup-plugin-visualizer
stats.html

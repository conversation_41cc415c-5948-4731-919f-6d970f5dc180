module Helpers
  def load_shopify_mock(name, format = :json)
    File.read(File.dirname(__FILE__) + "/fixtures/shopify/#{name}.#{format}")
  end

  def fake(endpoint, options = {})
    body = options.key?(:body) ? options.delete(:body) : load_shopify_mock(endpoint)
    format = options.delete(:format) || :json
    method = options.delete(:method) || :get
    parameters = options.delete(:parameters) || {}
    unless options[:extension] == false
      extension = ".#{options.delete(:extension) || "json"}"
    end

    url = if options.key?(:url)
      options[:url]
    else
      "https://test-app-1.myshopify.com/admin/#{endpoint}#{extension}"
    end

    if method == :get && !parameters.blank?
      url = [url, CGI.unescape(parameters.to_query)].join("?")
      # puts url
    end

    FakeWeb.register_uri(method, url, {body: body, status: 200, content_type: "text/#{format}", content_length: 1}.merge(options))
  end
end

FactoryBot.define do
  factory :shop do
    shopify_domain { "#{Faker::Internet.user_name}.myshopify.com" }
    shopify_token "592fd0dedb9ea198efc1748e1dca414e"
    installed_at { DateTime.new(2017, 0o1, 0o1, 0o1, 0o1, 0o1) }

    generated_password = Faker::Internet.password
    password { generated_password }
    password_confirmation { generated_password }

    factory :with_fake_source do
      after(:build) do |shop|
        shop.sources << FactoryBot.build(:source, sync_status: "started", next_schedule_time: Time.now + 5.days)
      end
    end

    factory :with_real_source do
      shopify_domain "wai-yan.myshopify.com"
      shopify_token "254eb8101a448f1c2b9892e5c093394a"
      charge_id 12
      package "basic"
      email "<EMAIL>"
      notification_email "<EMAIL>"
      domain "wai-yan.myshopify.com"
      after(:build) do |shop|
        shop.sources << FactoryBot.build(:real_source)
      end
    end

    factory :sample_shop do
      shopify_domain "test-app-1.myshopify.com"
      shopify_token "55ec810c3f99cd2ddf59affa94a16852"
      charge_id 12
      package "basic"
      email "<EMAIL>"
      domain "test-app-1.myshopify.com"
      after(:build) do |shop|
        shop.sources << FactoryBot.create(:email_source)
      end
    end
  end
end

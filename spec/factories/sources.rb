FactoryBot.define do
  factory :source do
    name { Faker::Name.name }
    source_host { Faker::Internet.ip_v4_address }
    source_login { Faker::Internet.user_name }
    source_password { Faker::Internet.password }
    source_parent_path "/"
    source_type "ftp"
    source_file_name "autofetch.csv"
    schedule_type "hourly"
    schedule_interval 12
    order_no_mapping 0
    sku_mapping 1
    quantity_mapping 2
    tracking_no_mapping 3
    tracking_company_mapping 4
    platform "shopify"
  end

  factory :real_source, class: "Source" do
    name "Real FTP Source"
    source_type "ftp"
    source_host "cloudcoder.com.my"
    source_parent_path "/"
    source_file_name "BL%{full_month}%{full_day}%{year}.csv"
    schedule_type "hourly"
    schedule_interval 1
    status "active"
    source_login "<EMAIL>"
    encrypted_source_password "E/ljzvzTpoDjeslHS7fkvA==\n"
    order_no_mapping 0
    tracking_no_mapping 1
  end

  factory :sync_source, class: "Source" do
    name { Faker::Name.name }
    source_type "file_upload"
    order_no_mapping 0
    tracking_no_mapping 1
    has_header true
  end

  factory :email_source, class: "Source" do
    name "Email Source"
    source_host "theworldofcim.com"
    source_file_name "autofetch.csv"
    schedule_type "hourly"
    schedule_interval 1
    status "active"
    email "<EMAIL>"
    order_no_mapping 0
    column_separator ","
    sku_mapping 1
    quantity_mapping 2
    tracking_no_mapping 3
    tracking_company_mapping 4
  end
end

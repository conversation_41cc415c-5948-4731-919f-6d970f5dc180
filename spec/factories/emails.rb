FactoryBot.define do
  factory :email, class: OpenStruct do
    # Assumes Griddler.configure.to is :hash (default)
    to [{full: "<EMAIL>", email: "<EMAIL>", token: "to_user", host: "email.com", name: nil},
      {full: "<EMAIL>", email: "<EMAIL>", token: "to_user", host: "email.com", name: nil}]
    from({token: "from_user", host: "email.com", email: "<EMAIL>", full: "From User <<EMAIL>>", name: "From User"})
    subject "email subject"
    body "Hello!"
    attachments { [] }

    trait :with_attachment do
      attachments do
        [
          ActionDispatch::Http::UploadedFile.new({
            filename: "sample.csv",
            type: "text/csv",
            tempfile: File.new("#{__dir__}/attachments/sample.csv")
          })
        ]
      end
    end
  end
end

require "rails_helper"

RSpec.describe Shop, type: :model do
  it "has installed_at field" do
    shop = create(:with_fake_source)
    expect(shop.installed_at).to be_present
  end

  describe "has expired trial period" do
    before :all do
      @shop = create(:with_fake_source)
      @shop.installed_at = Time.now - 15.days
      @shop.deactivate_shop
      @source = @shop.sources.first
    end

    it "deactivates user" do
      expect(@shop.active?).to eq(false)
    end

    describe "when user has syncable sources" do
      it "pauses the source from syncing" do
        expect(@source.sync_status).to eq("paused")
      end

      it "removes the next schedule time" do
        expect(@source.next_schedule_time).to be_nil
      end
    end
  end

  describe "update fulfillments" do
    before :all do
      @shop = build(:sample_shop, installed_at: Time.now - 8.days)
      @shop.create_session
    end

    it "creates Shopify fulfillment" do
      fulfillment = {
        order_id: 4_815_146_254,
        tracking_number: "ABC1235MY",
        tracking_numbers: ["ABC1235MY"],
        tracking_company: "USPS",
        tracking_url: "https://google.com",
        tracking_urls: ["https://google.com"]
      }

      fake "orders/4815146254/fulfillments", method: :post, body: load_shopify_mock("fulfillment")

      fulfillment = @shop.create_fulfillment(fulfillment, false)

      expect(fulfillment[:success]).to eq(true)
    end
  end
end

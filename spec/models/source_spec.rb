require "rails_helper"

RSpec.describe Source, type: :model do
  describe "is invalid" do
    it "without source name" do
      source = build(:source, name: nil)
      source.valid?
      expect(source.errors[:name]).to include("can't be blank")
    end

    describe "in column mapping" do
      it "when order number mapping is not integer" do
        source = build(:source, order_no_mapping: "test")
        source.valid?
        expect(source.errors[:order_no_mapping]).to include("is not a number")
      end

      it "when sku mapping is not integer" do
        source = build(:source, sku_mapping: "test")
        source.valid?
        expect(source.errors[:sku_mapping]).to include("is not a number")
      end

      it "when quantity mapping is not integer" do
        source = build(:source, quantity_mapping: "test")
        source.valid?
        expect(source.errors[:quantity_mapping]).to include("is not a number")
      end

      it "when tracking number mapping is not integer" do
        source = build(:source, tracking_no_mapping: "test")
        source.valid?
        expect(source.errors[:tracking_no_mapping]).to include("is not a number")
      end

      it "when tracking company mapping is not integer" do
        source = build(:source, tracking_company_mapping: "test")
        source.valid?
        expect(source.errors[:tracking_company_mapping]).to include("is not a number")
      end
    end

    describe "when schedule type is hourly" do
      it "without schedule interval" do
        source = build(:source, schedule_type: "hourly", schedule_interval: nil)
        source.valid?
        expect(source.errors[:schedule_interval]).to include("can't be blank")
      end

      it "and schedule interval less than 1" do
        source = build(:source, schedule_type: "hourly", schedule_interval: 0)
        source.valid?
        expect(source.errors[:schedule_interval]).to include("must be greater than or equal to 1")
      end

      it "and schedule interval is more than 24" do
        source = build(:source, schedule_type: "hourly", schedule_interval: 30)
        source.valid?
        expect(source.errors[:schedule_interval]).to include("must be less than or equal to 24")
      end
    end
  end

  describe "has proper source host" do
    context "when there is ftp prefix" do
      it "removes ftp prefix" do
        source = build(:source, source_host: "ftp://example.com")
        source.save
        expect(source.source_host).to eq "example.com"
      end
    end

    context "when there is no ftp prefix" do
      it "remains the same" do
        source = build(:source, source_host: "example.com")
        source.save
        expect(source.source_host).to eq "example.com"
      end
    end
  end

  describe "has proper source root path" do
    context "when there is no slash in the end of root path" do
      it "appends a slash" do
        source = build(:source, source_parent_path: "out")
        source.save
        expect(source.source_parent_path).to eq "out/"
      end
    end

    context "when there is one slash in the end of root path" do
      it "remains the same" do
        source = build(:source, source_parent_path: "out/")
        source.save
        expect(source.source_parent_path).to eq "out/"
      end
    end

    context "when there are many slashes in the end of root path" do
      it "removes extra slashes" do
        source = build(:source, source_parent_path: "out////")
        source.save
        expect(source.source_parent_path).to eq "out/"
      end
    end
  end

  describe "sets next schedule time properly" do
    before(:each) do
      @source = build(:source)
    end

    context "when it is started" do
      before do
        @source.update_sync_status(true)
      end

      it "sync status is started" do
        expect(@source.sync_status).to eq "started"
      end

      it "next schedule time should present" do
        expect(@source.next_schedule_time).to be_present
      end
    end

    context "when it is paused" do
      before do
        @source.update_sync_status(false)
      end

      it "sync status is paused" do
        expect(@source.sync_status).to eq "paused"
      end

      it "next schedule time should present" do
        expect(@source.next_schedule_time).not_to be_present
      end
    end
  end

  describe "sync fulfillments" do
    before do
      Timecop.freeze(Time.new(2015, 0o3, 0o3, 12, 50, 30))
    end

    after do
      Timecop.return
    end

    context "when next schedule time is earlier than current time" do
      it "is inside the syncable list" do
        time = Time.new(2015, 0o3, 0o3, 12, 48, 30)
        source = create(:source, next_schedule_time: time, sync_status: "started")
        syncable_sources = Source.syncable(Time.now.utc)
        expect(syncable_sources).to include(source)
      end
    end

    context "when next schedule time is later than current time" do
      it "is not inside the syncable list" do
        time = Time.new(2015, 0o3, 0o3, 13, 21, 0o5)
        source = create(:source, next_schedule_time: time, sync_status: "started")
        syncable_sources = Source.syncable(Time.now.utc)
        expect(syncable_sources).not_to include(source)
      end
    end
  end

  describe "after trial period" do
    context "when shop trial period is expired" do
      before :all do
        Timecop.freeze(Time.new(2015, 0o3, 0o3, 12, 50, 30))
        @time = Time.new(2015, 0o3, 0o3, 12, 48, 30)
        @shop = create(:with_fake_source)
        @shop.installed_at = Time.now - 8.days
        @shop.save
        @source = create(:source, next_schedule_time: @time, sync_status: "started")
        @source.shop = @shop
        @source.save

        @sources = Source.syncable(Time.now.utc)
      end

      after :each do
        Timecop.return
      end

      it "should include source in sync" do
        expect(@sources).to include(@source)
      end

      it "pauses the source from sync" do
        @source.deactivate unless @source.shop.active?
        expect(@source.sync_status).to eq("paused")
      end

      it "deactivates the shop" do
        expect(@shop.active?).to eq(false)
      end
    end

    context "when shop has upgraded" do
      before :all do
        Timecop.freeze(Time.new(2015, 0o3, 0o3, 12, 50, 30))
        @time = Time.new(2015, 0o3, 0o3, 12, 48, 30)
        @shop = create(:with_fake_source)
        @shop.charge_id = "1309274"
        @shop.package = "basic"
        @shop.installed_at = Time.now - 8.days
        @shop.save
        @source = create(:source, next_schedule_time: @time, sync_status: "started")
        @source.shop = @shop
        @source.save

        @sources = Source.syncable(Time.now.utc)
      end

      it "should include source in sync" do
        expect(@sources).to include(@source)
      end

      it "pauses the source from sync" do
        @source.deactivate unless @source.shop.active?
        expect(@source.sync_status).to eq("started")
      end

      it "deactivates the shop" do
        expect(@shop.active?).to eq(true)
      end
    end
  end

  # Unable to properly prove that timezone is correct
  # describe "updates next schedule time properly" do
  #   before :all do
  #     @time = Time.new(2015, 03, 03, 12, 50, 30).in_time_zone('Eastern Time (US & Canada)')
  #   end

  #   context "when scheduled intervally" do
  #     it "adds schedule interval" do
  #       source = create(:source, schedule_type: "hourly", schedule_interval: 3)
  #       expect(source.find_next_schedule_time(@time)).to eq(Time.new(2015, 03, 03, 15, 50, 30).in_time_zone('Eastern Time (US & Canada)'))
  #     end
  #   end

  #   context "when scheduled daily" do
  #     it "schedules the next day after schedule time is over for current day" do
  #       source = create(:source, schedule_type: "daily", schedule_time: "11:00 AM")
  #       expect(source.find_next_schedule_time(@time)).to eq(Time.new(2015, 03, 04, 11, 00, 00).in_time_zone('Eastern Time (US & Canada)'))
  #     end

  #     it "schedules the next day after schedule time has not over for current day" do
  #       source = create(:source, schedule_type: "daily", schedule_time: "3:00 PM")
  #       expect(source.find_next_schedule_time(@time)).to eq(Time.new(2015, 03, 03, 15, 00, 00).in_time_zone('Eastern Time (US & Canada)'))
  #     end
  #   end
  # end

  describe "returns data as array" do
    context "in CSV files" do
      it "returns data when data is not empty" do
        file = Rack::Test::UploadedFile.new(File.join(Rails.root, "spec", "sample_files", "csv.csv"))
        source = build(:sync_source, source_file: file)
        file_reader = source.build_file_reader

        expected_hash = {
          status: true,
          data: [["Order_ID", "tracking number"], ["#BY1064", "1232o4234273424"]],
          remark: nil
        }

        expect(source.download_file(file_reader)).to eq(expected_hash)
      end

      it "returns empty data array when data is empty" do
        file = Rack::Test::UploadedFile.new(File.join(Rails.root, "spec", "sample_files", "blank_csv.csv"))
        source = build(:sync_source, source_file: file)
        file_reader = source.build_file_reader

        expected_hash = {
          status: true,
          data: [],
          remark: nil
        }

        expect(source.download_file(file_reader)).to eq(expected_hash)
      end
    end

    context "in Excel 2007 files" do
      it "returns data when data is not empty" do
        file = Rack::Test::UploadedFile.new(File.join(Rails.root, "spec", "sample_files", "xlsx.xlsx"))
        source = build(:sync_source, source_file: file)
        file_reader = source.build_file_reader

        expected_hash = {
          status: true,
          data: [["Order Number", "Tracking Number"], ["#BY1068", "abc123ulkjgdf"]],
          remark: nil
        }

        expect(source.download_file(file_reader)).to eq(expected_hash)
      end

      it "returns empty data array when data is empty" do
        file = Rack::Test::UploadedFile.new(File.join(Rails.root, "spec", "sample_files", "blank_xlsx.xlsx"))
        source = build(:sync_source, source_file: file)
        file_reader = source.build_file_reader

        expected_hash = {
          status: true,
          data: [],
          remark: nil
        }

        expect(source.download_file(file_reader)).to eq(expected_hash)
      end
    end
  end

  describe "Email Source" do
    it "should set schedule type to on email received upon creation" do
      email_source = build(:email_source)
      email_source.source_type = "email"
      email_source.save
      expect(email_source.schedule_type).to eq "on_email_received"
    end

    it "does not need scheduler" do
      email_source = build(:email_source)
      email_source.source_type = "email"
      expect(email_source.needs_scheduler?).to eq(false)
    end

    it "does not need to be scheduled hourly" do
      email_source = build(:email_source)
      email_source.source_type = "email"
      expect(email_source.needs_scheduler?).to eq(false)
    end
  end

  describe "#sync_fulfillment" do
    it "should update fulfillment to Shopify" do
      _fakeweb1 = fake "orders/count", body: '{"count": 10}', method: :get, status: 201, parameters: {status: "any", financial_status: "paid", fulfillment_status: "unshipped"}
      _fakeweb1 = fake "orders/count", body: '{"count": 10}', method: :get, status: 201, parameters: {status: "any", financial_status: "paid", fulfillment_status: "partial"}

      fake "orders", body: load_shopify_mock("all_orders"), method: :get, status: 201, parameters: {status: "any", financial_status: "paid", fulfillment_status: "unshipped", limit: 250, order: "created_at+desc", page: 1}
      fake "orders", body: load_shopify_mock("all_orders"), method: :get, status: 201, parameters: {status: "any", financial_status: "paid", fulfillment_status: "partial", limit: 250, order: "created_at+desc", page: 1}
      fake "orders/4815146254/fulfillments", method: :post, body: load_shopify_mock("fulfillment")

      file = Rack::Test::UploadedFile.new(File.join(Rails.root, "spec", "sample_files", "tracking.csv"))
      shop = create(:sample_shop)
      source = create(:sync_source, source_file: file, order_key: "id", source_type: "file_upload", shop: shop, financial_status: "paid", platform: "shopify")

      _order_keys = ["#BY1064"]

      result = source.sync_fulfillments(:test, false)
      expect(result[:status]).to eq("success")
    end
  end
end

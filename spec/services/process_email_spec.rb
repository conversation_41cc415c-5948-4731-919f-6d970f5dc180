require "rails_helper"

RSpec.describe ProcessEmail do
  before(:all) do
    @email = build(:email)
    @email_with_attachment = build(:email, :with_attachment)
    @process_email = ProcessEmail.new(@email_with_attachment)
  end

  ## after_initialize callback causing issues
  describe "email processor process " do
    # it "creates email logs when runs with correct condition" do
    #   # correct conditions are
    #   # 1. source is available
    #   # 2. attachment is available from the email
    #   # 3. source's shop is valid
    #   shop = create(:sample_shop)
    #   source = shop.sources.first
    #   @process_email.process
    #   expect(source.email_logs.count).to eq(1)
    # end

    # this is integration test
    it "should trigger source to sync fulfilment with correct condition" do
      shop = create(:sample_shop)
      source = shop.sources.first
      source.source_type = "email"
      source.schedule_type = "on_email_received"
      source.save
      expect(@process_email.run(@process_email.email_object.to.first[:email])).to eq(true)
    end

    context "when email is not found, source is empty" do
      it "should return false status" do
        _shop = create(:sample_shop)
        expect(@process_email.run("<EMAIL>")).to eq(false)
      end
    end
  end
end

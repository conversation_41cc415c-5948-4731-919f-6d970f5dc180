require "rails_helper"

RSpec.describe FulfillmentWorker do
  describe "#get_order_identifier" do
    it "should filter out prefix and suffix when constant is a regex" do
      source = build(:source)
      fulfillment_worker = FulfillmentWorker.new(source)
      regex = Regexp.new('-[\w]*').to_s
      source.order_identifier_constants = regex
      source.save
      order = %w[1011-019abc KOALA2 1 12325346456657 USPS]
      order_no = fulfillment_worker.get_order_identifier(order)
      expect(order_no).to eq("1011")
    end

    it "should filter out prefix and suffix" do
      source = build(:source, order_identifier_constants: "#")
      fulfillment_worker = FulfillmentWorker.new(source)
      order = ["#1011", "KOALA2", "1", "12325346456657", "USPS"]
      order_no = fulfillment_worker.get_order_identifier(order)
      expect(order_no).to eq("1011")
    end

    it "should filter out prefix and suffix" do
      source = build(:source, order_identifier_constants: "MUG")
      fulfillment_worker = FulfillmentWorker.new(source)
      order = %w[MUG1011 KOALA2 1 12325346456657 USPS]
      order_no = fulfillment_worker.get_order_identifier(order)
      expect(order_no).to eq("1011")
    end

    it "should filter out prefix and suffix" do
      source = build(:source, order_identifier_constants: "91-")
      fulfillment_worker = FulfillmentWorker.new(source)
      order = %w[91-1011 KOALA2 1 12325346456657 USPS]
      order_no = fulfillment_worker.get_order_identifier(order)
      expect(order_no).to eq("1011")
    end

    it "should filter out prefix and suffix" do
      source = build(:source, order_identifier_constants: "")
      fulfillment_worker = FulfillmentWorker.new(source)
      order = %w[1011 KOALA2 1 12325346456657 USPS]
      order_no = fulfillment_worker.get_order_identifier(order)
      expect(order_no).to eq("1011")
    end
  end
end

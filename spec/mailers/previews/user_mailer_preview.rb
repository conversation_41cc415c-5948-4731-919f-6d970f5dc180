# Preview all emails at http://localhost:3000/rails/mailers/user_mailer
class UserMailerPreview < ActionMailer::Preview
  # Preview this email at http://localhost:3000/rails/mailers/user_mailer/notify_finish_process
  def notify_finish_process
    shop = Shop.first
    result = {
      status: "success",
      fulfillments: [{
        order_id: "3633086979",
        order_number: "1018116082906-759",
        tracking_number: "EN070292795MY",
        tracking_company: "POSLAJU",
        tracking_url: "http://poslaju.com.my/track-trace/#trackingIds=EN070292795MY"
      }, {
        order_id: "3633086979",
        order_number: "1018116082906-759",
        tracking_number: "EN070292795MY",
        tracking_company: "POSLAJU",
        tracking_url: "http://poslaju.com.my/track-trace/#trackingIds=EN070292795MY"
      }],
      sync_log: FactoryBot.build(:sync_log)
    }
    UserMailer.notify_finish_process(shop, result)
  end

  def notify_new_uptracker
    shop = Shop.first
    UserMailer.notify_new_uptracker(shop)
  end

  def notify_out_of_credit
    shop = Shop.first
    UserMailer.notify_out_of_credit(shop)
  end

  def notify_low_credit
    shop = Shop.first
    UserMailer.notify_low_credit(shop)
  end

  def billing_revoke
    shop = Shop.first
    UserMailer.billing_revoke(shop)
  end

  def uninstall_email
    shop = Shop.first
    UserMailer.uninstall_email(shop)
  end

  def notify_sync_success
    shop = Shop.first
    result = {
      status: "success",
      fulfillments: [{
        order_id: "3633086979",
        order_number: "1018116082906-759",
        tracking_number: "EN070292795MY",
        tracking_company: "POSLAJU",
        tracking_url: "http://poslaju.com.my/track-trace/#trackingIds=EN070292795MY"
      }, {
        order_id: "3633086979",
        order_number: "1018116082906-759",
        tracking_number: "EN070292795MY",
        tracking_company: "POSLAJU",
        tracking_url: "http://poslaju.com.my/track-trace/#trackingIds=EN070292795MY"
      }],
      sync_log: FactoryBot.build(:sync_log)
    }
    UserMailer.notify_sync_success(shop, result)
  end
end

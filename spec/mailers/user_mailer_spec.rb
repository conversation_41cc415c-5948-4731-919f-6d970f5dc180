require "rails_helper"

RSpec.describe UserMailer, type: :mailer do
  describe "notify_finish_process" do
    before(:all) do
      @shop = build(:with_real_source)
      @result = {
        status: "success",
        fulfillments: [{
          order_id: "3633086979",
          order_number: "1018116082906-759",
          tracking_number: "EN070292795MY",
          tracking_company: "POSLAJU",
          tracking_url: "http://poslaju.com.my/track-trace/#trackingIds=EN070292795MY"
        }],
        sync_log: build(:sync_log)
      }
    end

    let(:mail) { UserMailer.notify_finish_process(@shop, @result) }

    it "renders the headers" do
      expect(mail.subject).to eq("Uptracker Scheduled Process Finished")
      expect(mail.to).to eq(["<EMAIL>"])
      expect(mail.from).to eq(["<EMAIL>"])
    end

    it "renders the body" do
      expect(mail.body.encoded).to match("finished process")
    end
  end
end

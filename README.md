# First Time Setup

## Backend Setup

1. Install ruby `3.3.6` using rvm/rbenv

2. Install gems & setup database:

```
$ bundle install
$ rails db:create
$ rails db:schema:load
```

## Frontend Setup

1. Use a node version manager ([fnm](https://github.com/Schniz/fnm)
   or [nvm](https://github.com/nvm-sh/nvm)) to install the `node` version in `package.json`:

```json
  "engines": {
    "node": "...",
    ...
  },
```

2. Enable [corepack](https://nodejs.org/docs/latest-v20.x/api/corepack.html) to use the [packageManager](https://nodejs.org/docs/latest-v20.x/api/packages.html#packagemanager) defined in `package.json`:

```sh
$ corepack enable
$ pnpm install # install node dependencies
```

# Start The Application

## Start servers separately:

```sh
$ pnpm dev:jobs # delayed jobs server
$ pnpm dev:vite # frontend dev server
$ pnpm dev:rails # backend server

```

## ...or start all servers in parallel:

```sh
$ pnpm dev # run all 3 servers parallel in 1 terminal
```

### What's `mise.toml` and how do I use mise?

If you've installed [mise](https://mise.jdx.dev/), you can use it to run tasks for rails and scripts in `package.json` from commands in `mise.toml`:

```bash
# same as pnpm dev but pre-runs bundle install and pnpm install
# skips installs if cached output exists
# useful for different branches with changed lock files
$ mise dev
```

## Shopify CLI (Used for update app configuration setting with toml file)

1. Install Shopify CLI by `pnpm install -g @shopify/cli`
2. Try run `shopify` on terminal, you might found out `shopify` command is overwrite by ruby gem, open a new terminal without `source ~/.bash_profile`, you might save this command in `.zshrc`, temporary comment it out 1st.
3. To generate toml file for production configuration, run `shopify app config link -c production`, you can generate config for other env like staging (This step can be ignore if file existed)
4. Update `shopify.app.production.toml`, based on your need (scope, webhook)
5. Run `shopify app deploy -c production` to push the updated configuration setting to Shopify SS

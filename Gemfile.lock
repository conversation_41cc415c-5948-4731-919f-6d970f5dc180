GIT
  remote: **************:syncx-org/bigcommerce-api-ruby.git
  revision: d0c8fe5622e878932ea68bab0a539b9cb8c79e05
  specs:
    bigcommerce (1.0.2)
      faraday (>= 0.7.0)
      faraday_middleware (>= 0.12.2)
      hashie (>= 3.4)
      jwt (>= 1.5.4)

GIT
  remote: https://github.com/chrisk/fakeweb.git
  revision: 2b08c1ff2714ec13a12f3497d67fcefce95c2cbe
  specs:
    fakeweb (1.3.0)

GEM
  remote: https://rubygems.org/
  specs:
    ace-rails-ap (4.5)
    actioncable (********)
      actionpack (= ********)
      activesupport (= ********)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (********)
      actionpack (= ********)
      activejob (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      mail (>= 2.7.1)
    actionmailer (********)
      actionpack (= ********)
      actionview (= ********)
      activejob (= ********)
      activesupport (= ********)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (********)
      actionview (= ********)
      activesupport (= ********)
      rack (~> 2.0, >= 2.0.9)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (********)
      actionpack (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      nokogiri (>= 1.8.5)
    actionview (********)
      activesupport (= ********)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_utils (3.3.16)
      activesupport (>= 4.2)
      i18n
    activeadmin (3.2.2)
      arbre (~> 1.2, >= 1.2.1)
      csv
      formtastic (>= 3.1)
      formtastic_i18n (>= 0.4)
      inherited_resources (~> 1.7)
      jquery-rails (>= 4.2)
      kaminari (>= 1.2.1)
      railties (>= 6.1)
      ransack (>= 4.0)
    activeadmin_json_editor (0.0.10)
      ace-rails-ap
      railties (>= 3.0)
    activejob (********)
      activesupport (= ********)
      globalid (>= 0.3.6)
    activemodel (********)
      activesupport (= ********)
    activemodel-serializers-xml (1.0.2)
      activemodel (> 5.x)
      activesupport (> 5.x)
      builder (~> 3.1)
    activerecord (********)
      activemodel (= ********)
      activesupport (= ********)
    activeresource (5.1.1)
      activemodel (>= 5.0, < 7)
      activemodel-serializers-xml (~> 1.0)
      activesupport (>= 5.0, < 7)
    activestorage (********)
      actionpack (= ********)
      activejob (= ********)
      activerecord (= ********)
      activesupport (= ********)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (********)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
      zeitwerk (~> 2.3)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    airbrake (10.0.5)
      airbrake-ruby (~> 4.13)
    airbrake-ruby (4.15.0)
      rbtree3 (~> 0.5)
    airbrussh (1.5.3)
      sshkit (>= 1.6.1, != 1.7.0)
    arbre (1.7.0)
      activesupport (>= 3.0.0)
      ruby2_keywords (>= 0.0.2)
    ast (2.4.2)
    attr_encrypted (3.1.0)
      encryptor (~> 3.0.0)
    autoprefixer-rails (*********)
      execjs (~> 2)
    aws-eventstream (1.3.2)
    aws-partitions (1.1100.0)
    aws-sdk-apigatewaymanagementapi (1.39.0)
      aws-sdk-core (~> 3, >= 3.184.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-core (3.223.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-dynamodb (1.96.0)
      aws-sdk-core (~> 3, >= 3.184.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-secretsmanager (1.114.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.0)
    bigdecimal (3.1.9)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    bootstrap-sass (3.4.1)
      autoprefixer-rails (>= 5.2.1)
      sassc (>= 2.0.0)
    bootstrap-switch-rails (3.3.5)
    bootstrap3-datetimepicker-rails (4.7.14)
      momentjs-rails (>= 2.8.1)
    builder (3.3.0)
    byebug (11.1.3)
    capistrano (3.19.2)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.1.0)
      capistrano (~> 3.1)
    capistrano-maintenance (1.2.1)
      capistrano (>= 3.0)
    capistrano-nvm (0.0.7)
      capistrano (~> 3.1)
    capistrano-passenger (0.2.1)
      capistrano (~> 3.0)
    capistrano-rails (1.6.2)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rails-console (2.3.0)
      capistrano (>= 3.5.0, < 4.0.0)
      sshkit-interactive (~> 0.3.0)
    capistrano-rvm (0.1.2)
      capistrano (~> 3.0)
      sshkit (~> 1.2)
    capybara (3.39.2)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    carrierwave (2.2.6)
      activemodel (>= 5.0.0)
      activesupport (>= 5.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      mini_mime (>= 0.1.3)
      ssrf_filter (~> 1.0)
    certified (1.0.0)
    childprocess (3.0.0)
    chronic (0.10.2)
    coderay (1.1.3)
    coffee-rails (5.0.0)
      coffee-script (>= 2.2.0)
      railties (>= 5.2.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.3.4)
    config (2.2.3)
      deep_merge (~> 1.2, >= 1.2.1)
      dry-validation (~> 1.0, >= 1.0.0)
    crass (1.0.6)
    csv (3.3.4)
    daemons (1.3.1)
    database_cleaner (1.7.0)
    date (3.4.1)
    debug (1.8.0)
      irb (>= 1.5.0)
      reline (>= 0.3.1)
    decent_exposure (3.0.4)
      activesupport (>= 4.0)
    declarative (0.0.20)
    declarative-option (0.1.0)
    deep_merge (1.2.2)
    delayed_job (4.1.11)
      activesupport (>= 3.0, < 8.0)
    delayed_job_active_record (4.1.8)
      activerecord (>= 3.0, < 8.0)
      delayed_job (>= 3.0, < 5)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.3)
    docile (1.3.2)
    dotenv (3.1.8)
    double-bag-ftps (0.1.4)
    dry-cli (1.1.0)
    dry-configurable (1.0.1)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-core (1.0.0)
      concurrent-ruby (~> 1.0)
      zeitwerk (~> 2.6)
    dry-inflector (1.0.0)
    dry-initializer (3.1.1)
    dry-logic (1.5.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-schema (1.13.0)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 1.0, >= 1.0.1)
      dry-core (~> 1.0, < 2)
      dry-initializer (~> 3.0)
      dry-logic (>= 1.5, < 2)
      dry-types (>= 1.7, < 2)
      zeitwerk (~> 2.6)
    dry-types (1.7.1)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0)
      dry-inflector (~> 1.0)
      dry-logic (~> 1.4)
      zeitwerk (~> 2.6)
    dry-validation (1.10.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0, < 2)
      dry-initializer (~> 3.0)
      dry-schema (>= 1.12, < 2)
      zeitwerk (~> 2.6)
    ecwid_api (0.2.3)
      faraday (~> 0.9)
      faraday_middleware (~> 0.9)
    ed25519 (1.3.0)
    encryptor (3.0.0)
    erubi (1.13.0)
    execjs (2.9.1)
    factory_bot (4.11.1)
      activesupport (>= 3.0.0)
    factory_bot_rails (4.11.1)
      factory_bot (~> 4.11.1)
      railties (>= 3.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (0.17.6)
      multipart-post (>= 1.2, < 3)
    faraday_middleware (0.14.0)
      faraday (>= 0.7.4, < 1.0)
    ffi (1.16.3)
    font-awesome-sass (5.11.2)
      sassc (>= 1.11)
    formtastic (5.0.0)
      actionpack (>= 6.0.0)
    formtastic_i18n (0.7.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-api-client (0.52.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 0.9)
      httpclient (>= 2.8.1, < 3.0)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.0)
      rexml
      signet (~> 0.12)
    googleauth (0.13.1)
      faraday (>= 0.17.3, < 2.0)
      jwt (>= 1.4, < 3.0)
      memoist (~> 0.16)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (~> 0.14)
    graphiql-rails (1.7.0)
      railties
      sprockets-rails
    graphql (2.0.32)
      base64
    griddler (1.5.2)
      htmlentities
      rails (>= 3.2.0)
    griddler-mailgun (1.1.1)
      griddler
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hash_diff (1.1.1)
    hashie (5.0.0)
    highcharts-rails (*******)
      railties (>= 3.1)
    htmlentities (4.3.4)
    httparty (0.23.1)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    inherited_resources (1.14.0)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    io-console (0.7.0)
    irb (1.6.3)
      reline (>= 0.3.0)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.6.3)
    jwt (2.10.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    launchy (2.4.3)
      addressable (~> 2.3)
    lint_roller (1.1.0)
    listen (3.0.8)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    logger (*******)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    memoist (0.16.2)
    method_source (1.1.0)
    migration_data (0.3.1)
    mini_magick (4.12.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    momentjs-rails (2.20.1)
      railties (>= 3.1)
    msgpack (1.8.0)
    multi_json (1.15.0)
    multi_xml (0.7.2)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    net-ftp (0.3.8)
      net-protocol
      time
    net-imap (0.3.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.3)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    offsite_payments (2.7.21)
      actionpack (>= 5.2.3)
      actionview (>= *******)
      active_utils (~> 3.3.0)
      activesupport (>= 5.2.3)
      builder (>= 2.1.2, < 4.0.0)
      i18n (>= 0.6.6)
      nokogiri (>= 1.8.5)
    oj (3.16.10)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    omniauth (2.1.1)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-shopify-oauth2 (2.3.2)
      activesupport
      omniauth-oauth2 (~> 1.5)
    openssl (3.3.0)
    orm_adapter (0.5.0)
    os (1.1.1)
    ostruct (0.6.1)
    parallel (1.23.0)
    parser (3.2.2.4)
      ast (~> 2.4.1)
      racc
    paypal-sdk-core (0.3.4)
      multi_json (~> 1.0)
      xml-simple
    paypal-sdk-permissions (1.96.4)
      paypal-sdk-core (~> 0.3.0)
    pg (1.5.9)
    pretender (0.3.4)
      actionpack (>= 4.2)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    psych (3.3.4)
    public_suffix (4.0.7)
    puma (6.4.3)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (2.2.14)
    rack-protection (3.0.5)
      rack
    rack-proxy (0.7.7)
      rack
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (********)
      actioncable (= ********)
      actionmailbox (= ********)
      actionmailer (= ********)
      actionpack (= ********)
      actiontext (= ********)
      actionview (= ********)
      activejob (= ********)
      activemodel (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      bundler (>= 1.15.0)
      railties (= ********)
      sprockets-rails (>= 2.0.0)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails_same_site_cookie (0.1.9)
      rack (>= 1.5)
      user_agent_parser (~> 2.6)
    railties (********)
      actionpack (= ********)
      activesupport (= ********)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
    rainbow (3.1.1)
    rake (13.2.1)
    ransack (4.0.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rb-readline (0.5.5)
    rbtree3 (0.7.1)
    rdbg (0.1.0)
      debug (>= 1.2.2)
    redirect_safely (1.0.0)
      activemodel
    redis (4.1.3)
    regexp_parser (2.8.2)
    reline (0.4.1)
      io-console (~> 0.5)
    repost (0.4.1)
    representable (3.0.4)
      declarative (< 0.1.0)
      declarative-option (< 0.2.0)
      uber (< 0.2.0)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    retriable (3.1.2)
    rexml (3.4.4)
    roo (2.8.2)
      nokogiri (~> 1)
      rubyzip (>= 1.2.1, < 2.0.0)
    roo-xls (1.2.0)
      nokogiri
      roo (>= 2.0.0, < 3)
      spreadsheet (> 0.9.0)
    rspec (3.9.0)
      rspec-core (~> 3.9.0)
      rspec-expectations (~> 3.9.0)
      rspec-mocks (~> 3.9.0)
    rspec-core (3.9.0)
      rspec-support (~> 3.9.0)
    rspec-expectations (3.9.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.9.0)
    rspec-mocks (3.9.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.9.0)
    rspec-rails (3.9.0)
      actionpack (>= 3.0)
      activesupport (>= 3.0)
      railties (>= 3.0)
      rspec-core (~> 3.9.0)
      rspec-expectations (~> 3.9.0)
      rspec-mocks (~> 3.9.0)
      rspec-support (~> 3.9.0)
    rspec-support (3.9.0)
    rubocop (1.50.2)
      json (~> 2.3)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.28.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.30.0)
      parser (>= *******)
    rubocop-performance (1.16.0)
      rubocop (>= 1.7.0, < 2.0)
      rubocop-ast (>= 0.4.0)
    ruby-ole (********)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.2)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    rubyzip (1.3.0)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    securerandom (0.4.1)
    selenium-webdriver (3.142.6)
      childprocess (>= 0.5, < 4.0)
      rubyzip (>= 1.2.2)
    shopify-mock (0.1.2)
      fakeweb (>= 1.3.0)
      rake (>= 0.8.7)
      rspec (>= 2.6.0)
    shopify-money (0.13.1)
    shopify_api (14.10.0)
      activesupport
      concurrent-ruby
      hash_diff
      httparty
      jwt
      oj
      openssl
      securerandom
      sorbet-runtime
      zeitwerk (~> 2.5)
    shopify_app (22.00.0)
      activeresource
      addressable (~> 2.7)
      jwt (>= 2.2.3)
      rails (> 5.2.1)
      redirect_safely (~> 1.0)
      shopify_api (~> 14)
      sprockets-rails (>= 2.0.0)
    signet (0.14.0)
      addressable (~> 2.3)
      faraday (>= 0.17.3, < 2.0)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.17.1)
      docile (~> 1.1)
      json (>= 1.8, < 3)
      simplecov-html (~> 0.10.0)
    simplecov-html (0.10.2)
    slackistrano (4.0.1)
      capistrano (>= 3.8.1)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    sorbet-runtime (0.5.12083)
    spreadsheet (1.2.5)
      ruby-ole (>= 1.0)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sshkit (1.23.2)
      base64
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    sshkit-interactive (0.3.0)
      sshkit (~> 1.12)
    ssrf_filter (1.1.2)
    standard (1.28.5)
      language_server-protocol (~> ********)
      lint_roller (~> 1.0)
      rubocop (~> 1.50.2)
      standard-custom (~> 1.0.0)
      standard-performance (~> 1.0.1)
    standard-custom (1.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.50)
    standard-performance (1.0.1)
      lint_roller (~> 1.0)
      rubocop-performance (~> 1.16.0)
    state_machines (0.6.0)
    state_machines-activemodel (0.9.0)
      activemodel (>= 6.0)
      state_machines (>= 0.6.0)
    state_machines-activerecord (0.9.0)
      activerecord (>= 6.0)
      state_machines-activemodel (>= 0.9.0)
    stripe (5.37.0)
    thor (1.4.0)
    tilt (2.3.0)
    time (0.4.1)
      date
    timecop (0.9.1)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    tzinfo-data (1.2024.2)
      tzinfo (>= 1.0.0)
    uber (0.1.0)
    uglifier (4.2.0)
      execjs (>= 0.3.0, < 3)
    unicode-display_width (2.5.0)
    user_agent_parser (2.15.0)
    version_gem (1.1.2)
    vite_rails (3.0.17)
      railties (>= 5.1, < 8)
      vite_ruby (~> 3.0, >= 3.2.2)
    vite_ruby (3.8.2)
      dry-cli (>= 0.7, < 2)
      rack-proxy (~> 0.6, >= 0.6.1)
      zeitwerk (~> 2.2)
    warden (1.2.9)
      rack (>= 2.0.9)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    xml-simple (1.1.8)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.2)

PLATFORMS
  ruby

DEPENDENCIES
  activeadmin (~> 3.2.2)
  activeadmin_json_editor (~> 0.0.7)
  activeresource
  airbrake (= 10.0.5)
  attr_encrypted
  aws-sdk-apigatewaymanagementapi
  aws-sdk-dynamodb
  aws-sdk-secretsmanager (~> 1.0)
  bcrypt_pbkdf
  bigcommerce (~> 1.0)!
  bootsnap
  bootstrap-sass
  bootstrap-switch-rails
  bootstrap3-datetimepicker-rails (~> 4.7.14)
  byebug
  capistrano
  capistrano-bundler
  capistrano-maintenance (~> 1.0)
  capistrano-nvm
  capistrano-passenger
  capistrano-rails
  capistrano-rails-console
  capistrano-rvm
  capybara
  carrierwave
  certified
  coffee-rails
  concurrent-ruby (= 1.3.4)
  config (~> 2.2.1)
  daemons
  database_cleaner
  debug (= 1.8)
  decent_exposure
  delayed_job_active_record
  devise (= 4.9.4)
  dotenv
  double-bag-ftps
  ecwid_api
  ed25519
  factory_bot_rails (~> 4.0)
  faker
  fakeweb!
  faraday
  ffi (< 1.17.0)
  font-awesome-sass
  google-api-client
  graphiql-rails
  graphql
  griddler-mailgun
  highcharts-rails (~> 4.0.0)
  httparty
  kaminari
  launchy
  listen (>= 3.0.5, < 3.2)
  migration_data
  mini_portile2 (~> 2.8, >= 2.8.1)
  momentjs-rails (>= 2.9.0)
  net-ftp
  net-scp
  net-sftp
  nokogiri
  offsite_payments
  omniauth-shopify-oauth2
  paypal-sdk-permissions
  pg (~> 1.1)
  pretender
  pry-rails
  psych (< 4)
  puma
  rails (= ********)
  rails_same_site_cookie
  rb-readline
  rdbg
  redis
  repost
  roo
  roo-xls
  rspec-rails
  sass-rails
  selenium-webdriver
  shopify-mock
  shopify-money
  shopify_api
  shopify_app
  simplecov
  slackistrano
  standard
  state_machines-activerecord
  stripe
  timecop
  tzinfo-data
  uglifier (>= 1.0.3)
  vite_rails
  whenever

RUBY VERSION
   ruby 3.3.6p108

BUNDLED WITH
   2.4.3

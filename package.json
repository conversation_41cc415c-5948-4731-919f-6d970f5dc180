{"name": "fulfillment-sync", "private": true, "type": "module", "engines": {"node": "^20.19.0", "npm": "not-supported", "pnpm": "^9.15.6", "yarn": "not-supported"}, "packageManager": "pnpm@9.15.6", "scripts": {"build": "stale-dep && pnpm build:clean && NODE_OPTIONS=--max-old-space-size=3072 bundle exec vite build", "build:clean": "rimraf public/vite*", "build:production": "pnpm build -- --mode production", "build:visualizer": "pnpm build -- --mode visualizer", "dev:jobs": "bundle exec rails jobs:work", "dev:rails": "bundle exec rails s", "dev:vite": "stale-dep && bundle exec vite dev", "dev": "concurrently -k -c auto -n dev: npm:dev:*", "lint": "eslint app/javascript --cache", "postinstall": "stale-dep -u", "prepare": "lefthook install", "typecheck": "stale-dep && tsc -p tsconfig.json --noEmit"}, "dependencies": {"@apollo/client": "^3.8.7", "@shopify/polaris": "^13.9.5", "@shopify/polaris-icons": "^8.11.1", "apollo-upload-client": "9.1.0", "dayjs": "^1.11.10", "formik": "^2.4.5", "graphql": "^15.10.1", "immutable": "^5.1.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "notistack": "^3.0.2", "posthog-js": "^1.236.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-router-dom": "^6.20.0", "sockette": "^2.0.6", "ts-pattern": "^5.5.0", "yup": "^1.4.0"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@rollup/plugin-typescript": "^11.1.6", "@types/js-cookie": "^3.0.5", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.16", "concurrently": "^9.1.2", "eslint": "^8.57.1", "eslint-config-prettier": "^8.10.0", "eslint-define-config": "^1.24.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^5.2.0", "lefthook": "^1.11.13", "postcss-nesting": "^12.0.2", "prettier": "3.2.5", "rimraf": "5", "rollup-plugin-visualizer": "^5.12.0", "stale-dep": "^0.8.2", "typescript": "^5.8.2", "vite": "^6.3.5", "vite-plugin-rails": "^0.5.0"}, "pnpm": {"overrides": {"@babel/runtime@<7.26.10": ">=7.26.10", "cross-spawn": "^7.0.5", "micromatch": "^4.0.8", "nanoid@<3.3.8": ">=3.3.8", "esbuild@<=0.24.2": ">=0.25.0", "brace-expansion@>=1.0.0 <=1.1.11": "^1.1.12", "brace-expansion@>=2.0.0 <=2.0.1": "^2.0.2"}}}
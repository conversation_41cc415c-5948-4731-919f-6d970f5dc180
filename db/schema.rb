# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2024_11_02_141618) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "active_admin_comments", id: :serial, force: :cascade do |t|
    t.string "namespace"
    t.text "body"
    t.string "resource_id", null: false
    t.string "resource_type", null: false
    t.integer "author_id"
    t.string "author_type"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["author_type", "author_id"], name: "index_active_admin_comments_on_author_type_and_author_id"
    t.index ["namespace"], name: "index_active_admin_comments_on_namespace"
    t.index ["resource_type", "resource_id"], name: "index_active_admin_comments_on_resource_type_and_resource_id"
  end

  create_table "admin_users", id: :serial, force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.inet "current_sign_in_ip"
    t.inet "last_sign_in_ip"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_admin_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_admin_users_on_reset_password_token", unique: true
  end

  create_table "background_processes", force: :cascade do |t|
    t.integer "running_source_count"
    t.integer "running_job_count"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "billings", id: :serial, force: :cascade do |t|
    t.string "charge_id"
    t.string "shop_name"
    t.string "plan_name"
    t.string "status"
    t.string "remark"
    t.float "total_charge"
    t.boolean "refunded", default: false
    t.integer "shop_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["shop_id"], name: "index_billings_on_shop_id"
  end

  create_table "delayed_jobs", id: :serial, force: :cascade do |t|
    t.integer "priority", default: 0, null: false
    t.integer "attempts", default: 0, null: false
    t.text "handler", null: false
    t.text "last_error"
    t.datetime "run_at"
    t.datetime "locked_at"
    t.datetime "failed_at"
    t.string "locked_by"
    t.string "queue"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.bigint "source_id"
    t.index ["priority", "run_at"], name: "delayed_jobs_priority"
    t.index ["source_id"], name: "index_delayed_jobs_on_source_id"
  end

  create_table "email_logs", force: :cascade do |t|
    t.bigint "source_id"
    t.text "raw_msg"
    t.string "recipient"
    t.string "source_file"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["source_id"], name: "index_email_logs_on_source_id"
  end

  create_table "file_replacements", id: :serial, force: :cascade do |t|
    t.integer "source_id"
    t.string "column"
    t.string "replace_from"
    t.string "replace_to"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "nil_if_not_found", default: false
    t.index ["source_id"], name: "index_file_replacements_on_source_id"
  end

  create_table "fulfillment_logs", id: :serial, force: :cascade do |t|
    t.integer "source_id"
    t.bigint "shopify_order_id"
    t.string "tracking_no"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "order_number"
    t.string "tracking_company"
    t.string "sku"
    t.integer "quantity"
    t.string "fulfillment_status"
    t.string "error_message"
    t.integer "shop_id"
    t.bigint "shopify_fulfillment_id"
    t.string "financial_status"
    t.integer "sync_log_id"
    t.string "original_values"
    t.integer "bigcommerce_shipment_id"
    t.float "lineitem_amount", default: 0.0
    t.float "order_amount", default: 0.0
    t.float "usd_amount"
    t.string "currency"
    t.index ["shop_id", "source_id", "created_at"], name: "index_fulfillment_logs_on_shop_id_and_source_id_and_created_at"
    t.index ["shop_id", "source_id"], name: "index_fulfillment_logs_on_shop_id_and_source_id"
    t.index ["shop_id"], name: "index_fulfillment_logs_on_shop_id"
    t.index ["source_id", "id"], name: "index_fulfillment_logs_idx_source_sort"
    t.index ["source_id"], name: "index_fulfillment_logs_on_source_id"
  end

  create_table "orders", force: :cascade do |t|
    t.string "order_no"
    t.string "order_name"
    t.string "app_order_id"
    t.string "app"
    t.bigint "shop_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "trace"
    t.index ["app_order_id"], name: "index_orders_on_app_order_id"
    t.index ["order_name"], name: "index_orders_on_order_name"
    t.index ["order_no"], name: "index_orders_on_order_no"
    t.index ["shop_id", "order_name"], name: "index_orders_on_shop_id_and_order_name"
    t.index ["shop_id"], name: "index_orders_on_shop_id"
  end

  create_table "paypal_orders", force: :cascade do |t|
    t.boolean "success"
    t.string "transaction_id"
    t.bigint "fulfillment_log_id"
    t.index ["fulfillment_log_id"], name: "index_paypal_orders_on_fulfillment_log_id"
  end

  create_table "shops", id: :serial, force: :cascade do |t|
    t.string "shopify_domain"
    t.string "shopify_token"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "charge_id"
    t.datetime "charged_at"
    t.string "package", default: "trial"
    t.datetime "installed_at"
    t.string "email"
    t.string "domain"
    t.string "notification_email"
    t.integer "source_limit", default: 5
    t.string "api_token"
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.inet "current_sign_in_ip"
    t.inet "last_sign_in_ip"
    t.string "provider"
    t.string "customer_support_email"
    t.text "email_subscriptions", default: "---\n- sync_failure\n- sync_success\n- newsletter\n"
    t.integer "credit", default: 3000
    t.boolean "use_credit", default: true
    t.string "timezone", default: "Eastern Time (US & Canada)"
    t.float "schedule_min_hour", default: 6.0
    t.integer "user_id"
    t.integer "ecwid_store_id"
    t.string "ecwid_token"
    t.bigint "primary_location_id"
    t.string "ecwid_store_url"
    t.string "bigcommerce_store_hash"
    t.string "bigcommerce_client_id"
    t.string "bigcommerce_access_token"
    t.string "bigcommerce_domain"
    t.string "email_notification_start_time"
    t.string "email_notification_end_time"
    t.boolean "email_notification_custom_time_enabled", default: false
    t.string "country_name"
    t.string "city"
    t.string "shopify_plan_name"
    t.integer "low_credit_alert", default: 20
    t.datetime "uninstalled_at"
    t.string "paypal_access_token"
    t.string "paypal_refresh_token"
    t.string "paypal_client_id"
    t.string "paypal_client_secret"
    t.string "stripe_customer_id"
    t.string "stripe_subscription_id"
    t.integer "failure_count", default: 0
    t.index ["api_token"], name: "index_shops_on_api_token", unique: true
    t.index ["bigcommerce_store_hash"], name: "index_shops_on_bigcommerce_store_hash"
    t.index ["reset_password_token"], name: "index_shops_on_reset_password_token", unique: true
    t.index ["shopify_domain"], name: "index_shops_on_shopify_domain", unique: true
    t.index ["user_id"], name: "index_shops_on_user_id"
  end

  create_table "sources", id: :serial, force: :cascade do |t|
    t.integer "shop_id"
    t.string "name"
    t.string "source_host"
    t.string "source_login"
    t.string "source_parent_path"
    t.string "source_file_name"
    t.string "schedule_type"
    t.string "schedule_time"
    t.string "status"
    t.datetime "last_processing_time"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.integer "order_no_mapping"
    t.integer "sku_mapping"
    t.integer "quantity_mapping"
    t.integer "tracking_no_mapping"
    t.integer "tracking_company_mapping"
    t.string "source_process"
    t.string "source_rename"
    t.boolean "has_header", default: false
    t.string "sync_status"
    t.datetime "next_schedule_time"
    t.float "schedule_interval", default: 6.0
    t.string "encrypted_source_password"
    t.string "order_key", default: "order_number"
    t.text "ssh_key"
    t.string "source_type", default: "file_upload"
    t.integer "tracking_url_mapping"
    t.integer "ignore_key"
    t.string "ignore_value"
    t.boolean "ignore_empty_sku", default: true
    t.string "source_url"
    t.string "column_separator", default: ","
    t.string "source_file"
    t.string "find_val"
    t.boolean "notify_customer", default: true
    t.string "order_identifier_constants"
    t.string "email"
    t.string "tracking_company_default"
    t.boolean "allow_blank_tracking_no", default: true
    t.string "shopify_order_key_constants"
    t.string "financial_status", default: "paid,partially_paid,partially_refunded"
    t.string "path_to_file"
    t.string "ftp_mode", default: "passive"
    t.string "platform", default: "shopify"
    t.string "ecwid_fulfillment_status"
    t.string "ecwid_payment_status", default: "PAID,AWAITING_PAYMENT,PARTIALLY_REFUNDED,REFUNDED,CANCELLED"
    t.string "parent_node", default: ""
    t.string "after_fulfilled_order_financial_status"
    t.string "location_id"
    t.string "location_name"
    t.string "row_separator"
    t.string "tracking_url_default"
    t.string "fulfillment_status", default: "unshipped,partial"
    t.string "order_status", default: "any"
    t.integer "order_days_ago", default: 90
    t.string "bigcommerce_order_status", default: "11"
    t.integer "progress", default: 0
    t.string "file_encoding"
    t.string "column_ranges"
    t.string "source_file_host"
    t.integer "template_id"
    t.boolean "auto_detect_tracking", default: true
    t.string "google_sheet_name", default: "Sheet1"
    t.boolean "tag_enabled", default: false
    t.string "tag_value", default: "fulfillsync-ok"
    t.string "schedule_day"
    t.string "shipment_status", default: "delivered"
    t.string "premapping_keys"
    t.boolean "only_selected_location", default: true
    t.string "sku_prefix"
    t.string "exclude_inventory_management"
    t.string "line_item_identifier", default: "sku"
    t.jsonb "tracking_company_converter", default: {}
    t.integer "location_mapping"
    t.jsonb "connection_settings", default: {}, null: false
    t.integer "shipment_status_mapping"
    t.jsonb "shipment_status_converter", default: {}
    t.index ["shop_id"], name: "index_sources_on_shop_id"
  end

  create_table "sync_logs", id: :serial, force: :cascade do |t|
    t.integer "source_id"
    t.string "status"
    t.datetime "processed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "caller"
    t.string "error_message"
    t.integer "shop_id"
    t.integer "number_fulfillment_updated"
    t.integer "total_source_row"
    t.integer "total_process_orders", default: 0
    t.index ["shop_id", "source_id", "created_at"], name: "index_sync_logs_on_shop_id_and_source_id_and_created_at"
    t.index ["shop_id", "source_id"], name: "index_sync_logs_on_shop_id_and_source_id"
    t.index ["shop_id"], name: "index_sync_logs_on_shop_id"
    t.index ["source_id", "id"], name: "idx_source_sort"
    t.index ["source_id"], name: "index_sync_logs_on_source_id"
  end

  create_table "users", id: :serial, force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.inet "current_sign_in_ip"
    t.inet "last_sign_in_ip"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "shop_id"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  add_foreign_key "file_replacements", "sources"
  add_foreign_key "sync_logs", "sources"
end

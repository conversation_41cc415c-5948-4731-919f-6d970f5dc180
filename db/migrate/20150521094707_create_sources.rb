

class CreateSources < ActiveRecord::Migration
  def change
    create_table :sources do |t|
      t.belongs_to :shop
      t.string :name
      t.string :type
      t.string :source_host
      t.string :source_login
      t.string :source_password
      t.string :source_parent_path
      t.string :source_file_name
      t.string :source_job_type
      t.string :source_job_time
      t.string :status
      t.datetime :last_processing_time

      t.timestamps
    end
  end
end

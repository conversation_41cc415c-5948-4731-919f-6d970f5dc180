

class AddApiTokenToShop < ActiveRecord::Migration[5.0]
  def up
    add_column :shops, :api_token, :string
    add_index :shops, :api_token, unique: true

    Shop.all.each do |shop|
      shop.regenerate_api_token
      if shop.save
        puts "API Token for shop id = #{shop.id} generated successfully."
      else
        puts "API Token for shop id = #{shop.id} failed to generate."
      end
    end
  end

  def down
    remove_column :shops, :api_token
  end
end

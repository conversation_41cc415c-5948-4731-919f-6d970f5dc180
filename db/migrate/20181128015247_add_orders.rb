

class AddOrders < ActiveRecord::Migration[5.0]
  def change
    create_table :order_backups do |t|
      t.bigint :shopify_order_id
      t.string :shipping_province
      t.string :shipping_province_code
      t.string :shipping_latitude
      t.string :shipping_longitude
      t.string :shipping_country_code
      t.string :shipping_zip
      t.string :shipping_city
      t.decimal :total_price, precision: 25, scale: 3
      t.decimal :total_weight, precision: 25, scale: 3
      t.string :variant_name
      t.string :vendor
      t.bigint :shopify_variant_id
      t.string :sku
      t.bigint :shop_id
      t.timestamps
    end
  end
end

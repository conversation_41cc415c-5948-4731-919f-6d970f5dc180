

class AddPathToFile < ActiveRecord::Migration[5.0]
  def change
    add_column :sources, :path_to_file, :string
    Source.all.each do |source|
      path_separator = '/'
      if source.source_parent_path && (source.source_parent_path.split('\\').length > 1)
        path_separator = '\\'
      end
      file_name = source.source_file_name
      path = source.source_parent_path

      if source.source_file_name&.start_with?(path_separator)
        file_name = source.source_file_name[1..source.source_file_name.length - 1]
      end
      if source.source_parent_path&.end_with?(path_separator)
        path = source.source_parent_path[0..source.source_parent_path.length - 2]
      end
      if source.source_parent_path&.end_with?('/')
        path = source.source_parent_path[0..source.source_parent_path.length - 2]
      end
      unless file_name.blank?
        source.path_to_file = "#{path.blank? || path == path_separator ? path : "#{path}#{path_separator}"}#{file_name}"
        source.save
      end
    end; nil
    #   source.path_to_file = "#{path ? "#{path}/" : ""}#{file_name}"
    # end
  end
end

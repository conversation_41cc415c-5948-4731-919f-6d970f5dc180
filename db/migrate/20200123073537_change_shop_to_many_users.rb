

class ChangeShopToManyUsers < ActiveRecord::Migration[5.2]
  def change
    begin
      add_column :users, :shop_id, :integer
    rescue StandardError => e
    end
    begin
      Shop.all.each do |shop|
        user = shop.user
        if user
          user.shop_id = shop.id
          user.save
        else
          puts shop.id
        end
      end
    rescue StandardError => e
      ActiveRecord::Base.connection.execute 'ROLLBACK'
    end
  end
end

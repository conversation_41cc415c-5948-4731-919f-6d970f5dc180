# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "335c70f6d282e75f29a1c0800dcd20c4"
name = "syncX: Fulfill Tracking"
handle = "syncx-fulfill-tracking"
application_url = "https://app.fulfillsync.com/"
embedded = false

[webhooks]
api_version = "2021-10"

  [[webhooks.subscriptions]]
  uri = "/customer_data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "/customer_redact"
  compliance_topics = [ "customers/redact" ]

  [[webhooks.subscriptions]]
  uri = "/shop_redact"
  compliance_topics = [ "shop/redact" ]

[auth]
redirect_urls = [
  "https://fulfillsync.com/auth/shopify/callback",
  "http://fulfillsync.com/auth/shopify/callback",
  "https://uptracker.app/auth/shopify/callback",
  "http://uptracker.app/auth/shopify/callback",
  "https://app.fulfillsync.com/auth/shopify/callback",
  "http://app.fulfillsync.com/auth/shopify/callback"
]

[pos]
embedded = false

[build]
include_config_on_deploy = true

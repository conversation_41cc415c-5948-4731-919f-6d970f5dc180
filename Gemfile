source "https://rubygems.org"

ruby "3.3.6"

git_source(:github) do |repo_name|
  repo_name = "#{repo_name}/#{repo_name}" unless repo_name.include?("/")
  "https://github.com/#{repo_name}.git"
end

gem "rails", "********"
gem "pg", "~> 1.1"
gem "puma"
gem "redis"
gem "graphql"
gem "repost"
gem "ed25519"
gem "bcrypt_pbkdf"
gem "shopify-money", require: "money"
gem "offsite_payments"
gem "ecwid_api"
gem "pretender"
gem "bigcommerce", "~> 1.0", git: "**************:syncx-org/bigcommerce-api-ruby.git"
gem "bootsnap", require: false

# Shopify API and Authentication
gem "shopify_app"
gem "shopify_api"
gem "omniauth-shopify-oauth2"
gem "stripe"

# Assets and JavaScript
gem "sass-rails"
gem "coffee-rails"
gem "font-awesome-sass"
gem "uglifier", ">= 1.0.3"
gem "bootstrap-sass"
gem "momentjs-rails", ">= 2.9.0"
gem "bootstrap3-datetimepicker-rails", "~> 4.7.14"
gem "bootstrap-switch-rails"
gem "vite_rails"

# Error Logging
gem "airbrake", "10.0.5"

# Rails Plugins
gem "state_machines-activerecord"
gem "attr_encrypted"
gem "delayed_job_active_record"
gem "daemons"
gem "net-sftp"
gem "net-ftp"
gem "carrierwave"
gem "double-bag-ftps"
gem "roo"
gem "roo-xls"
gem "migration_data"
gem "nokogiri"
gem "faraday"
gem "google-api-client"
gem "paypal-sdk-permissions"

# Configurations
gem "decent_exposure"
gem "kaminari"
gem "config", "~> 2.2.1"
gem "rails_same_site_cookie"

# Active Admin
gem "devise", "4.9.4"
gem "activeadmin", "~> 3.2.2"
gem "activeadmin_json_editor", "~> 0.0.7"
gem "certified"

gem "whenever", require: false

# AWS
gem "aws-sdk-dynamodb"
gem "aws-sdk-apigatewaymanagementapi"
gem "aws-sdk-secretsmanager", "~> 1.0", require: false

# Email Processor
gem "griddler-mailgun"

# Chartings
gem "highcharts-rails", "~> 4.0.0"

gem "httparty"
gem "activeresource"
gem "net-scp"
gem "mini_portile2", "~> 2.8", ">= 2.8.1"
gem "tzinfo-data"
gem "ffi", "< 1.17.0"

### DO NOT REMOVE THESE GEMS ###
gem "psych", "< 4" # break when running YAML.load (paypal gem)
gem "concurrent-ruby", "1.3.4"

group :development do
  gem "graphiql-rails"
  gem "rb-readline"
  gem "listen", ">= 3.0.5", "< 3.2"
  gem "standard"
  # Use Capistrano for deployment
  gem "capistrano"
  gem "capistrano-rvm"
  gem "capistrano-nvm", require: false
  gem "capistrano-bundler"
  gem "capistrano-rails"
  gem "capistrano-maintenance", "~> 1.0", require: false
  gem "capistrano-rails-console"
  gem "capistrano-passenger"
  gem "slackistrano"

  # For debugger
  gem "debug", "1.8", require: false
  gem "rdbg", require: false
end

group :development, :test do
  gem "dotenv"
  gem "pry-rails"
  gem "byebug"

  gem "rspec-rails"
  gem "factory_bot_rails", "~> 4.0"
  gem "faker"
end

group :test do
  gem "capybara"
  gem "database_cleaner"
  gem "launchy"
  gem "selenium-webdriver"
  gem "timecop"
  gem "shopify-mock"
  gem "simplecov", require: false
  gem "fakeweb", github: "chrisk/fakeweb"
end

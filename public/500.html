<!doctype html>
<html>
  <head>
    <title>Something Went Wrong</title>
    <link
      href="//fonts.googleapis.com/css?family=Dosis:"
      rel="stylesheet"
      type="text/css"
    />
    <link rel="icon" href="/fs_favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <style>
      .container {
        text-align: center;
        padding: 20px;
      }
      .navbar .brand {
        float: none;
      }
      .error_btn {
        background-color: #0facf3;
        border-color: #0facf3;
        border-radius: 6px;
        color: #fff;
        padding: 10px 16px;
        border: 1px solid #0facf3;
        font-size: 15px;
        cursor: pointer;
        margin-top: 25px;
      }
      .error_btn:hover {
        -webkit-box-shadow: 0 2px 10px rgba(15, 172, 243, 0.4);
        box-shadow: 0 2px 10px rgba(15, 172, 243, 0.4);
      }
      body {
        font-family: 'Inter', 'Open Sans', sans-serif;
        color: #535353;
        font-weight: 300;
        font-size: 0.9375rem;
        line-height: 1.9;
      }
      h2 {
        font-family: 'Inter', 'Open Sans', sans-serif;
        color: #37404d;
        letter-spacing: 1px;
        line-height: 1.6;
        font-size: 1.6rem;
      }
      .section-header hr {
        width: 50px;
        margin-bottom: 1.5rem;
        border-color: rgba(83, 83, 83, 0.07);
      }
      small {
        color: #b5b9bf;
      }
      .lead {
        font-size: 1.04rem;
      }
      img {
        max-width: 300px;
        width: 100%;
      }
    </style>
  </head>

  <body>
    <div class="container">
      <header class="section-header">
        <img
          src="/FulfillSyncLogo.png"
          max-height="50px"
          alt="FulfillSync logo"
        />
        <h2>Server Error</h2>
        <hr />
        <p class="lead">This site is currently down at the moment.</p>
        <p class="lead">
          We're aware of this problem. Grab a snack and we'll be back before you
          know it.
        </p>
        <p class="lead">
          If you are still having trouble, please clear the cookies on your
          browser and try again.
        </p>
      </header>

      <p>
        <a href="#">
          <button
            class="error_btn"
            type="button"
            value="back"
            onClick="window.history.back();"
            class="btn btn-primary"
          >
            <i class="icon-chevron-left" style="opacity: 0.25"></i> Back to the
            app
          </button>
        </a>
      </p>
    </div>
  </body>
</html>

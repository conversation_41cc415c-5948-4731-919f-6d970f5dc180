<!doctype html>
<html>
  <head>
    <title>The page has been moved.</title>
    <link
      href="//fonts.googleapis.com/css?family=Dosis:"
      rel="stylesheet"
      type="text/css"
    />
    <link rel="icon" href="/fs_favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <style>
      .container {
        text-align: center;
        padding: 20px;
      }
      .error_btn {
        background-color: #0facf3;
        border-color: #0facf3;
        border-radius: 6px;
        color: #fff;
        padding: 10px 16px;
        border: 1px solid #0facf3;
        font-size: 15px;
        cursor: pointer;
      }
      .error_btn:hover {
        -webkit-box-shadow: 0 2px 10px rgba(15, 172, 243, 0.4);
        box-shadow: 0 2px 10px rgba(15, 172, 243, 0.4);
      }
      body {
        font-family: 'Inter', 'Open Sans', sans-serif;
        color: #535353;
        font-weight: 300;
        font-size: 0.9375rem;
        line-height: 1.9;
      }
      .section-header small {
        display: inline-block;
        font-size: 0.6875rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 1rem;
        opacity: 0.6;
      }
      h2 {
        font-family: 'Inter', 'Open Sans', sans-serif;
        color: #37404d;
        letter-spacing: 1px;
        line-height: 1.6;
        font-size: 1.6rem;
      }
      .section-header hr {
        width: 50px;
        margin-bottom: 1.5rem;
        border-color: rgba(83, 83, 83, 0.07);
      }
      small {
        color: #b5b9bf;
      }
      .lead {
        font-size: 1.04rem;
      }
      img {
        max-width: 300px;
        width: 100%;
      }
    </style>
  </head>

  <body>
    <div class="container">
      <header class="section-header">
        <img src="/FS_logo.png" max-height="50px" alt="FulfillSync logo" />
        <br />
        <img
          style="width: 500px"
          src="https://cdn.stock-sync.com/images/error_page.svg"
        />
        <h2>The page has been moved.</h2>
        <hr />
        <p class="lead">
          Oops! The page you were looking doesn’t seem to exist anymore.
        </p>
      </header>

      <p>
        <a href="#"
          ><button
            class="error_btn"
            type="button"
            value="back"
            onClick="window.history.back();"
            class="btn btn-primary"
          >
            <i class="icon-chevron-left" style="opacity: 0.25"></i> Back to the
            app
          </button></a
        >
      </p>
    </div>
  </body>
</html>

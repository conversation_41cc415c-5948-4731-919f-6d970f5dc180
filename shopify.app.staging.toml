# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "e2c1f5728a8a090476e4d1baaea56ba8"
name = "X - FulfillSync Dev 2 (Public)"
application_url = "https://staging.fulfillsync.com"
embedded = false
handle = "x-fulfillsync-dev-2-public"

[webhooks]
api_version = "2023-01"

[auth]
redirect_urls = [
  "https://private.stock-sync.com/auth/shopify/callback",
  "http://localhost:3000/auth/shopify/callback",
  "https://localhost:3000/auth/shopify/callback",
  "https://private.cloudcoder.com.my/auth/shopify/callback",
  "https://32ff81320505.ngrok.io/auth/shopify/callback",
  "https://geo-embed-test.syncx.com/auth/shopify/callback",
  "https://instructional-legend-establish-promo.trycloudflare.com/auth/shopify/callback",
  "https://staging.fulfillsync.com/auth/shopify/callback"
]

[pos]
embedded = false

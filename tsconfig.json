{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "baseUrl": "./app/javascript", "esModuleInterop": true, "incremental": true, "isolatedModules": true, "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noEmit": true, "noImplicitAny": false, "noUnusedLocals": true, "noUnusedParameters": true, "paths": {"@/*": ["./*"]}, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ESNext", "types": ["vite/client"], "verbatimModuleSyntax": true}, "include": ["app/javascript"]}
# https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners#codeowners-syntax
# one pattern, followed by one or more @username or @org/team-name 
# ...not multiple patterns on one line

* @syncx-org/syncx-backend-team
.vscode/ @syncx-org/syncx-tech-team
README.md @syncx-org/syncx-tech-team
lefthook.* @syncx-org/syncx-tech-team
app/assets/stylesheets/ @syncx-org/syncx-tech-team
public/ @syncx-org/syncx-tech-team
.github/workflows/*.yml @syncx-org/syncx-tech-team
*.cjs @syncx-org/syncx-ui-team
*.js @syncx-org/syncx-ui-team
*.ts @syncx-org/syncx-ui-team
.npmrc @syncx-org/syncx-ui-team
.prettier* @syncx-org/syncx-ui-team
package.json @syncx-org/syncx-ui-team
pnpm-lock.yaml @syncx-org/syncx-ui-team
tsconfig.json @syncx-org/syncx-ui-team
vite.json @syncx-org/syncx-ui-team
app/javascript/ @syncx-org/syncx-ui-team
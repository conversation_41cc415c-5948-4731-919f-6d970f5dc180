module Arguments
  class SourceInput < ::Types::BaseInputObject
    description "Properties for creating a Source"

    argument :id, String, required: false
    argument :name, String, required: false
    argument :source_file, Types::FileType, required: false
    argument :source_host, String, required: false
    argument :source_type, String, required: false
    argument :path_to_file, String, required: false
    argument :ssh_key, String, required: false
    argument :source_url, String, required: false
    argument :source_process, String, required: false
    argument :source_rename, String, required: false
    argument :order_key, String, required: false
    argument :order_no_mapping, String, required: false
    argument :order_identifier_constants, String, required: false
    argument :shopify_order_key_constants, String, required: false
    argument :sku_mapping, String, required: false
    argument :quantity_mapping, String, required: false
    argument :tracking_no_mapping, String, required: false
    argument :tracking_url_mapping, String, required: false
    argument :allow_blank_tracking_no, <PERSON><PERSON><PERSON>, required: false
    argument :tracking_company_mapping, String, required: false
    argument :tracking_company_default, String, required: false
    argument :financial_status, String, required: false
    argument :ignore_key, String, required: false
    argument :ignore_value, String, required: false
    argument :ignore_empty_sku, Bo<PERSON>an, required: false
    argument :notify_customer, <PERSON><PERSON><PERSON>, required: false
    argument :source_password, String, required: false
    argument :source_login, String, required: false
    argument :email, String, required: false
    argument :schedule_type, String, required: false
    argument :schedule_time, String, required: false
    argument :sync_status, String, required: false
    argument :schedule_interval, Float, required: false
    argument :ftp_mode, String, required: false
    argument :column_separator, String, required: false
    argument :platform, String, required: false
    argument :ecwid_payment_status, String, required: false
    argument :ecwid_fulfillment_status, String, required: false
    argument :parent_node, String, required: false
    argument :after_fulfilled_order_financial_status, String, required: false
    argument :location_id, String, required: false
    argument :location_name, String, required: false
    argument :tracking_url_default, String, required: false
    argument :bigcommerce_order_status, String, required: false
    argument :order_days_ago, String, required: false
    argument :auto_detect_tracking, Boolean, required: false
    argument :google_sheet_name, String, required: false
    argument :tag_enabled, Boolean, required: false
    argument :tag_value, String, required: false
    argument :schedule_day, String, required: false
    argument :sku_prefix, String, required: false
    argument :exclude_inventory_management, String, required: false
    argument :line_item_identifier, String, required: false
    argument :location_mapping, String, required: false
    argument :file_replacements, [GraphQL::Types::JSON], required: false
  end
end

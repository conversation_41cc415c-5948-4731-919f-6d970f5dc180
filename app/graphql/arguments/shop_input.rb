module Arguments
  class ShopInput < ::Types::BaseInputObject
    description "Properties for creating a Shop"

    argument :id, String, required: false
    argument :notification_email, String, required: false
    argument :low_credit_alert, String, required: false
    argument :email_subscriptions, [String], required: false
    argument :timezone, String, required: false
    argument :bigcommerce_store_hash, String, required: false
    argument :bigcommerce_client_id, String, required: false
    argument :bigcommerce_access_token, String, required: false
    argument :email_notification_start_time, String, required: false
    argument :email_notification_end_time, String, required: false
    argument :email_notification_custom_time_enabled, Boolean, required: false
    argument :paypal_client_id, String, required: false
    argument :paypal_client_secret, String, required: false
  end
end

module Types
  class SyncLogType < BaseObject
    description "SyncLog"

    field :id, ID, null: false
    field :status, String, null: true
    field :processed_at, GraphQL::Types::ISO8601DateTime, null: true
    field :created_at, GraphQL::Types::ISO8601DateTime, null: true
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: true
    field :caller, String, null: true
    field :error_message, String, null: true
    field :number_fulfillment_updated, Int, null: true
    field :source, SourceType, null: true

    def source
      object.source
    end
  end
end

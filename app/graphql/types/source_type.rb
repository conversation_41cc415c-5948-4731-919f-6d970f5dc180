module Types
  class SourceType < BaseObject
    description "Source"

    field :id, ID, null: false
    field :name, String, null: false
    field :shop_id, Int, null: true
    field :source_host, String, null: true
    field :source_login, String, null: true
    field :path_to_file, String, null: true
    field :schedule_type, String, null: false
    field :schedule_time, String, null: true
    field :status, String, null: true
    field :last_processing_time, GraphQL::Types::ISO8601DateTime, null: true
    field :created_at, GraphQL::Types::ISO8601DateTime, null: true
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: true
    field :order_no_mapping, String, null: false
    field :sku_mapping, String, null: true
    field :quantity_mapping, String, null: true
    field :tracking_no_mapping, String, null: true
    field :tracking_company_mapping, String, null: true
    field :source_process, String, null: true
    field :source_rename, String, null: true
    field :has_header, Boolean, null: true
    field :sync_status, String, null: true
    field :next_schedule_time, GraphQL::Types::ISO8601DateTime, null: true
    field :schedule_interval, Float, null: true
    field :encrypted_source_password, String, null: true
    field :order_key, String, null: true
    field :ssh_key, String, null: true
    field :source_type, String, null: true
    field :tracking_url_mapping, String, null: true
    field :ignore_key, String, null: true
    field :ignore_value, String, null: true
    field :ignore_empty_sku, Boolean, null: true
    field :source_url, String, null: true
    field :column_separator, String, null: true
    field :source_file, FileType, null: true
    field :find_val, String, null: true
    field :notify_customer, Boolean, null: true
    field :email, String, null: true
    field :order_identifier_constants, String, null: true
    field :tracking_company_default, String, null: true
    field :allow_blank_tracking_no, Boolean, null: true
    field :shopify_order_key_constants, String, null: true
    field :financial_status, String, null: true
    field :ftp_mode, String, null: true
    field :parent_node, String, null: true
    field :after_fulfilled_order_financial_status, String, null: true
    field :shipment_status, String, null: true
    field :exclude_inventory_management, String, null: true
    field :sync_log, SyncLogType, null: true
    field :source_password, String, null: true
    field :column_mapping_data, TestColumnMappingType, null: true
    field :platform, String, null: true
    field :ecwid_payment_status, String, null: true
    field :ecwid_fulfillment_status, String, null: true
    field :location_id, String, null: true
    field :location_name, String, null: true
    field :tracking_url_default, String, null: true
    field :bigcommerce_order_status, String, null: true
    field :progress, Int, null: true
    field :order_days_ago, String, null: true
    field :template_id, Int, null: true
    field :auto_detect_tracking, Boolean, null: true
    field :google_sheet_name, String, null: true
    field :tag_enabled, Boolean, null: true
    field :tag_value, String, null: true
    field :schedule_day, String, null: true
    field :sku_prefix, String, null: true
    field :line_item_identifier, String, null: true
    field :location_mapping, String, null: true
    field :file_replacements, [GraphQL::Types::JSON], null: false

    def sync_log
      object.sync_logs.last
    end

    def column_mapping_data
      object.test_column_mapping
    end
  end
end

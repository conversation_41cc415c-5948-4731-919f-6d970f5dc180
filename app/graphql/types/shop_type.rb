module Types
  class ShopType < BaseObject
    description "Shop"

    field :id, ID, null: false
    field :shopify_domain, String, null: true
    field :shopify_token, String, null: true
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    field :charge_id, String, null: true
    field :charge_at, GraphQL::Types::ISO8601DateTime, null: true
    field :package, String, null: true
    field :installed_at, GraphQL::Types::ISO8601DateTime, null: false
    field :email, String, null: false
    field :message, String, null: true
    field :domain, String, null: false
    field :notification_email, String, null: true
    field :low_credit_alert, String, null: true
    field :email_subscriptions, [String], null: true
    field :source_limit, Int, null: false
    field :api_token, String, null: false
    field :provider, String, null: false
    field :sources, [SourceType], null: true
    field :expired_day, Int, null: true
    field :timezone, String, null: true
    field :use_credit, Boolean, null: true
    field :credit, Int, null: true
    field :schedule_min_hour, Int, null: true
    field :locations, [LocationType], null: true
    field :ecwid_store_id, Int, null: true
    field :ecwid_store_url, String, null: true
    field :store_domain, String, null: true
    field :bigcommerce_store_hash, String, null: true
    field :bigcommerce_client_id, String, null: true
    field :bigcommerce_access_token, String, null: true
    field :connected, Boolean, null: true
    field :platform, String, null: true
    field :email_notification_start_time, String, null: true
    field :email_notification_end_time, String, null: true
    field :email_notification_custom_time_enabled, Boolean, null: true
    field :paypal_client_id, String, null: true
    field :paypal_client_secret, String, null: true

    def sources
      object.sources
    end

    def locations
      object.get_locations
    end
  end
end

module Types
  class FulfillmentLogType < BaseObject
    description "FulfillmentLog"

    field :id, ID, null: false
    field :shopify_order_id, GraphQL::Types::BigInt, null: true
    field :tracking_no, String, null: true
    field :created_at, GraphQL::Types::ISO8601DateTime, null: true
    field :order_number, String, null: true
    field :tracking_company, String, null: true
    field :sku, String, null: true
    field :quantity, Int, null: true
    field :fulfillment_status, String, null: true
    field :error_message, String, null: true
    field :shopify_fulfillment_id, GraphQL::Types::BigInt, null: true
    field :page, Int, null: true
    field :bigcommerce_shipment_id, Int, null: true
    field :paypal_transaction, String, null: true
    field :paypal_success, String, null: true
    field :paypal_order, PaypalOrderType, null: true

    def paypal_transaction
      object.paypal_transaction || nil
    end

    def paypal_success
      object.paypal_success || nil
    end

    def paypal_order
      object.paypal_order || nil
    end
  end
end

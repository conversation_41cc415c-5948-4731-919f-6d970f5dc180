module Types
  class MutationType < Types::BaseObject
    field :create_source, mutation: Mutations::CreateSource
    field :source, mutation: Mutations::Source
    field :edit_shop, mutation: Mutations::EditShop
    field :upload_file, mutation: Mutations::UploadFile
    field :delete_source, mutation: Mutations::DeleteSource
    field :make_a_copy, mutation: Mutations::MakeACopy
    field :create_ticket, mutation: Mutations::CreateTicket
    field :cancel_fulfillment, mutation: Mutations::CancelFulfillment
    field :copy_source, mutation: Mutations::CopySource
    field :create_stripe_checkout, mutation: Mutations::CreateStripeCheckout
  end
end

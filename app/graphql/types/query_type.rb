module Types
  class QueryType < Types::BaseObject
    field :current_user, UserType, null: false
    def current_user
      context[:current_user]
    end

    field :current_shop, ShopType, null: false
    def current_shop
      return GraphQL::ExecutionError.new("Unauthenticated") if context[:current_shop].blank?
      context[:current_shop]
    end

    field :sources, [SourceType, null: false], null: false
    def sources
      context[:current_shop].sources.order("created_at desc")
    end

    field :templates, [ShopifyTemplateType], null: false
    def templates
      Source::ShopifyTemplate.order("name asc")
    end

    field :sync_logs, [SyncLogType], null: false
    def sync_logs
      context[:current_shop].sync_logs.includes(:source).order("id desc").limit(10)
    end

    field :source, SourceType, null: false do
      argument :id, ID, required: true
    end
    def source(id:)
      context[:current_shop].sources.find(id)
    end

    field :fulfillment_logs, [FulfillmentLogType], null: true do
      argument :page, Int, required: false
      argument :filters, [Arguments::FulfillmentFiltersInput], required: true
    end
    def fulfillment_logs(page:, filters:)
      logs = context[:current_shop].fulfillment_logs

      filters.each do |filter|
        if filter[:column] == "cancel_fulfilled_order"
          logs = logs.where("to_char(created_at, 'DD/MM/YYYY') like :search", search: "%#{filter[:term].downcase}%")
        elsif filter[:column] == "error"
          if filter[:term] == "yes"
            logs = logs.where("error_message <> ''")
          end
        else
          logs = logs.where("#{filter[:column]} like :search", search: "%#{filter[:term]}%")
        end
      end

      logs.order("id desc").page(page)
    end

    field :checkout, PaymentType, null: false do
      argument :nonce, String, required: false
      argument :credit, Int, required: false
    end
    def checkout(nonce:, credit:)
      result = context[:current_shop].send_payment(nonce: nonce, credit: credit)
      OpenStruct.new(result)
    end

    field :source_sync_logs, [SyncLogType], null: false do
      argument :id, ID, required: true
    end
    def source_sync_logs(id:)
      source = Source.where(id: id, shop_id: context[:current_shop].id).first
      source.sync_logs.order("id desc").limit(10)
    end

    field :source_guided_mapping, ::GraphQL::Types::JSON, null: false do
      argument :id, ID, required: true
    end
    def source_guided_mapping(id:)
      source = Source.where(id: id, shop_id: context[:current_shop].id).first
      data = source.guided_mapping
      if data[:status]
        {"data" => data[:data]}
      else
        {"data" => []}
      end
    end

    field :shop_paypal_order, [FulfillmentLogType], null: false do
      argument :page, Int, required: false
    end
    def shop_paypal_order(page:)
      logs = FulfillmentLog.paypal_fulfillments(context[:current_shop])
      logs.order("id desc").page(page)
    end
  end
end

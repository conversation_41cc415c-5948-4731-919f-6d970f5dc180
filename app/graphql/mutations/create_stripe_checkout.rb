module Mutations
  class CreateStripeCheckout < BaseMutation
    argument :price, String, required: true
    argument :credit, Int, required: true

    type Types::StripeObjectType

    def resolve(price:, credit:)
      stripe = nil

      begin
        total_credits = credit
        charge_price = price

        return {success: false, message: "Fulfillment must be greater than 199"} unless total_credits >= 200

        shop = current_shop
        charge_price_in_cents = (100 * charge_price.to_r).to_i
        stripe = begin
          session = Stripe::Checkout::Session.create(
            success_url: "#{Settings.app_domain}?payment_success=true",
            cancel_url: "#{Settings.app_domain}?payment_success=false",
            payment_method_types: ["card"],
            mode: "payment",
            metadata: {
              platform: shop.provider,
              store_id: shop.id,
              total_credits: total_credits,
              api_token: shop.api_token
            },
            line_items: [{
              price_data: {
                currency: "USD",
                product_data: {
                  name: "Buy FulfillSync Credit",
                  description: "#{total_credits} Credits"
                },
                unit_amount: charge_price_in_cents
              },
              quantity: 1
            }]
          )

          {success: true, session_url: session.url}
        rescue => e
          {success: false, message: e.error}
        end
      rescue => _
        stripe = {}
      end

      stripe
    end
  end
end

module Mutations
  class CancelFulfillment < BaseMutation
    argument :id, ID, required: false

    type Types::FulfillmentLogType

    def resolve(id:)
      fulfillment = nil
      begin
        shop = current_shop
        fulfillment = shop.fulfillment_logs.find(id)
        result = shop.cancel_fulfillment(fulfillment.shopify_order_id, fulfillment.shopify_fulfillment_id)
        fulfillment.update(fulfillment_status: "cancelled") if result
      rescue => _
        fulfillment = {}
      end
      fulfillment
    end
  end
end

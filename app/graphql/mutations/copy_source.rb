module Mutations
  class CopySource < BaseMutation
    argument :id, ID, required: false

    type Types::SourceType

    def resolve(id:)
      source = nil
      begin
        shop = current_shop
        source = shop.sources.find(id)

        if shop.sources.count < shop.source_limit
          source_attr = source.attributes.except("id", "created_at", "updated_at", "email")
          new_source = ::Source.new(source_attr)
          new_source.name = "Copy of #{source.name}"

          new_source.sync_status = "paused"
          new_source.status = ""
          new_source.assign_source_email
          new_source.save
          source = new_source
        else
          raise GraphQL::ExecutionError.new("You have reached your source limit.")
        end
      rescue GraphQL::ExecutionError => e
        raise e
      rescue => _
        source = nil
      end
      source
    end
  end
end

module Mutations
  class Source < BaseMutation
    argument :source_input, Arguments::SourceInput, required: false

    type Types::SourceType

    def resolve(source_input:)
      source = nil
      begin
        source_input = source_input.to_h
        puts source_input
        source = current_shop.sources.find(source_input[:id])

        if source_input[:file_replacements]
          source_input[:file_replacements_attributes] = source_input.delete(:file_replacements).each do |file_replacement|
            file_replacement.permit!
          end
        end

        source.update(source_input)

        if source_input[:sync_status]
          is_active = source_input[:sync_status] == "started"
          source.update_sync_status(!is_active)
        end
      rescue
        source = {}
      end
      source
    end
  end
end

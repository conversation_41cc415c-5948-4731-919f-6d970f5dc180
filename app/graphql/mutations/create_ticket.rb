module Mutations
  class CreateTicket < BaseMutation
    argument :shopify_domain, String, required: false
    argument :subject, String, required: false
    argument :email, String, required: false
    argument :message, String, required: false

    type Types::TicketType

    def resolve(shopify_domain: nil, subject: nil, email: nil, message: nil)
      ticket = nil

      begin
        freshdesk_api = FreshdeskApi.new
        result = freshdesk_api.create_ticket(shopify_domain, subject, message, email)
        ticket = if result.present?
          {
            success: true
          }
        else
          {
            success: false
          }
        end
      rescue => _
        ticket = {}
      end
      ticket
    end
  end
end

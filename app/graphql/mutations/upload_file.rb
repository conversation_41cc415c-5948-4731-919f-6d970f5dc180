module Mutations
  class UploadFile < BaseMutation
    argument :id, ID, required: false
    argument :source_file, type: Types::FileType, required: false
    argument :update_mode, <PERSON><PERSON>an, required: false
    argument :cancel_sync_now, <PERSON><PERSON><PERSON>, required: false

    type Types::SourceType

    def resolve(id:, source_file: nil, update_mode: nil, cancel_sync_now: nil)
      source = nil
      begin
        source = current_shop.sources.find(id)
        shop = current_shop
        if !shop.active?
          raise GraphQL::ExecutionError.new("Your trial period has ended. Please upgrade to use this feature.")
        else
          if source_file
            # Regex to detect multiple extensions (e.g., dump.php.csv)
            regex_multi_extensions = /\.[a-z]+\.[a-z]+$/i
            file_name = source_file.original_filename

            if regex_multi_extensions.match?(file_name)
              raise GraphQL::ExecutionError.new("File #{file_name} has multiple extensions.")
            end

            unix_mime = `file --mime-type -b #{source_file.tempfile.path}`.chomp
            Rails.logger.info "Unix mime type : #{unix_mime}, source inspect: #{source_file.inspect}"
            if Settings.accepted_mime.include?(unix_mime)
              source.update(source_file: source_file, source_file_host: Socket.gethostname)
            else
              raise GraphQL::ExecutionError.new("File #{source_file&.original_filename} is invalid for processing.")
            end
          end
          if cancel_sync_now
            job = Delayed::Job.where(queue: "update_fulfillments").where("handler like '%id: #{source.id}\n%'").first
            job&.destroy
            source.status = ""
            source.save
          else
            source.set_queuing

            if update_mode
              source.delay(queue: "update_fulfillments").sync_fulfillments(:ui_all, true)
            else
              source.delay(queue: "update_fulfillments", priority: 0).sync_fulfillments(:ui)
            end
          end
        end
      rescue GraphQL::ExecutionError => e
        raise e
      rescue => _
        source = {}
      end
      source
    end
  end
end

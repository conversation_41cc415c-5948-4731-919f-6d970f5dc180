module Mutations
  class CreateSource < BaseMutation
    argument :shop_id, ID, required: true

    type Types::SourceType, null: true

    def resolve(shop_id:)
      source = nil
      begin
        shop = current_shop

        if shop.sources.count < shop.source_limit
          source = shop.sources.new
          source.assign_defaults
          source.assign_source_email
          source.save(validate: false)
        else
          raise GraphQL::ExecutionError.new("You have reached your source limit.")
        end
      rescue GraphQL::ExecutionError => e
        raise e
      rescue => _
        source = {}
      end
      source
    end
  end
end

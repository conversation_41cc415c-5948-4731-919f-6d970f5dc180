class FulfillmentWorker
  attr_reader :source, :shop

  def initialize(source)
    @source = source
    @worker_class = "FulfillmentWorker::#{source.platform.camelize}".constantize
    @worker = @worker_class.new(source)
  end

  def call(*args)
    @worker.call(*args)
  end

  def debug_orders(*args)
    @worker.debug_orders(*args)
  end

  def list_orders(*args)
    @worker.list_orders(*args)
  end

  def get_order_identifier(*args)
    @worker.get_order_identifier(*args)
  end
end

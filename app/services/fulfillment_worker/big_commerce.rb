class FulfillmentWorker::BigCommerce
  DEFAULT_FULFILLMENT_STATUS_FILTER = [11].freeze
  DEFAULT_ORDER_LIMIT = 250
  attr_accessor :source, :shop

  def initialize(source)
    @source = source
    @shop = source.shop
    @update_mode = false

    Bigcommerce.configure do |config|
      config.store_hash = @shop.bigcommerce_store_hash
      config.client_id = @shop.bigcommerce_client_id
      config.access_token = @shop.bigcommerce_access_token
    end
  end

  def call(trigger = :undefined, update_mode = false)
    if source.status == "running"
      source.sync_logs.create(shop: shop, status: "running", error_message: "There is already a process running", processed_at: Time.now, caller: trigger)
      return {status: "running", fulfillments: [], sync_log: []}
    end

    @update_mode = update_mode

    source.update(status: "running")
    fulfillments = []
    data = download_file(build_file_reader)
    source_order_hash = source_data(data)
    unless source_order_hash[:status]
      source.update(status: "")
      begin
        process_file
      rescue => e
        # do nothing
      end
      source.sync_logs.create(shop: shop, status: "retrieve_failed", error_message: source_order_hash[:remark], processed_at: Time.now, caller: trigger)
      return {status: "retrieve_failed", error: source_order_hash[:remark]}
    end

    source_order_hash = source_order_hash[:data]
    if source_order_hash.blank?
      source.update(status: "")
      begin
        process_file
      rescue => e
        # do nothing
      end
      source.sync_logs.create(shop: shop, status: "no_data", error_message: "No data available to sync. Please check your Test Column Mapping.", processed_at: Time.now, caller: trigger)
      return {status: "no_data", error: "No data available to sync. Please check your Test Column Mapping."}
    end

    partial = false
    sync_log = source.sync_logs.create(shop: shop, status: "running", error_message: "This process is running", caller: trigger, processed_at: Time.now)
    order_keys = source_order_hash.keys
    orders = get_orders
    orders.each do |order|
      order_number = order.id.to_s
      next unless order_keys.include?(order_number)

      source_items = source_order_hash[order_number]
      source_item = source_items.first # only one tracking number for now
      next if source_item && source_item[:tracking_no].blank? && !source.allow_blank_tracking_no

      if shop.use_credit && (shop.credit <= 0)
        partial = true
        break
      end

      order_products = get_order_products(order.id)
      shipment_items = construct_shipment_items(order_products)

      shipments = []
      shipment_items.each do |order_address_id, shipment_item|
        tries = 2
        begin
          order_shipments = shop.order_shipment(order.id)
          if @update_mode && order_shipments
            shop.update_shipment(order_shipments, tracking_number)
          else
            shipment = Bigcommerce::Shipment.create(
              order.id,
              tracking_number: source_item[:tracking_no],
              shipping_provider: source_item[:tracking_company],
              order_address_id: order_address_id,
              items: shipment_item
            )
          end

          shop.update_order_status(order.id) unless shipment.tracking_number.blank?

          shipments << shipment

          Rails.logger.info "update success"
          _log = source.fulfillment_logs.where(order_number: order_number, bigcommerce_shipment_id: shipment.id).first_or_initialize.tap do |fulfillment_log|
            fulfillment_log.error_message = ""
            fulfillment_log.tracking_no = shipment.tracking_number
            fulfillment_log.tracking_company = shipment.shipping_provider
            fulfillment_log.fulfillment_status = true
            fulfillment_log.shop_id = @shop.id
            fulfillment_log.sync_log_id = sync_log.id
            fulfillment_log.shopify_fulfillment_id = shipment.id
            fulfillment_log.save
          end

          fulfillment = {
            order_number: order_number,
            tracking_number: shipment.tracking_number,
            tracking_company: shipment.shipping_provider
          }

          fulfillments << fulfillment
        rescue Bigcommerce::BadRequest => e
          if /shipping_provider/.match?(e.response_headers.first[:details][:invalid_reason])
            tries -= 1
            if tries > 0
              source_item[:tracking_company] = ""
              retry
            else
              raise e
            end
          else
            _log = source.fulfillment_logs.where(order_number: order_number).first_or_initialize.tap do |fulfillment_log|
              fulfillment_log.error_message = e.response_headers.first[:details][:invalid_reason]
              fulfillment_log.tracking_no = source_item[:tracking_no]
              fulfillment_log.fulfillment_status = false
              fulfillment_log.shop_id = @shop.id
              fulfillment_log.sync_log_id = sync_log.id
              fulfillment_log.save
            end
          end
        end
      rescue => e
        puts "Error creating shipment for Source #{source.id}: #{e.class} #{e.message}"
      end

      next if shipments.blank?

      if shop.use_credit
        shop.decrement!(:credit, 1) if shop.credit > 0
      end
    end

    begin
      file_name = source.need_after_process? ? process_file : get_file_name
    rescue => _
    end

    if partial
      sync_log.update(status: "success", error_message: "You are out of credit. Only #{fulfillments.count} fulfillments updated.", processed_at: Time.now, caller: trigger, number_fulfillment_updated: fulfillments.count, total_source_row: source_order_hash.count)
      UserMailer.notify_out_of_credit(shop, source).deliver_later
    else
      sync_log.update(status: "success", error_message: "Processed #{if file_name.is_a?(Array)
                                                                       "#{file_name.count} files (#{file_name.first(3).join(",")}#{(file_name.count > 3) ? "..." : ""})"
                                                                     else
                                                                       file_name
                                                                     end}", processed_at: Time.now, caller: trigger, number_fulfillment_updated: fulfillments.count, total_source_row: source_order_hash.count)

      if shop.use_credit && (shop.credit <= shop.low_credit_alert)
        UserMailer.notify_low_credit(shop).deliver_later
      end
    end
    source.update(status: "")
    {status: "success", fulfillments: fulfillments, sync_log: sync_log}
  rescue => e
    Rails.logger.info e.message
    Rails.logger.info e.backtrace.join("\n")
    source.update(status: "")
    Airbrake.notify(e, {source: source})
    sync_log&.update(status: "error", processed_at: Time.now, caller: trigger, error_message: "error: #{e.message}")
    {status: "error", error: e.message, sync_log: sync_log}
  end

  def debug_orders(all_orders = false)
    data = download_file(build_file_reader)
    source_order_hash = source_data(data)
    unless source_order_hash[:status]
      return {status: "retrieve_failed", error: source_order_hash[:remark]}
    end
    if source_order_hash[:data].blank?
      return {status: "no_data", error: "No data available to sync. Please check your Test Column Mapping."}
    end

    source_order_hash = source_order_hash[:data]

    @update_mode = all_orders == "true"
    orders = get_orders
    unless orders.present?
      return {status: "no_data", error: "No New Bigcommerce orders available to sync"}
    end

    line_items_hash = {}
    order_keys = source_order_hash.keys
    orders.each do |order|
      order_number = order.id.to_s
      next unless order_keys.include?(order_number)
      line_items_hash[order_number] = order
    end

    {status: "success", line_items_hash: line_items_hash}
  rescue
  end

  def list_orders
    get_orders
  end

  def get_orders
    orders = []
    if order_filters[:status_id].present?
      order_filters[:status_id].each do |status_id|
        list = Bigcommerce::Order.all(status_id: status_id, limit: DEFAULT_ORDER_LIMIT)
        orders << list
        page = 1
        Rails.logger.debug "page #{page}"
        while list.length == DEFAULT_ORDER_LIMIT
          page += 1
          Rails.logger.debug "page #{page}"
          list = Bigcommerce::Order.all(status_id: status_id, limit: DEFAULT_ORDER_LIMIT, page: page)
          orders << list
        end
      end
    else
      orders << (list = Bigcommerce::Order.all(limit: DEFAULT_ORDER_LIMIT))
      page = 1
      Rails.logger.debug "page #{page}"
      while list.length == DEFAULT_ORDER_LIMIT
        page += 1
        Rails.logger.debug "page #{page}"
        list = Bigcommerce::Order.all(limit: DEFAULT_ORDER_LIMIT, page: page)
        orders << list
      end
    end
    orders = orders.flatten
  end

  def get_order_products(order_id)
    Bigcommerce::OrderProduct.all(order_id)
  end

  private

  def download_file(file_reader)
    source.download_file(file_reader)
  end

  def build_file_reader
    @reader ||= begin
      source_config = {
        file: source.source_file,
        url: source.source_url,
        host: source.source_host,
        login: source.source_login,
        password: source.source_password,
        path_to_file: source.path_to_file,
        ftp_mode: source.ftp_mode,
        process_type: source.source_process,
        file_rename: source.source_rename,
        ssh_key: source.ssh_key,
        email_logs: source.get_email_log,
        timezone: source.shop.timezone,
        encoding: source.file_encoding,
        source_file_host: source.source_file_host,
        google_sheet_name: source.google_sheet_name
      }
      FileReaderFactory.get_file_reader(source.source_type.to_sym, source_config)
    end
  end

  def process_file
    file_reader = build_file_reader
    file_reader.process_file
  end

  def get_file_name
    file_reader = build_file_reader
    file_reader.final_file_name
  end

  def source_data(orders)
    ignore_key = source.ignore_key
    ignore_value = source.ignore_value
    order_no_mapping = source.order_no_mapping
    tracking_no_mapping = source.tracking_no_mapping
    tracking_company_mapping = source.tracking_company_mapping

    begin
      source_order_hash = {}
      unless orders[:status] && orders[:data].present?
        return {status: false, data: {}, remark: "No data retrieved from source."}
      end

      orders = orders[:data]
      orders.each do |order|
        skip_value = nil
        if ignore_key.present? && order[ignore_key].present? && ignore_value.present?
          operator = ignore_value.split(/(<|>)/).reject(&:blank?)
          next if operator.length == 1 && order[ignore_key] == ignore_value

          operator, skip_value = operator
          case operator
          when ">"
            next if order[ignore_key] > skip_value
          when "<"
            next if order[ignore_key] < skip_value
          end
        end
        next unless order[order_no_mapping].present?

        order_no = order[order_no_mapping].to_s
        tracking_no = (tracking_no_mapping.present? && order[tracking_no_mapping].present?) ? order[tracking_no_mapping].strip : ""
        tracking_company = (tracking_company_mapping.present? && order[tracking_company_mapping].present?) ? order[tracking_company_mapping].strip : ""
        source_order_hash[order_no] = [] if source_order_hash[order_no].nil?
        source_order_hash[order_no] << {order_no: order_no, tracking_no: tracking_no.to_s, tracking_company: tracking_company.to_s}
      end
      {status: true, data: source_order_hash}
    rescue => e
      Rails.logger.info e.message
      Rails.logger.info e.backtrace.join("\n")
      Airbrake.notify(e, {source: source})
      {status: false, data: {}, remark: e.message}
    end
  end

  def order_filters
    filters = {}

    unless @update_mode
      filters[:status_id] = if source.bigcommerce_order_status
        source.bigcommerce_order_status.split(",")
      else
        DEFAULT_FULFILLMENT_STATUS_FILTER
      end
    end

    filters
  end

  def construct_shipment_items(order_products)
    shipment_items = {}

    order_products.each do |order_product|
      if shipment_items[order_product.order_address_id].nil?
        shipment_items[order_product.order_address_id] = []
      end
      shipment_items[order_product.order_address_id] << {order_product_id: order_product.id, quantity: order_product.quantity}
    end

    shipment_items
  end
end

class FulfillmentWorker::Ecwid
  DEFAULT_FULFILLMENT_STATUS_FILTER = %w[AWAITING_PROCESSING PROCESSING].join(",")

  attr_accessor :source, :shop

  def initialize(source)
    @source = source
    @shop = source.shop
    @update_mode = false
  end

  def call(trigger = :undefined, update_mode = false)
    sync_log = source.sync_logs.create(shop: shop, status: "running", error_message: "This process is running", caller: trigger, processed_at: Time.now)
    if source.status == "running"
      return {status: "running", fulfillments: [], sync_log: []}
    end

    @update_mode = update_mode

    source.update(status: "running")
    fulfillments = []
    data = download_file(build_file_reader)
    source_order_hash = source_data(data)
    unless source_order_hash[:status]
      source.update(status: "")
      begin
        process_file
      rescue => e
        # do nothing
        Airbrake.notify(e, {source: source})
      end
      source.sync_logs.create(shop: shop, status: "retrieve_failed", error_message: source_order_hash[:remark], processed_at: Time.now, caller: trigger)
      return {status: "retrieve_failed", error: source_order_hash[:remark]}
    end

    source_order_hash = source_order_hash[:data]
    if source_order_hash.blank?
      source.update(status: "")
      begin
        process_file
      rescue => e
        # do nothing
        Airbrake.notify(e, {source: source})
      end
      source.sync_logs.create(shop: shop, status: "no_data", error_message: "No data available to sync. Please check your Test Column Mapping.", processed_at: Time.now, caller: trigger)
      return {status: "no_data", error: "No data available to sync. Please check your Test Column Mapping."}
    end

    partial = false

    order_keys = source_order_hash.keys
    orders = get_orders
    orders.each do |ecwid_order|
      order_number = ecwid_order.id
      next unless order_keys.include?(order_number)

      source_items = source_order_hash[order_number]
      source_item = source_items.first # only one tracking number for now
      next if source_items.first[:tracking_no].blank?

      if shop.use_credit && (shop.credit <= 0)
        partial = true
        break
      end

      ecwid_order.tracking_number = source_item[:tracking_no]
      ecwid_order.fulfillment_status = :shipped
      result = ecwid_order.save
      next unless result.body["updateCount"] == 1

      Rails.logger.info "update success"
      _log = source.fulfillment_logs.where(order_number: order_number).first_or_initialize.tap do |fulfillment_log|
        fulfillment_log.tracking_no = ecwid_order.tracking_number
        fulfillment_log.shop_id = shop.id
        fulfillment_log.fulfillment_status = "true"
        fulfillment_log.sync_log = sync_log
        fulfillment_log.save
      end

      fulfillment = {
        order_number: order_number,
        tracking_number: ecwid_order.tracking_number
      }

      fulfillments << fulfillment
      if shop.use_credit
        shop.decrement!(:credit, 1) if shop.credit > 0
      end
    end

    begin
      file_name = source.need_after_process? ? process_file : get_file_name
    rescue => _
      # do nothing
    end

    if partial
      sync_log.update(status: "success", error_message: "You are out of credit. Only #{fulfillments.count} fulfillments updated.", processed_at: Time.now, caller: trigger, number_fulfillment_updated: fulfillments.count, total_source_row: source_order_hash.count)
      UserMailer.notify_out_of_credit(shop, source).deliver_later
    else
      sync_log.update(status: "success", error_message: "Processed #{file_name.is_a?(Array) ? "#{file_name.length} files" : file_name}", processed_at: Time.now, caller: trigger, number_fulfillment_updated: fulfillments.count, total_source_row: source_order_hash.count)

      if shop.use_credit && (shop.credit <= shop.low_credit_alert)
        UserMailer.notify_low_credit(shop).deliver_later
      end
    end
    source.update(status: "")
    {status: "success", fulfillments: fulfillments, sync_log: sync_log}
  rescue => e
    Rails.logger.info e.message
    Rails.logger.info e.backtrace.join("\n")
    Airbrake.notify(e, {source: source})
    source.update(status: "")
    sync_log = source.sync_logs.create(shop: shop, status: "error", processed_at: Time.now, caller: trigger, error_message: "error: #{e.message}")
    {status: "error", error: e.message, sync_log: sync_log}
  end

  def debug_orders(all_orders = false)
    data = download_file(build_file_reader)
    source_order_hash = source_data(data)
    unless source_order_hash[:status]
      return {status: "retrieve_failed", error: source_order_hash[:remark]}
    end
    if source_order_hash[:data].blank?
      return {status: "no_data", error: "No data available to sync. Please check your Test Column Mapping."}
    end

    source_order_hash = source_order_hash[:data]

    @update_mode = all_orders == "true"
    orders = get_orders
    unless orders.present?
      return {status: "no_data", error: "No New Ecwid orders available to sync"}
    end

    line_items_hash = {}
    order_keys = source_order_hash.keys
    orders.each do |order|
      order_number = order.id
      next unless order_keys.include?(order_number)
      line_items_hash[order_number] = order.items
    end

    {status: "success", line_items_hash: line_items_hash}
  rescue => e
    Airbrake.notify(e, {source: source})
  end

  def list_orders
    get_orders
  end

  def get_orders
    client.orders.all(order_filters)
  end

  private

  def client
    EcwidApi::Client.new(shop.ecwid_store_id, shop.ecwid_token)
  end

  def download_file(file_reader)
    source.download_file(file_reader)
  end

  def build_file_reader
    @reader ||= begin
      source_config = {
        file: source.source_file,
        url: source.source_url,
        host: source.source_host,
        login: source.source_login,
        password: source.source_password,
        path_to_file: source.path_to_file,
        ftp_mode: source.ftp_mode,
        process_type: source.source_process,
        file_rename: source.source_rename,
        ssh_key: source.ssh_key,
        email_logs: source.get_email_log,
        timezone: source.shop.timezone,
        encoding: source.file_encoding,
        source_file_host: source.source_file_host,
        google_sheet_name: source.google_sheet_name
      }
      FileReaderFactory.get_file_reader(source.source_type.to_sym, source_config)
    end
  end

  def process_file
    file_reader = build_file_reader
    file_reader.process_file
  end

  def get_file_name
    file_reader = build_file_reader
    file_reader.final_file_name
  end

  def source_data(orders)
    ignore_key = source.ignore_key
    ignore_value = source.ignore_value
    order_no_mapping = source.order_no_mapping
    tracking_no_mapping = source.tracking_no_mapping

    begin
      source_order_hash = {}
      unless orders[:status] && orders[:data].present?
        return {status: false, data: {}, remark: "No data retrieved from source."}
      end

      orders = orders[:data]
      orders.each do |order|
        skip_value = nil
        if ignore_key.present? && order[ignore_key].present? && ignore_value.present?
          operator = ignore_value.split(/(<|>)/).reject(&:blank?)
          next if operator.length == 1 && order[ignore_key] == ignore_value

          operator, skip_value = operator
          case operator
          when ">"
            next if order[ignore_key] > skip_value
          when "<"
            next if order[ignore_key] < skip_value
          end
        end
        next unless order[order_no_mapping].present?

        order_no = order[order_no_mapping]
        tracking_no = (tracking_no_mapping.present? && order[tracking_no_mapping].present?) ? order[tracking_no_mapping].strip : ""
        source_order_hash[order_no] = [] if source_order_hash[order_no].nil?
        source_order_hash[order_no] << {order_no: order_no, tracking_no: tracking_no.to_s}
      end
      {status: true, data: source_order_hash}
    rescue => e
      Rails.logger.info e.message
      Rails.logger.info e.backtrace.join("\n")
      Airbrake.notify(e, {source: source})
      {status: false, data: {}, remark: e.message}
    end
  end

  def order_filters
    filters = {}

    if source.ecwid_payment_status
      filters[:paymentStatus] = source.ecwid_payment_status
    end

    if source.ecwid_fulfillment_status
      filters[:fulfillmentStatus] = source.ecwid_fulfillment_status
    elsif !@update_mode
      filters[:fulfillmentStatus] = DEFAULT_FULFILLMENT_STATUS_FILTER
    end

    filters
  end
end

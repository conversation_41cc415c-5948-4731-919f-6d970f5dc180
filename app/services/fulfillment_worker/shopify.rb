class FulfillmentWorker::Shopify
  attr_reader :source, :shop

  def initialize(source)
    @source = source
    @shop = source.shop
    @update_mode = false
    @regex = nil
    if @source.order_identifier_constants.present?
      @regex = Regexp.new(source.order_identifier_constants)
    end
  end

  def download_file(file_reader)
    source.download_file(file_reader)
  end

  def get_order_identifier(order_no)
    order_no = order_no.to_s
    order_no = order_no.gsub(@regex, "") if @regex
    order_no.strip
  end

  def source_data(orders)
    ignore_key = source.ignore_key
    ignore_value = source.ignore_value
    sku_mapping = source.sku_mapping
    order_no_mapping = source.order_no_mapping
    quantity_mapping = source.quantity_mapping
    tracking_no_mapping = source.tracking_no_mapping
    tracking_company_mapping = source.tracking_company_mapping
    tracking_url_mapping = source.tracking_url_mapping
    tracking_company_default = source.tracking_company_default
    tracking_url_default = source.tracking_url_default
    auto_detect_from_shopify = source.auto_detect_tracking
    location_mapping = source.location_mapping
    shipment_status_mapping = source.shipment_status_mapping
    shipment_status_converter = source.shipment_status_converter

    begin
      source_order_hash = {}
      unless orders[:status] && orders[:data].present?
        return {status: false, data: {}, remark: orders[:remark]}
      end

      tracking_company_replacements = source.file_replacements.where(column: "tracking_company_mapping")
      tracking_url_replacements = source.file_replacements.where(column: "tracking_url_mapping")
      orders = orders[:data]
      orders.each do |order|
        order_no = order[order_no_mapping]
        skip_value = nil
        if ignore_key.present? && order[ignore_key].present? && ignore_value.present?
          operator = ignore_value.split(/(<|>)/).reject(&:blank?)
          next if operator.length == 1 && order[ignore_key] == ignore_value

          operator, skip_value = operator
          case operator
          when ">"
            next if order[ignore_key] > skip_value
          when "<"
            next if order[ignore_key] < skip_value
          end
        end
        next unless order_no.present?

        order_no = get_order_identifier(order_no)
        sku = (sku_mapping.present? && order[sku_mapping].present?) ? strip_val(order[sku_mapping]) : nil
        sku = sku.gsub(/#{source.sku_prefix}/, "") unless sku.blank?
        quantity = (quantity_mapping.present? && order[quantity_mapping].present?) ? strip_val(order[quantity_mapping]) : nil
        location = (location_mapping.present? && order[location_mapping].present?) ? order[location_mapping] : source.location_name

        if tracking_no_mapping.present?
          tracking_no_mapping.to_s.split(",").each do |tracking_index_no|
            tracking_index_no = tracking_index_no.to_i
            tracking_no = order[tracking_index_no].present? ? strip_val(order[tracking_index_no]) : ""
            tracking_company = if auto_detect_from_shopify
              nil
            else
              (if tracking_company_mapping.present? && order[tracking_company_mapping].present?
                 strip_val(order[tracking_company_mapping])
               else
                 (tracking_company_default.blank? ? nil : tracking_company_default)
               end)
            end
            original_tracking_company = tracking_company
            tracking_company_replacements.each do |tracking_company_replacement|
              new_tracking_company = tracking_company_replacement.replace(tracking_company)
              if tracking_company != new_tracking_company
                tracking_company = new_tracking_company
                break
              end
            end

            tracking_url = if auto_detect_from_shopify
              nil
            else
              (if tracking_url_mapping.present? && order[tracking_url_mapping].present?
                 strip_val(order[tracking_url_mapping])
               else
                 new_url = (tracking_url_default.blank? ? nil : tracking_url_default.gsub("<tracking_no>", tracking_no))
                 new_url
               end)
            end
            # valid URL
            if tracking_url
              tracking_url = tracking_url.strip

              if /[\/\w]#[\/\w]/.match?(tracking_url)
                # do nothing
                # to avoid escape url like this https://t.17track.net/en#nums=4202728492612903396156000040927394&amp;fc=190094
              elsif tracking_url.include? "tools.usps.com"
              # ignore for now no escape
              else
                unless /\A#{URI::DEFAULT_PARSER}\z/o.match?(tracking_url.downcase)
                  p = URI::DEFAULT_PARSER

                  tracking_url = p.escape(tracking_url)
                  unless /\A#{URI::DEFAULT_PARSER.make_regexp(["http", "https"])}\z/.match?(tracking_url.downcase)
                    tracking_url = "https://#{tracking_url.strip}"
                    unless /\A#{URI::DEFAULT_PARSER.make_regexp(["http", "https"])}\z/.match?(tracking_url.downcase)
                      tracking_url = ""
                    end
                  end
                end
              end
            end

            # original_tracking_url = tracking_url
            tracking_url_replacements.each do |tracking_url_replacement|
              new_tracking_url = tracking_url_replacement.replace(tracking_url)
              if tracking_url != new_tracking_url
                tracking_url = new_tracking_url
                break
              end
            end

            curr_shipment_status = nil

            if !shipment_status_converter.empty? && !shipment_status_mapping.blank? && order[shipment_status_mapping] && order[shipment_status_mapping] != ""
              curr_shipment_status = shipment_status_converter[order[shipment_status_mapping]]
            end

            source_order_hash[order_no] = [] if source_order_hash[order_no].nil?
            source_order_hash[order_no] << {order_no: order_no, sku: sku, qty: quantity, tracking_no: tracking_no, tracking_company: tracking_company, tracking_url: tracking_url, original_tracking_company: original_tracking_company, location: location, shipment_status: curr_shipment_status}
          end
        else
          tracking_company = if auto_detect_from_shopify
            nil
          else
            (if tracking_company_mapping.present? && order[tracking_company_mapping].present?
               strip_val(order[tracking_company_mapping])
             else
               (tracking_company_default.blank? ? nil : tracking_company_default)
             end)
          end
          original_tracking_company = tracking_company
          tracking_company_replacements.each do |tracking_company_replacement|
            new_tracking_company = tracking_company_replacement.replace(tracking_company)
            if tracking_company != new_tracking_company
              tracking_company = new_tracking_company
              break
            end
          end
          tracking_url = if auto_detect_from_shopify
            nil
          else
            (if tracking_url_mapping.present? && order[tracking_url_mapping].present?
               strip_val(order[tracking_url_mapping])
             else
               (tracking_url_default.blank? ? nil : tracking_url_default)
             end)
          end

          source_order_hash[order_no] = [] if source_order_hash[order_no].nil?
          source_order_hash[order_no] << {order_no: order_no, sku: sku, qty: quantity, tracking_no: nil, tracking_company: tracking_company, tracking_url: tracking_url, original_tracking_company: original_tracking_company}
        end
      end
      {status: true, data: source_order_hash}
    rescue => e
      Rails.logger.info e.message
      Rails.logger.info e.backtrace.join("\n")
      Airbrake.notify(e, {source: source})
      {status: false, data: {}, remark: e.message}
    end
  end

  def build_file_reader
    @reader ||= begin
      source_config = {
        file: source.source_file,
        url: source.source_url,
        host: source.source_host,
        login: source.source_login,
        password: source.source_password,
        path_to_file: source.path_to_file,
        ftp_mode: source.ftp_mode,
        process_type: source.source_process,
        file_rename: source.source_rename,
        ssh_key: source.ssh_key,
        email_logs: source.get_email_log,
        timezone: source.shop.timezone,
        encoding: source.file_encoding,
        source_file_host: source.source_file_host,
        google_sheet_name: source.google_sheet_name
      }
      FileReaderFactory.get_file_reader(source.source_type.to_sym, source_config)
    end
  end

  def process_file
    file_reader = build_file_reader
    file_reader.process_file
  end

  def get_file_name
    file_reader = build_file_reader
    file_reader.final_file_name
  end

  def order_fulfillment_items(source_items, shopify_order_items, refunds, only_selected_location)
    sku_mapping = source.sku_mapping
    ignore_empty_sku = source.ignore_empty_sku?
    check_fulfilled = !(@update_mode || source.fulfillment_status == "any")
    by_fulfillment_services = {}
    line_items = {}
    log_items = {}

    existing_items = {}

    # loop through each source wrt its order to get the line items to be fulfilled
    source_items.each do |src_itm|
      shopify_order_items.each do |order_itm|
        if check_fulfilled
          next if order_itm["fulfillment_status"] == "fulfilled"
        end

        next if source.exclude_inventory_management == "blank" && order_itm["variant_inventory_management"].blank?

        skip_this = false
        refunds.each do |refund|
          refund.refund_line_items.each do |refund_line_item|
            if order_itm["id"].present? && (order_itm["id"] == refund_line_item["line_item_id"])
              if order_itm["quantity"] > refund_line_item["quantity"]
                # means refund partial can fulfilled
              else
                skip_this = true
              end
            end
          end
        end
        next if skip_this

        # order_itm is order line items from shopify, not a variant

        line_item_identifier = order_itm.fetch(@source.line_item_identifier)
        order_item_id = order_itm["id"]
        quantity = sku = nil

        if src_itm[:sku].blank? || !line_item_identifier.present?
          quantity = if sku_mapping.present? && ignore_empty_sku
            0
          else
            ((order_itm["fulfillable_quantity"] == 0) ? order_itm["quantity"] : order_itm["fulfillable_quantity"])
          end

        elsif line_item_identifier.present? && line_item_identifier.to_s.upcase == src_itm[:sku].to_s.upcase
          quantity = if !source.allow_blank_tracking_no && @source.tracking_no_mapping.present? && src_itm[:tracking_no].blank?
            0
          elsif src_itm[:qty].present?
            source_qty = src_itm[:qty].to_i
            order_itm_qty = if check_fulfilled
              order_itm["fulfillable_quantity"]
            else
              order_itm["quantity"]
            end
            if source_qty > order_itm_qty
              return_qty = order_itm_qty
            elsif (item = line_items[order_item_id])
              # to handle multiple lines with same SKU in the same feed
              return_qty = item[:quantity] + source_qty
              if return_qty > order_itm_qty
                return_qty = order_itm_qty
              end
            else
              return_qty = source_qty
            end
            return_qty
          else
            order_itm["fulfillable_quantity"]
          end
          sku = line_item_identifier
        else
          quantity = 0
        end

        next unless quantity > 0

        unless by_fulfillment_services[order_itm["fulfillment_service"]]
          by_fulfillment_services[order_itm["fulfillment_service"]] = []
        end
        if existing_items[order_item_id]
          by_fulfillment_services[order_itm["fulfillment_service"]].each do |i|
            if i[:id] == order_item_id
              i[:quantity] = quantity
            end
          end
        else
          existing_items[order_item_id] = {id: order_item_id, quantity: quantity}
          by_fulfillment_services[order_itm["fulfillment_service"]] << existing_items[order_item_id]
        end

        line_items[order_item_id] = {id: order_item_id, quantity: quantity}
        log_items[order_item_id] = {id: order_item_id, quantity: quantity, sku: sku, total: (order_itm["price"].to_f * order_itm["quantity"])}
      end
    end
    {line_items: by_fulfillment_services, log_items: log_items.values}
  end

  def call(trigger = :undefined, update_mode = false)
    @update_mode = update_mode
    allow_blank_tracking_no = source.allow_blank_tracking_no
    notify_customer = source.notify_customer
    fulfillment_logs = source.fulfillment_logs
    fulfillments = 0
    fulfillment_list = []
    success_ids_list = []
    begin
      source.shop.get_shop_information
    rescue ActiveResource::UnauthorizedAccess => e
      if source.sync_logs.where(status: "running").order("created_at").count > 3
        source.sync_status = "paused"
        source.save
        return {status: "error", error: e.message, sync_log: nil}
      end
    rescue ShopifyAPI::Errors::HttpResponseError => e
      if (e.message =~ /^Unavailable Shop.*/).present? || (e.message =~ /^Not Found.*/).present?
        source.sync_status = "paused"
        source.save
        return {status: "error", error: e.message, sync_log: nil}
      else
        raise e
      end
    end
    sync_log = source.sync_logs.create(shop: shop, status: "running", error_message: "This process is running", caller: trigger, processed_at: Time.now)
    if source.location_id
      locations = shop.get_locations
      if locations.blank?
        Airbrake.notify("No location - #{source.id}")
        return {status: "error", error: "error on shop API"}
      else
        unless locations.collect(&:id).include?(source.location_id.to_i)
          source.location_id = nil
          source.save
        end
      end
    end
    begin
      if source.status != "running"
        source.update({status: "running", progress: 0})
        if shop.use_credit && (shop.credit <= 0)
          remark = "You are out of credit."
          sync_log.update(status: "out_of_credit", error_message: remark, processed_at: Time.now)
          source.update(status: "")
          UserMailer.notify_out_of_credit(shop, source).deliver_later
          return {status: "out_of_credit", error: remark}
        end

        data = download_file(build_file_reader)
        Rails.logger.debug("Load data done #{data[:status]}")
        Rails.logger.debug("Total rows #{data[:data].count}")
        source_order_hash = source_data(data)

        Rails.logger.info source_order_hash[:data].count
        source.set_progress(:progress_source)
        sync_log.update(total_source_row: source_order_hash[:data].present? ? source_order_hash[:data].count : -1)
        unless source_order_hash[:status]
          if data[:data].count > 0
            begin
              process_file if source.need_after_process?
            rescue => _
              # do nothing
            end
          end
          sync_log.update(status: "retrieve_failed", error_message: source_order_hash[:remark], processed_at: Time.now)
          source.update(status: "")
          return {status: "retrieve_failed", error: source_order_hash[:remark]}
        end
        unless source_order_hash[:data].present?
          if data[:data].count > 0
            begin
              process_file if source.need_after_process?
            rescue => _
              # do nothing
            end
          end
          remark = "No data available to sync. Please check your Test Column Mapping."
          sync_log.update(status: "no_data", error_message: remark, processed_at: Time.now)
          source.update(status: "")
          return {status: "no_data", error: remark}
        end
        source_order_hash = source_order_hash[:data]
        shop.create_session

        order_keys = source_order_hash.keys
        order_hash, total_orders = OrderFetcher.new(source: source, update_mode: update_mode).call(order_keys)
        Rails.logger.debug("Fetch Shopify orders total: #{total_orders}")
        Rails.logger.debug("Matching Shopify orders total: #{order_hash.count}")
        source.set_progress(:progress_orders)
        unless order_hash.present?
          if data[:data].count > 0
            begin
              process_file if source.need_after_process?
            rescue => _
              # do nothing
            end
          end
          remark = "No match found from #{total_orders} Shopify orders#{data[:file_name] ? " (#{data[:file_name]})" : ""}"
          sync_log.update(status: "no_data", error_message: remark, processed_at: Time.now, total_process_orders: total_orders)
          source.update(status: "")
          return {status: "no_data", error: remark}
        end

        partial = false

        fulfill_locations = {}
        fulfill_locations["manual"] = source.location_id.blank? ? @shop.primary_location_id : source.location_id
        fulfill_locations["g"] = fulfill_locations["manual"]

        shop.get_locations.each do |location|
          fulfill_locations[location.name.parameterize] = location.id
          fulfill_locations[location.name.parameterize.underscore] = location.id
        end
        source_order_hash.keys.each_with_index do |order_no, idx|
          Rails.logger.debug "#{idx}: #{order_no}"
          source.set_progress(idx, sync_log.total_source_row)
          order_no = order_no.strip if order_no
          source_items = source_order_hash[order_no]

          if !allow_blank_tracking_no && @source.tracking_no_mapping.present? && source_items.collect { |a| a[:tracking_no] }.count { |a| !a.blank? } == 0
            next
          end

          order = order_hash[order_no]
          next unless order

          if shop.use_credit && (shop.credit <= 0)
            partial = true
            break
          end

          next unless order

          # Code not in used
          # tracking_urls = source_items.collect { |a| a[:tracking_url] }.reject(&:blank?)
          # if order.has_attributes
          #   tracking_urls = tracking_urls.collect { |tu| tu.gsub("<country_code>", order.shipping_address.country_code).gsub("<zip_code>", order.shipping_address.country_code) }
          # end

          fulfillment_status = {success: false, fulfillment: nil, error_message: nil}
          by_fulfillment_services, log_items = order_fulfillment_items(source_items, order.line_items, order.refunds, source.only_selected_location).values
          # handle line items fulfillment service correctly
          Rails.logger.debug(by_fulfillment_services)
          Rails.logger.debug(log_items)
          fos = @shop.get_fulfillment_order(order.id)

          boolean = false
          if boolean
            # no longer needed
            if update_mode || (source.fulfillment_status == "any")

              if source_items.first[:sku]
                fulfill_line_items = []
                fos.each do |fo|
                  fo.line_items.each do |li|
                    source_item = line_items.reject { |a| a[:id] != li["line_item_id"] }.first
                    if source_item
                      fulfill_line_items << {id: "gid://shopify/FulfillmentOrderLineItem/#{li["id"]}", quantity: source_item[:quantity]}
                    end
                  end
                  fulfillment = {
                    trackingInfo: {
                      company: source_items.first[:tracking_company],
                      numbers: source_items.collect { |a| a[:tracking_no].to_s.split(",") }.flatten.uniq,
                      urls: source_items.collect { |a| a[:tracking_url] }.reject(&:blank?)
                    },
                    lineItemsByFulfillmentOrder: [{fulfillmentOrderId: "gid://shopify/FulfillmentOrder/#{fo.id}", fulfillmentOrderLineItems: fulfill_line_items}]
                  }
                  Rails.logger.debug("Fulfillment: #{fulfillment}")
                end
              else
                fo = fos.reject { |a| !["open", "in_progress"].include?(a.status) }.first
                fo ||= fos.first

                fulfillment = {
                  trackingInfo: {
                    company: source_items.first[:tracking_company],
                    numbers: source_items.collect { |a| a[:tracking_no].to_s.split(",") }.flatten.uniq,
                    urls: source_items.collect { |a| a[:tracking_url] }.reject(&:blank?)
                  },
                  lineItemsByFulfillmentOrder: [{fulfillmentOrderId: "gid://shopify/FulfillmentOrder/#{fo.id}", fulfillmentOrderLineItems: fo.line_items}]
                }
              end

              fulfilled_location_id = fulfill_locations["manual"]
              shipment_status = source_items.first[:shipment_status] || nil
              fulfillment_status = shop.update_fulfillment(order, fulfillment, notify_customer, shipment_status)

              log = if source_items.first[:sku]
                fulfillment_logs.where(order_number: order_no, sku: source_items.first[:sku]).first
              else
                fulfillment_logs.where(order_number: order_no).first
              end
              fulfill_log = save_to_logs(log, sync_log, order, order_no, source_items, fulfillment_status, fulfillment_logs)

              if fulfillment_status[:success]
                fulfillment_list << fulfillment.merge({order_number: order_no})
                fulfillments += 1
                shop.decrement!(:credit, 1) if shop.use_credit
              end
            end
          else
            by_fulfillment_services.each_pair do |location_id, line_items|
              if source.only_selected_location
                if !line_items.empty?
                  line_item = order.line_items.find { |line_item| line_item["id"] == line_items.first[:id] }
                  fulfilled_location_id = if line_item
                    fulfill_locations[line_item["fulfillment_service"]]
                  else
                    fulfill_locations["manual"]
                  end
                else
                  fulfilled_location_id = fulfill_locations["manual"]
                end
              else
                fulfilled_location_id = location_id
              end

              if update_mode || (source.fulfillment_status == "any")
                if fos && fos.length > 0
                  is_created = false
                  fos.each do |fo|
                    if ["open", "in_progress"].include?(fo.status)
                      # handle location move
                      if (location_key = source_items.first[:location])
                        location_id = fulfill_locations[location_key.parameterize.underscore]
                        if location_id && fo.assigned_location_id != location_id
                          @shop.move_fulfillment(fo, location_id)
                          fo.assigned_location_id = location_id
                        end
                      end

                      fulfill_line_items = []

                      fo.line_items.each do |li|
                        source_item = line_items.reject { |a| a[:id] != li["line_item_id"] }.first
                        if source_item
                          fulfill_line_items << {id: "gid://shopify/FulfillmentOrderLineItem/#{li["id"]}", quantity: source_item[:quantity]}
                        end
                      end
                      fulfillment = {
                        trackingInfo: {
                          company: source_items.first[:tracking_company],
                          numbers: source_items.collect { |a| a[:tracking_no].to_s.split(",") }.flatten.uniq,
                          urls: source_items.collect { |a| a[:tracking_url] }.reject(&:blank?)
                        },
                        lineItemsByFulfillmentOrder: [{fulfillmentOrderId: "gid://shopify/FulfillmentOrder/#{fo.id}", fulfillmentOrderLineItems: fulfill_line_items}]
                      }
                      is_created = true
                      fulfillment_status = shop.create_fulfillment(fulfillment, notify_customer, source.shipment_status)

                    end
                  end

                  unless is_created
                    ids = line_items.collect { |a| a[:id] }
                    order.fulfillments.each do |f|
                      line_item = f.line_items.reject { |a| !ids.include?(a["id"]) }.first
                      if line_item
                        fulfillment = {
                          trackingInfo: {
                            company: source_items.first[:tracking_company],
                            numbers: source_items.collect { |a| a[:tracking_no].to_s.split(",") }.flatten.uniq,
                            urls: source_items.collect { |a| a[:tracking_url] }.reject(&:blank?)
                          }
                        }
                        shipment_status = source_items.first[:shipment_status] || nil
                        fulfillment_status = shop.update_fulfillment(f, fulfillment, notify_customer, shipment_status)
                      end
                    end
                  end

                else
                  fulfillment = {
                    order_id: order.id,
                    trackingInfo: {
                      company: source_items.first[:tracking_company],
                      numbers: source_items.collect { |a| a[:tracking_no].to_s.split(",") }.flatten.uniq,
                      urls: source_items.collect { |a| a[:tracking_url] }.reject(&:blank?)
                    }
                  }
                  shipment_status = source_items.first[:shipment_status] || nil
                  fulfillment_status = shop.update_fulfillment(order, fulfillment, notify_customer, shipment_status)
                end

                log = if source_items.first[:sku]
                  fulfillment_logs.where(order_number: order_no, sku: source_items.first[:sku]).first
                else
                  fulfillment_logs.where(order_number: order_no).first
                end
                fulfill_log = save_to_logs(log, sync_log, order, order_no, source_items, fulfillment_status, fulfillment_logs)

                if fulfillment_status[:success]
                  fulfillment_list << fulfillment.merge({order_number: order_no})
                  fulfillments += 1
                  shop.decrement!(:credit, 1) if shop.use_credit
                end
              else
                unless line_items.empty?
                  if fos && fos.length > 0
                    fos.each do |fo|
                      next unless ["open", "in_progress"].include?(fo.status)
                      # handle location move
                      if (location_key = source_items.first[:location])
                        location_id = fulfill_locations[location_key.parameterize.underscore]
                        if location_id && fo.assigned_location_id != location_id
                          @shop.move_fulfillment(fo, location_id)
                          fo.assigned_location_id = location_id
                        end
                      end

                      fulfill_line_items = []

                      fo.line_items.each do |li|
                        item = {id: "", quantity: 0}
                        line_items.reject { |a| a[:id] != li["line_item_id"] }.each do |a|
                          item[:id] = li["id"]
                          item[:quantity] += a[:quantity]
                        end
                        unless item[:id].blank?
                          fulfill_line_items << {id: "gid://shopify/FulfillmentOrderLineItem/#{item[:id]}", quantity: item[:quantity]}
                        end
                      end
                      unless fulfill_line_items.blank?
                        fulfillment = {
                          trackingInfo: {
                            company: source_items.first[:tracking_company],
                            numbers: source_items.collect { |a| a[:tracking_no].to_s.split(",") }.flatten.uniq,
                            urls: source_items.collect { |a| a[:tracking_url] }.reject(&:blank?)
                          },
                          lineItemsByFulfillmentOrder: [{fulfillmentOrderId: "gid://shopify/FulfillmentOrder/#{fo.id}", fulfillmentOrderLineItems: fulfill_line_items}]
                        }
                        fulfillment_status = shop.create_fulfillment(fulfillment, notify_customer, source.shipment_status)
                      end
                    end
                  end

                  log_items.each do |item|
                    fulfill_log = fulfillment_logs.create do |log|
                      log.sync_log_id = sync_log.id
                      log.shop_id = shop.id
                      log.order_number = order_no
                      log.shopify_order_id = order.id
                      log.order_amount = order.total_price
                      log.currency = order.currency
                      log.lineitem_amount = item[:total]
                      log.sku = item[:sku]
                      log.quantity = item[:quantity]
                      log.tracking_no = source_items.first[:tracking_no]
                      log.tracking_company = source_items.first[:tracking_company]
                      log.financial_status = order.financial_status
                      log.shopify_fulfillment_id = fulfillment_status[:fulfillment] if fulfillment_status[:fulfillment]
                      log.fulfillment_status = fulfillment_status[:success].to_s
                      log.error_message = fulfillment_status[:error_message]
                      log.original_values = {tracking_company_mapping: source_items.first[:original_tracking_company]}
                    end
                  end

                  if fulfillment_status[:success]
                    fulfillment_list << fulfillment.merge({order_number: order_no, order_id: order.id, tracking_number: source_items.collect { |a| a[:tracking_no].to_s.split(",") }.flatten.uniq.join(",")})
                    fulfillments += 1
                    shop.decrement!(:credit, 1) if shop.use_credit
                  end
                end

              end
            end
          end

          # handle after fulfilled update order status
          if source.after_fulfilled_order_financial_status
            if source.after_fulfilled_order_financial_status == "close"
              if shop.close_order(order)
                shop.decrement!(:credit, 1) if shop.use_credit
              end
            elsif source.after_fulfilled_order_financial_status == "paid"
              puts "!!!! #{order.financial_status} != #{source.after_fulfilled_order_financial_status}"
              if order.financial_status != source.after_fulfilled_order_financial_status
                api = ShopifyAPI::LightGraphQL
                api.set(source.shop)
                data = api.query(GraphqlHelper.mark_order_as_paid(order.id))
                if data["data"]["userErrors"].blank?
                  shop.decrement!(:credit, 1) if shop.use_credit
                  order.financial_status = source.after_fulfilled_order_financial_status
                end
              end
            end
          end
          puts "AFTER #{order.financial_status}!!!"

          # only save orders that are fulfilled
          next unless fulfillment_status[:success]

          save_fulfilled_order(order, sync_log, fulfill_log)
          if paypal_order?(order) && auth_paypal?
            update_paypal_tracking_no(order, fulfill_log)
          end
          next unless @source.tag_enabled && @source.tag_value.present?
          success_ids_list << "gid://shopify/Order/#{order.id}"
          if success_ids_list.count >= 5
            add_tag(success_ids_list)
            success_ids_list = []
          end
        end
        if @source.tag_enabled && @source.tag_value.present? && success_ids_list.count > 0
          add_tag(success_ids_list)
          success_ids_list = []
        end
        shop.clear_session

        if partial
          sync_log.update(status: "success", error_message: "You are out of credit. Only #{fulfillments} fulfillments updated.", processed_at: Time.now, number_fulfillment_updated: fulfillments, total_process_orders: total_orders)
          UserMailer.notify_out_of_credit(shop, source).deliver_later
        else
          file_name = source.need_after_process? ? process_file : get_file_name
          sync_log.update(status: "success", error_message: "Processed #{if file_name.is_a?(Array)
                                                                           "#{file_name.count} files (#{file_name.first(3).join(",")}#{(file_name.count > 3) ? "..." : ""})"
                                                                         else
                                                                           file_name
                                                                         end}", processed_at: Time.now, number_fulfillment_updated: fulfillments, total_process_orders: total_orders)

          if shop.use_credit && (shop.credit <= shop.low_credit_alert)
            UserMailer.notify_low_credit(shop).deliver_later
          end
        end
        Rails.logger.info "log updated!!!!!!!!!!!!"
        source.update(status: "")
        {status: "success", fulfillments: fulfillment_list, sync_log: sync_log}
      else
        sync_log.update(status: "running", error_message: "There is already a process running", processed_at: Time.now)
        {status: "running", fulfillments: [], sync_log: []}
      end
    rescue ActiveResource::UnauthorizedAccess => e
      Airbrake.notify(e, {source: source})
      sync_log.update(status: "error", processed_at: Time.now, error_message: "Shopify API issue. Please ensure FulfillSync is authorized or try again later.")
      source.update({status: "", sync_status: "paused"})
      {status: "error", error: e.message, sync_log: sync_log}
    rescue CSV::MalformedCSVError => e
      Airbrake.notify(e, {source: source})
      sync_log.update(status: "error", processed_at: Time.now, error_message: "Please ensure CSV column separator is correct")
      source.update(status: "")
      {status: "error", error: e.message, sync_log: sync_log}
    rescue => e
      Rails.logger.info e.message
      Rails.logger.info e.backtrace.join("\n")
      Airbrake.notify(e, {source: source})
      sync_log.update(status: "error", processed_at: Time.now, error_message: "#{(fulfillments > 0) ? "#{fulfillments} fulfilled but error found" : "Error"}: #{e.message}", number_fulfillment_updated: fulfillments)
      source.update(status: "")
      {status: "error", error: e.message, sync_log: sync_log}
    end
  end

  def save_to_logs(log, sync_log, order, order_no, source_items, fulfillment_status, fulfillment_logs)
    if log
      log.sync_log_id = sync_log.id
      log.order_number = order_no
      log.shopify_order_id = order.id
      log.order_amount = order.total_price
      log.tracking_no = source_items.first[:tracking_no]
      log.tracking_company = source_items.first[:tracking_company]
      log.shopify_fulfillment_id = if fulfillment_status[:fulfillment].is_a?(Integer)
        fulfillment_status[:fulfillment]
      else
        (fulfillment_status[:fulfillment] ? fulfillment_status[:fulfillment].id : nil)
      end
      log.fulfillment_status = fulfillment_status[:success].to_s
      log.error_message = fulfillment_status[:error_message]
      log.original_values = {tracking_company_mapping: source_items.first[:original_tracking_company]}
      log.save
    else
      log = fulfillment_logs.create do |log|
        log.sync_log_id = sync_log.id
        log.shop_id = @shop.id
        log.order_number = order_no
        log.shopify_order_id = order.id
        log.order_amount = order.total_price
        log.currency = order.currency
        log.sku = source_items.first[:sku]
        log.quantity = source_items.first[:quantity]
        log.lineitem_amount = source_items.first[:total]
        log.tracking_no = source_items.first[:tracking_no]
        log.tracking_company = source_items.first[:tracking_company]
        log.shopify_fulfillment_id = if fulfillment_status[:fulfillment].is_a?(Integer)
          fulfillment_status[:fulfillment]
        else
          (fulfillment_status[:fulfillment] ? fulfillment_status[:fulfillment].id : nil)
        end
        log.fulfillment_status = fulfillment_status[:success].to_s
        log.error_message = fulfillment_status[:error_message]
        log.original_values = {tracking_company_mapping: source_items.first[:original_tracking_company]}
      end
    end

    log
  end

  def debug_orders(all_orders = false)
    data = download_file(build_file_reader)
    source_order_hash = source_data(data)
    unless source_order_hash[:status]
      return {status: "retrieve_failed", error: source_order_hash[:remark]}
    end
    unless source_order_hash[:data].present?
      return {status: "no_data", error: "No data available to sync. Please check your Test Column Mapping."}
    end

    source_order_hash = source_order_hash[:data]

    shop.create_session

    update_mode = all_orders == "true"
    order_hash, _total_orders = OrderFetcher.new(source: source, update_mode: update_mode).call(source_order_hash.keys)

    unless order_hash.present?
      return {status: "no_data", error: "No New Shopify orders available to sync"}
    end

    line_items_hash = {}
    source_order_hash.keys.each do |order_no|
      source_items = source_order_hash[order_no]
      order = order_hash[order_no]
      next unless order

      if order
        line_items, _log_items = order_fulfillment_items(source_items, order.line_items, order.refunds, source.only_selected_location).values
        line_items_hash[order_no] = line_items.values
      end
    end

    shop.clear_session
    {status: "success", line_items_hash: line_items_hash}
  rescue => e
    Rails.logger.info e.message
    Rails.logger.info e.backtrace.join("\n")
    Airbrake.notify(e, {source: source})
    {status: "error", error: e.message}
  end

  def strip_val(val)
    if val.is_a? String
      CGI.unescapeHTML(ActionView::Base.full_sanitizer.sanitize(val.strip))
    else
      val.to_s
    end
  end

  def list_orders
    orders, _total_orders = OrderFetcher.new(source: @source, update_mode: false).call([], true)

    orders.values
  end

  def save_fulfilled_order(fulfilled_order, sync_log, log)
    fulfilled_order = @shop.orders.new(app_order_id: fulfilled_order.id,
      app: "shopify",
      order_no: fulfilled_order.order_number,
      order_name: fulfilled_order.name,
      trace: "#{sync_log.id}::#{log.id}")
    fulfilled_order.save
  end

  def add_tag(ids)
    tries = 3
    begin
      res = ShopifyAPI::LightGraphQL.query(GraphqlHelper.add_orders_tag(ids, @source.tag_value))
      if res["errors"].present?
        if res["errors"].is_a?(Array) && res.dig("errors", 0, "message") == "Throttled"
          raise StandardError, "throttle"
        else
          # save order_id to a new log maybe?
        end
      end
    rescue => e
      if e.message == "throttle" && e.is_a?(StandardError)
        tries -= 1
        if tries > 0
          sleep 1.second
          retry
        else
          # also save order_id to a new log?
        end
      end
    end
  end

  def paypal_order?(order)
    payment_gateway = order.payment_gateway_names
    payment_gateway.any? { |s| s.downcase.include? "paypal" }
  end

  def auth_paypal?
    shop.paypal_client_id? && shop.paypal_client_secret?
  end

  def update_paypal_tracking_no(order, fulfill_log)
    tries ||= 2
    client_id = shop.paypal_client_id
    client_secret = shop.paypal_client_secret
    transactions = shop.get_transactions(order.id)
    success_traz = transactions.reject { |a| a.status != "success" }.first
    if success_traz&.receipt&.[]("payment_status") != "Completed"
      success_traz = transactions.reject { |a| a&.receipt&.[]("payment_status") != "Completed" }.first
    end
    if success_traz&.receipt&.has_key?("transaction_id")
      transaction_id = success_traz.receipt["transaction_id"]
      tracking_company = fulfill_log.tracking_company ? fulfill_log.tracking_company.parameterize.underscore.upcase : "OTHER"

      access_token ||= get_paypal_access_token(client_id, client_secret)

      begin
        uri = "https://api.paypal.com/v1/shipping/trackers-batch"

        headers = {
          "Content-Type" => "application/json",
          "Authorization" => "Bearer #{access_token}"
        }

        body = {
          trackers: [
            {
              transaction_id: transaction_id,
              tracking_number: fulfill_log.tracking_no,
              status: "SHIPPED",
              carrier: tracking_company
            }
          ]
        }

        response = HTTParty.post(uri, body: body.to_json, headers: headers)
        if response.code == 400 && response["errors"][0]["details"][0]["issue"] == "INVALID_CARRIER"
          raise "INVALID_CARRIER"
        else
          result = response["tracker_identifiers"]
        end
      rescue => e
        if e.message.index("INVALID_CARRIER")
          tries -= 1
          tracking_company = "OTHER"
          retry if tries > 0
        end
        Airbrake.notify(e, "SourceId: #{@source.id} FulfillmentLogID: #{fulfill_log.id} #{e.message}")
      end
      success = result ? !result.empty? : false
      p_order = ::PaypalOrder.new(transaction_id: transaction_id,
        success: success,
        fulfillment_log_id: fulfill_log.id)
      p_order.save
    end
  end

  def get_paypal_access_token(client_id, client_secret)
    uri = "https://api-m.paypal.com/v1/oauth2/token"

    headers = {
      "Content-Type" => "application/x-www-form-urlencoded"
    }

    auth = {
      username: client_id,
      password: client_secret
    }
    body = {
      "grant_type" => "client_credentials"
    }
    begin
      response = HTTParty.post(uri, body: body, basic_auth: auth, headers: headers)
      response["access_token"]
    rescue => e
      Airbrake.notify(e, "SourceId: #{@source.id} FulfillmentLogID: #{fulfill_log.id} #{e.message}")
    end
  end
end

class AwsServices
  def self.credentials
    @@credentials ||= {
      region: ENV["AWS_REGION"],
      access_key_id: ENV["AWS_ACCESS_KEY"],
      secret_access_key: ENV["AWS_SECRET_KEY"]
    }
  end

  def self.dynamodb_client
    Aws::DynamoDB::Client.new(credentials)
  end

  def self.api_gateway_client
    Aws::ApiGatewayManagementApi::Client.new(credentials.merge({endpoint: Settings.aws.websocket_endpoint}))
  end
end

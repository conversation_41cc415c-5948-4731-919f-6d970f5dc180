class Ga4AnalyticsService
  GA4_MEASUREMENT_ID = ENV["GA4_MEASUREMENT_ID"]
  GA4_API_SECRET = ENV["GA4_API_SECRET"]
  GA4_ENDPOINT = "https://www.google-analytics.com/mp/collect"

  def self.track_plan_purchase(user:, transaction_id:, plan:, price:, event_type: "upgrade")
    return unless Rails.env.production?
    return unless GA4_MEASUREMENT_ID.present? && GA4_API_SECRET.present?

    payload = {
      client_id: user.id.to_s,
      events: [{
        name: "purchase",
        params: {
          transaction_id: transaction_id,
          value: price.to_f,
          items: [{
            item_id: plan,
            item_name: plan,
            item_category: "subscription",
            price: price.to_f,
            quantity: 1
          }],
          shop_id: user.id.to_s
        }
      }]
    }

    send_to_ga4(payload)
    Rails.logger.info("GA4 Purchase Event: user=#{user.id}, plan=#{plan}, value=#{price}")
  end

  def self.track_credit_purchase(user:, transaction_id:, credits:, price:)
    return unless Rails.env.production?
    return unless GA4_MEASUREMENT_ID.present? && GA4_API_SECRET.present?

    payload = {
      client_id: user.id.to_s,
      events: [{
        name: "purchase",
        params: {
          transaction_id: transaction_id,
          value: price.to_f,
          items: [{
            item_id: "import_credits",
            item_name: "Import Credits (#{credits})",
            item_category: "credits",
            price: price.to_f,
            quantity: 1
          }],
          shop_id: user.id.to_s
        }
      }]
    }

    send_to_ga4(payload)
    Rails.logger.info("GA4 Purchase Event: user=#{user.id}, credits=#{credits}, value=#{price}")
  end

  def self.send_to_ga4(payload)
    response = Faraday.post(GA4_ENDPOINT) do |req|
      req.headers["Content-Type"] = "application/json"
      req.params["measurement_id"] = GA4_MEASUREMENT_ID
      req.params["api_secret"] = GA4_API_SECRET
      req.body = payload.to_json
    end

    unless response.success?
      Rails.logger.error("GA4 API Error: #{response.status} #{response.body}")
    end
  rescue => e
    Rails.logger.error("GA4 tracking error: #{e.message}")
  end
end

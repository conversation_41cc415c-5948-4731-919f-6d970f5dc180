class ProcessEmail
  attr_reader :email_object

  def initialize(email)
    @email_object = email
  end

  def process
    processed_emails = []
    @emails = []
    @emails << @email_object.to
    @emails << @email_object.cc if @email_object.cc.present?
    @emails = @emails.flatten

    @emails.each do |recipient|
      source_email = recipient[:email]
      unless processed_emails.include?(source_email)
        run(source_email)
        processed_emails << source_email
      end
    end
  end

  # process email has 3 responsibility
  # 1. create email prefix to use ActiveRecord find
  # 2. create email logs, and assign it to the source
  # 3. using attachments found in email logs, run fulfilment process on the logs
  def run(source_email)
    return false unless source_email.present?

    raw_source_email = source_email
    source_email = process_email(source_email)
    return false if source_email == "info@"
    source = find_source_using_email_prefix(source_email)

    return false unless valid_source?(source)
    create_raw_email_logs(raw_source_email)
    attachment = process_attachment(source.source_file_name)

    if attachment.present?
      create_email_logs(source, attachment)

      # scope = {person: {id: source.shop.id, username: source.shop.id, email: source_email}}

      # refactor this as well
      begin
        # sync fulfilment using delayed job
        if (source.source_type == "email") && (source.schedule_type == "on_email_received")
          if source.shop.any_running?
            UpdateFulfillmentsJob.set(wait: 7.minutes).perform_later(source.id)
          else
            UpdateFulfillmentsJob.perform_later(source.id)
          end
          true
        end
      rescue => _
        SyncLog.create(source_id: source.id, error_message: "Problem Processing from Email", processed_at: Time.now, caller: "EmailProcessor")
        false
      end
    else
      SyncLog.create(source_id: source.id, error_message: "Problem processing email attachment", processed_at: Time.now, caller: "EmailProcessor")
      false
    end
  end

  def valid_source?(source)
    ## law of demeter violation
    source.present? && @email_object.attachments.present? && source.shop.active?
  end

  def process_email(source_email)
    email = nil
    if (prefix = source_email.downcase.strip.split("@").first)
      email = prefix << "@"
    end
    email
  end

  # tested
  def find_source_using_email_prefix(email_prefix)
    Source.where("email like ?", "#{email_prefix}%").where(schedule_type: "on_email_received").first
  end

  def accepted_mime_types
    [
      "text/plain",
      "text/csv", # csv
      "application/csv",
      "text/x-csv",
      "application/vnd.ms-excel", # xls
      "application/vnd.openxmlformats-officedocument.spreadsheetml.template", # xlsx
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", # xlsx,
      "application/octet-stream" # any format
    ]
  end

  def valid_email_mime?(attachment)
    accepted_mime_types.include?(attachment.content_type.strip)
  end

  def process_attachment(source_file_name = "")
    current_attachment = nil
    source_file_name = "" if @email_object.attachments.count == 1
    @email_object.attachments.each do |attachment|
      if source_file_name.present?
        if /#{source_file_name}/i.match?(attachment.original_filename)
          current_attachment = attachment
          break
        end
      elsif valid_email_mime?(attachment)
        current_attachment = attachment
        break
      end
    end
    current_attachment
  end

  ##  no longer delete email logs after email logs is more than 2 , easier to debug
  def create_email_logs(source, attachment)
    source.email_logs.create(raw_msg: @email_object.raw_text, recipient: @email_object.to.first[:email], source_file: attachment, source_file_host: Socket.gethostname)
    source.update(source_file_host: Socket.gethostname)
  end

  private

  # create raw email logs
  def create_raw_email_logs(source_email)
    RawEmailLog.create(raw_message: @email_object.inspect.to_s, recipient: source_email.to_s)
  rescue => _
    Rails.logger.info "Create raw email log failed."
  end
end

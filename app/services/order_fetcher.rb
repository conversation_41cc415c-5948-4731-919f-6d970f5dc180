class OrderFetcher
  def initialize(opts = {})
    @source = opts.fetch(:source)
    @shop = @source.shop
    @update_mode = opts.fetch(:update_mode)
    @limit = 250
    @fulfillment_status = @update_mode ? ["any"] : @source.fulfillment_status.split(",")
    ShopifyAPI::LightGraphQL.set(@shop)
  end

  def count
    @shop.create_session
    limit = 250
    overall_total_orders = 0

    financial_statuses = @source.financial_status.split(",")
    financial_statuses = [""] if financial_statuses.empty?

    financial_statuses.each do |financial_status|
      financial_status.strip!

      @fulfillment_status.each do |fulfillment_status|
        fulfillment_status.strip!

        total_order = @shop.total_orders(financial_status, fulfillment_status, @source.order_days_ago.days.ago)
        _total_page = (total_order.to_f / limit.to_f).ceil
        overall_total_orders += total_order
      end
    end
    overall_total_orders
  end

  def call(order_keys, return_all = false)
    @shop.create_session
    shopify_orders = {}
    limit = 250
    overall_total_orders = 0

    financial_statuses = @source.financial_status.split(",")
    financial_statuses = [""] if financial_statuses.empty?

    # #refactor get rid of loops return financial status and fulfillment status separated by commas in get orders
    financial_statuses.each do |financial_status|
      break unless return_all || !order_keys.empty?

      financial_status.strip!

      @fulfillment_status.each do |fulfillment_status|
        break unless return_all || !order_keys.empty?

        fulfillment_status.strip!

        page = 1
        orders = @shop.get_orders(@source.order_status, financial_status, fulfillment_status, limit, @source.order_days_ago.days.ago, [])
        loop do
          Rails.logger.info "load orders #{financial_status}- #{page}"

          orders.each do |order|
            order_id = order.send(@source.order_key).to_s
            unless @source.shopify_order_key_constants.blank?
              order_id = order_id.gsub(Regexp.new(@source.shopify_order_key_constants), "")
            end
            if return_all
              shopify_orders[order_id] = order
            elsif order_keys.include?(order_id)
              order_keys.delete(order_id)
              shopify_orders[order_id] = order
            end
          end
          overall_total_orders += orders.count
          break unless return_all || !order_keys.empty?

          if orders.present? && ShopifyAPI::Order.next_page?
            orders = @shop.get_orders(@source.order_status, financial_status, fulfillment_status, limit, @source.order_days_ago.days.ago, [], ShopifyAPI::Order.next_page_info)
            page += 1
          else
            break
          end
        end
      end
    end
    Rails.logger.debug "Scaned #{overall_total_orders} orders"

    [shopify_orders, overall_total_orders]
  end

  private

  def get_source_orders_id(order_keys, key)
    saved_orders = case key
    when "name"
      @shop.orders.where(order_name: order_keys).pluck(:order_name)
    when "order_number"
      @shop.orders.where(order_no: order_keys).pluck(:order_no)
    when "id"
      @shop.orders.where(app_order_id: order_keys).pluck(:app_order_id)
    end
    order_keys - saved_orders
  end

  def get_shopify_orders_id(ids, key)
    str = []
    if key == "id"
      str = ids
    elsif key == "order_number"
      # some store can search order_number via graphql, need to find how to append suffix/postfix in order_no
      return str
    else
      tries = 3
      ids.each_slice(250) do |batch|
        need_sleep = nil
        result = ShopifyAPI::LightGraphQL.query(GraphqlHelper.get_orders_id(batch.count, batch))
        if result["errors"] && result["errors"].first["message"] == "Throttled"
          time_taken = ((result["extensions"].first[1]["requestedQueryCost"] - result["extensions"].first[1]["throttleStatus"]["currentlyAvailable"]) / result["extensions"].first[1]["throttleStatus"]["restoreRate"]).ceil

          need_sleep = time_taken.second + 1.second
          sleep need_sleep
        elsif result["data"] && result["data"]["orders"]
          orders = result["data"]["orders"]["edges"]
          if orders.is_a? Array
            orders.each do |edge|
              str << edge["node"]["id"].split("/").last
            end
          end
        else
          Airbrake.notify("#{@source.id}: nil result issue", result)
          need_sleep = 1.second
          raise StandardError.new(" nil result issue sleep 1 second")
        end
      rescue => e
        tries -= 1
        if tries > 0

          unless need_sleep.blank?
            sleep need_sleep
            retry
          end
        end

        Airbrake.notify("#{@source.id}: StandardError Order FETCHING: #{e.message} #{e.class}")
      end
    end
    str
  end
end

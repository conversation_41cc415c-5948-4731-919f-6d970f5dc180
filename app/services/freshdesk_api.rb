class FreshdeskApi
  include HTTParty
  base_uri "https://stocksync.freshdesk.com"

  def create_ticket(shopify_domain, subject, message, email)
    body = {
      subject: subject,
      name: shopify_domain,
      email: email,
      description: message,
      priority: 1,
      status: 2,
      product_id: 44000015238,
      custom_fields: {cf_store_name: shopify_domain}
    }

    result = self.class.post("/api/v2/tickets", headers: {"Content-Type" => "application/json", "Accept" => "application/json", "Authorization" => "Basic eHNXVUl0NGxOaGdyMkwwcjlQTGg6", "Cookie" => "_x_w=31_1"}, body: body.to_json)

    result = false if result.parsed_response.nil?
    result
  end
end

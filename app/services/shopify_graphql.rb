class ShopifyGraphql
  def initialize(shop)
    @shop = shop
    shop.create_session
    @client = ShopifyAPI::Clients::Graphql::Admin.new(session: ShopifyAPI::Context.active_session)
  end

  def get_variant(product_id)
    retries ||= 0
    get_product_variant_query
    result = nil
    product_id = "gid://shopify/ProductVariant/#{product_id}"

    response = @client.query(query: ProductVariantQuery, variables: {id: product_id})

    result = response.body if response.body.present?

    result
  rescue
    sleep 1.second
    retry if (retries += 1) < 3
  end

  def get_first_location_id(product_id)
    retries ||= 0
    get_product_variant_query
    result = nil
    product_id = "gid://shopify/ProductVariant/#{product_id}"

    response = @client.query(query: ProductVariantQuery, variables: {id: product_id})

    if response.body.present?
      inventory_levels = response.body["data"]["productVariant"]["inventoryItem"]["inventoryLevels"]["edges"]
      if inventory_levels.present?
        result = inventory_levels.first["node"]["location"]["id"]
      end
    end

    result.split("/").last
  rescue
    sleep 1.second
    retry if (retries += 1) < 3
  end

  private

  def get_product_variant_query
    query = <<-GRAPHQL
      query($id: ID!) {
        productVariant(id:$id) {
          id
          inventoryItem {
            id
            inventoryLevels(first:10) {
              edges {
                node {
                  id
                  location {
                    id
                  }
                  available
                }
              }
            }
          }
        }
      }
    GRAPHQL

    unless defined? ProductVariantQuery
      Kernel.const_set(:ProductVariantQuery, query)
    end
  end
end

require "roo"
class FileParser
  attr_accessor :source, :file_name, :downloaded_data, :config

  def initialize(source, file_name, downloaded_data, config)
    @source = source
    @file_name = file_name
    @downloaded_data = downloaded_data
    @config = config
    @premapping_keys = @source.premapping_keys.to_s.split(",")
  end

  def file_extension
    # can use File.extname for string with dot
    @file_name.split(".").last.split("?").first.downcase.to_sym
  end

  def parse
    replace_val
    case file_extension
    when :xml
      parse_xml
    when :xls
      parse_xls
    when :xlsx
      parse_xlsx
    else
      if @source.column_ranges.blank?

        check_plain_file_encoding
        parse_csv
      else
        parse_edi
      end
    end
  end

  private

  def replace_val
    find_val = source.find_val
    @downloaded_data = @downloaded_data.gsub(find_val, "") if find_val.present?
  end

  # xlsx will not be parsed properly if do encoding check for excel files
  def check_plain_file_encoding
    ## encoding performed here because gsub is performed on invalid downloaded_data
    @downloaded_data.encode!("UTF-8", "binary", invalid: :replace, undef: :replace, replace: "")
    unless @downloaded_data.nil? || @downloaded_data.valid_encoding?
      @downloaded_data = @downloaded_data.encode("UTF-8", "ISO-8859-1")
    end
  end

  def parse_csv
    CSV.parse(downloaded_data, **config)
  rescue CSV::MalformedCSVError
    CSV.parse(downloaded_data.strip, **config)
  end

  def parse_edi
    ranges = @source.column_ranges.to_s.split(",")
    result = downloaded_data.split("\n")

    return_result = []
    result.each do |row|
      next if row[0..1] == "OR"

      new_row = []
      ranges.each do |key|
        next if key.blank?

        key_token = key.split("-")
        start_idx = key_token.first.strip.to_i - 1
        end_idx = key_token.last.strip.to_i - 1

        value = row[start_idx..end_idx]
        value&.strip!
        new_row << value
      end
      return_result << new_row
    end
    return_result
  end

  def parse_xml
    csv = []

    if @downloaded_data.is_a? String
      @downloaded_data.prepend("<root>")
      @downloaded_data.concat("</root>")
    end

    xml = Nokogiri::XML.parse(@downloaded_data, nil, nil, Nokogiri::XML::ParseOptions::RECOVER)
    unless @source.parent_node.blank?
      parent_node = @source.parent_node
      parent_node = "//#{parent_node}" if @source.parent_node.index("/").nil?
      orders = xml.xpath(parent_node)
      orders.each do |order|
        row = []
        order.traverse do |node|
          if node.is_a?(Nokogiri::XML::Element) && node.element_children.empty?
            if @premapping_keys.blank?
              row << node.text
            elsif @premapping_keys.include? node.name
              row << node.text
            end
          end
        end
        csv << row
      end
    end
    csv
  end

  def parse_xls
    file_path = "/tmp/temp_file_#{@source.id}.#{file_extension}"
    File.binwrite(file_path, @downloaded_data)

    xls_file = Roo::Spreadsheet.open(file_path, extension: :xls)
    sheet = xls_file.sheet(0)
    File.delete(file_path) if file_path && File.exist?(file_path)
    CSV.parse(sheet.to_csv)
  end

  def parse_xlsx
    data = []
    file_path = "/tmp/temp_file_#{@source.id}.#{file_extension}"
    File.binwrite(file_path, @downloaded_data)
    xlsx = Roo::Excelx.new(file_path)

    if @source.parent_node.blank?
      sheet = xlsx.sheet(0)
    else
      begin
        sheet = xlsx.sheet(@source.parent_node)
      rescue RangeError => _
        sheet = xlsx.sheet(0) # default to first sheet
      end
    end
    File.delete(file_path) if file_path && File.exist?(file_path)
    sheet.parse do |row|
      data << row
    end
    data
  end
end

require "net/ftp"
class FtpFileReader < FileReader
  def self.attr_list
    %i[host login password file_name file_path process_type file_rename]
  end

  attr_accessor(*attr_list)

  def initialize(opts)
    @host = opts[:host].strip
    @login = opts[:login]
    @password = opts[:password]
    @ftp_mode = opts[:ftp_mode]
    @path_to_file = opts[:path_to_file]
    @encoding = opts[:encoding]
    path_separator = "/"
    path_separator = "\\" if @path_to_file.split("\\").length > 1
    file_path = @path_to_file.split(path_separator)
    @file_name = file_path.pop
    @file_name = "" if @file_name == "*"
    @file_path = file_path.join(path_separator)
    @file_path << path_separator unless @file_path.ends_with? path_separator
    @final_file_name = @file_name
    @process_type = opts[:process_type]
    @file_rename = opts[:file_rename]
    @timezone = opts[:timezone]
  end

  def get_session
    ftp = Net::FTP.new
    ftp.open_timeout = 20.seconds

    if @host.index(":")
      tokens = @host.split(":")
      ftp.connect(tokens.first, tokens.last)
    else
      ftp.connect(@host)
    end
    ftp.login(@login, @password)
    ftp.passive = @ftp_mode == "passive"

    if block_given?
      yield ftp
      ftp.close
    end
    ftp
  end

  def get_data(first_only = false)
    raise "Invalid host" if @host.nil?

    data = nil
    now = @timezone ? ActiveSupport::TimeZone.new(@timezone).now : Time.now
    is_xml = false
    need_strip = false
    dynamic_filename = FileReader.parse_url(@file_name, now)
    get_session do |ftp|
      ftp.chdir(@file_path) if @file_path.present?
      list_file = ftp.list(dynamic_filename)
      raise EOFError.new("List file empty: #{list_file}") if list_file.empty?

      list_file.each do |final_file_name|
        raise EOFError.new("File name blank") if final_file_name.nil?
        next if final_file_name.start_with?("total")

        file = final_file_name.match(/:\d\d\s(.*)$/)

        if file
          final_file_name = file[1]
          # next if [".", ".."].include? final_file_name
          # take only last filename if there is / folder name
          if final_file_name.index("/")
            final_file_name = final_file_name.split("/").last
          end
          unless final_file_name.index("   ").nil?
            final_file_name = final_file_name[final_file_name.index("   ")..final_file_name.length].strip
          end
        else
          final_file_name = list_file.first.split(" ").last
        end
        puts final_file_name
        @final_file_name = final_file_name
        Rails.logger.debug "read #{@final_file_name}"
        begin
          if @encoding.blank?
            file_data = ftp.getbinaryfile(final_file_name, nil)
          else
            tmp_file = Tempfile.new
            ftp.getbinaryfile(final_file_name, tmp_file)
            file_data = if first_only
              open(tmp_file.path, @encoding).readline
            else
              open(tmp_file.path, @encoding).read
            end
          end
        rescue Net::FTPPermError => _
          file_data = ftp.getbinaryfile(dynamic_filename, nil)
        end
        if file_data.is_a? String
          if final_file_name.downcase.end_with? ".csv", ".txt"
            data = "" if data.nil?
            if file_data.ends_with?("\r") || file_data.ends_with?("\n")
              data << file_data
            else
              # detech the right carrier return.
              # If not, the CSV parse will break
              sep = "\r"
              if file_data.split("\r\n").length > 1
                sep = "\r\n"
              elsif file_data.split("\n").length > 1
                sep = "\n"
              end
              need_strip = true
              data << file_data << sep
            end
          else
            data = file_data
          end

          is_xml = true if final_file_name.downcase.end_with? ".xml"

        else
          data = file_data
          break
        end
      end
    end
    if is_xml
      data.prepend("<root>")
      data.concat("</root>")
    end
    if need_strip
      data.strip!
    end
    data
  end

  def process_file
    now = @timezone ? ActiveSupport::TimeZone.new(@timezone).now : Time.now
    dynamic_filename = FileReader.parse_url(@file_name, now)
    processed_files = []
    get_session do |ftp|
      ftp.chdir(@file_path) if @file_path.present?
      list_file = ftp.list(dynamic_filename)

      list_file.each do |final_file_name|
        raise EOFError if final_file_name.nil?
        next if final_file_name.start_with?("total")

        file = final_file_name.match(/:\d\d\s(.*)$/)
        if file
          final_file_name = file[1]
          # take only last filename if there is / folder name
          if final_file_name.index("/")
            final_file_name = final_file_name.split("/").last
          end
          unless final_file_name.index("   ").nil?
            final_file_name = final_file_name[final_file_name.index("   ")..final_file_name.length].strip
          end
        else
          final_file_name = list_file.first.split(" ").last
        end
        @final_file_name = final_file_name
        processed_files << final_file_name
        begin
          case @process_type
          when "rename"
            Rails.logger.debug("rename file from #{@file_path + final_file_name} to #{full_file_path({rename_filename: @file_rename})} ")
            ftp.rename(@file_path + final_file_name, full_file_path({rename_filename: @file_rename}))
          when "append_date"
            datetime = Time.zone.now.to_i
            @file_path = ((ftp.pwd == "/") ? ftp.pwd : "#{ftp.pwd}/")
            Rails.logger.debug("rename file from #{@file_path + final_file_name} to #{full_file_path({datetime: datetime})} ")
            ftp.rename(@file_path + final_file_name, full_file_path({datetime: datetime}))
          when "delete"
            ftp.delete(@file_path + final_file_name)
          when "move"
            Rails.logger.debug("rename file from #{@file_path + final_file_name} to #{@file_rename.chomp("/")}/#{final_file_name} ")
            ftp.rename(@file_path + final_file_name, "#{@file_rename.chomp("/")}/#{final_file_name}")
          end
        rescue => e
          if e.message.index("550")
            next
          else
            raise e
          end
        end
      end
    end
    # @file_name = processed_files.first
    processed_files
  end
end

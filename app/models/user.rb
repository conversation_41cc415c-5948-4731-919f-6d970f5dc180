class User < ApplicationRecord
  devise :database_authenticatable, :registerable,
    :recoverable, :rememberable, :trackable, :validatable
  has_many :shops
  belongs_to :shop

  def email_required?
    false
  end

  def email_changed?
    false
  end

  # use this instead of email_changed? for Rails = 5.1.x
  def will_save_change_to_email?
    false
  end

  def initialize(arg)
    super(arg)
    _validators[:email].reject! { |v| v.instance_of?(ActiveRecord::Validations::UniquenessValidator) }
  end

  validates_uniqueness_of :email, scope: :shop_id
end

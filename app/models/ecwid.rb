class Ecwid < Shop
  include EcwidClient

  def self.auth
    @auth ||= EcwidApi::OAuth.new do |config|
      config.client_id = ENV["ECWID_CLIENT_ID"]
      config.client_secret = ENV["ECWID_CLIENT_SECRET"]
      config.redirect_uri = Settings.ecwid.redirect_uri
      config.scope = "read_orders, update_orders"
    end
  end

  def self.get_authorization_url
    auth.oauth_url
  end

  def self.from_omniauth(auth)
    shop = where(ecwid_store_id: auth.store_id).first_or_initialize do |shop|
      shop.provider = "Ecwid"
      shop.ecwid_store_id = auth.store_id
      shop.installed_at = Time.now
      shop.uninstalled_at = nil
    end
    shop.ecwid_token = auth.access_token
    if shop.save
      user = User.where(email: auth.email, shop_id: shop.id).first_or_initialize do |user|
        user.email = auth.email
        user.password = Devise.friendly_token[0, 20]

        user.save
        UserMailer.welcome_email(user.email, "", shop.provider).deliver_now
      end
      shop.email = user.email
      shop.notification_email = shop.email if shop.notification_email.blank?
      shop.user_id = user.id
      shop.save
    end
    shop.update_shop_info

    Airbrake.notify("user error while oauth", {shop: shop}) unless shop.valid?

    shop
  end

  def update_shop_info
    info = get_shop_information
    return nil unless info

    if info["generalInfo"]
      self.ecwid_store_url = info["generalInfo"]["storeUrl"].gsub("https://", "")
    end

    if info["mailNotifications"]
      self.notification_email = info["mailNotifications"]["adminNotificationEmails"].join(",")
    end

    if info["formatsAndUnits"]
      self.timezone = info["formatsAndUnits"]["timezone"]
    end

    save if changed?
  end

  def store_domain
    ecwid_store_url
  end
end

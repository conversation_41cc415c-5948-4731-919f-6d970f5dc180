class SessionStorage
  # ?deprecated in new version of shopfy_api
  # include ShopifyAPI::Auth::SessionStorage

  def initialize
    # Initialize as needed
  end

  def store_session(session)
    # Implement a store function
    Rails.logger.info "store_session"
    Rails.logger.info "store_session"
    Rails.logger.info session.inspect
    # some_store_function(id: session.id, session_data: session.serialize)
  end

  def load_session(id)
    # Implement a fetch function
    Rails.logger.info "load_session #{id}"
  end

  def delete_session(id)
    # Implement a delete function
    Rails.logger.info "delete_session #{id}"
    true
  end
end

class BigcaringFileReader < FileReader
  # BASE_URL = "https://dev-mc-webhook.bigcaring.com.my/api" - Development
  BASE_URL = "https://mc-webhook.bigcaring.com.my/api"

  def initialize(opts)
    @secret_key = ENV["BIGCARING_SECRET_KEY"]
    @source_id = opts[:file].model.id
  end

  def get_data(_first_only = false)
    result = []

    source = Source.find_by(id: @source_id)
    orders = source.list_orders

    return result if orders.blank?

    order_numbers = orders.map(&:name).reverse

    order_numbers.each do |name|
      response = HTTParty.post(
        "#{BASE_URL}/v1/order/get_shopify_order_detail",
        headers: {"Content-Type" => "application/json"},
        body: {
          secretkey: @secret_key,
          order_reference: name
        }.to_json
      )

      if response.success?
        json = JSON.parse(response.body)
        if json["data"] && !json["data"].empty?
          # Add latest order status to the data
          if json["data"]["order_status_history"].present?
            json["data"]["latest_order_status"] = json["data"]["order_status_history"].first["order_status"]
          end
          result.concat([json["data"]])
          # else
          #   puts "Order #{name}: #{json["code_description"]}"
        end
      else
        Rails.logger.error("HTTP error fetching order #{name} for #{@source_id}: #{response.code}")
      end
    end

    values_array = result.map { |order| order.values }

    Rails.logger.info("Bigcaring order data")
    Rails.logger.info(values_array.inspect)
    values_array.empty? ? [[]] : values_array
  end
end

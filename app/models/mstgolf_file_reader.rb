class MstgolfFileReader < FileReader
  BASE_URLS = {
    "my" => "http://ip28.mstgolf.com/ecomMY/api/",
    "sg" => "http://ip28.mstgolf.com/ecomSG/api/"
  }.freeze

  def initialize(opts)
    @username = opts[:file].model.source_login
    @password = opts[:file].model.source_password
    @source_id = opts[:file].model.id
    @base_url = get_base_url(opts)
  end

  def get_data(first_only = false)
    arrays = []

    begin
      token = fetch_bearer_token
      return [[]] unless token

      date = Date.today - 10.days
      orders_url = "#{@base_url}OrderStatus/getWEBOrd?dateFrom=#{date.strftime("%Y/%m/%d")}"
      Rails.logger.info("MST order URL")
      Rails.logger.info(orders_url)

      orders_data = JSON.parse(fetch_data_with_retries(orders_url))

      orders_data.each do |order|
        ponumber = order["invno"]
        encoded_ponumber = URI.encode_www_form_component(ponumber)
        details_url = "#{@base_url}OrderStatus/getWEBOrdDetails?webno=#{encoded_ponumber}"
        order_details = JSON.parse(fetch_data_with_retries(details_url, {headers: {"Authorization" => "Bearer #{token}"}}))
        arrays << order_details.first if order_details&.first
      end
    rescue => e
      Airbrake.notify(e, "Source: #{@source_id}, #{e.message}")
    end

    values_array = arrays.map { |order| order.values }
    Rails.logger.info("MST order data")
    Rails.logger.info(values_array.inspect)
    values_array.empty? ? [[]] : values_array
  end

  def fetch_data_with_retries(url, options = {}, retries = 3)
    response = HTTParty.get(url, {timeout: 180}.merge(options))
    JSON.parse(response.body)
  rescue Net::ReadTimeout => e
    retries -= 1
    if retries > 0
      sleep(10)
      retry
    else
      raise e
    end
  end

  private

  def get_base_url(opts)
    region = opts[:file].model.connection_settings.fetch("region").to_s.strip.downcase

    if region.blank? || !BASE_URLS.key?(region)
      Airbrake.notify("Invalid country code", message: "Source: #{@source_id}, Region: #{region}")
      raise StandardError, "Invalid MST Golf region: #{region}"
    end

    BASE_URLS[region]
  end

  def fetch_bearer_token
    token_url = "#{@base_url}RequestToken/Get"
    body = {username: @username, password: @password}

    begin
      response = HTTParty.get(token_url, query: body, headers: {"Content-Type" => "application/json"})
      JSON.parse(response.body)
    rescue => e
      Airbrake.notify(e, "Source: #{@source_id}, API Token Fetch Error: #{e.message}")
      nil
    end
  end
end

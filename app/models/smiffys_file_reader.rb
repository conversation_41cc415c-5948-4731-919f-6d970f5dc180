class SmiffysFileReader < FileReader
  def self.attr_list
    %i[client_id api_key source_id]
  end

  attr_accessor(*attr_list)

  def initialize(opts)
    @client_id = opts[:file].model.source_login
    @api_key = opts[:file].model.source_password
    @source_id = opts[:file].model.id
  end

  def get_data(first_only = false)
    arrays = []
    date = DateTime.now
    start_date = date.beginning_of_month.strftime("%F")
    end_date = date.end_of_month.strftime("%F")
    body = {
      apiKey: api_key,
      clientID: client_id,
      startDate: start_date,
      endDate: end_date
    }
    url = "http://webservices.smiffys.com/services/orders.asmx/GetStatusByDate"
    begin
      result = HTTParty.get(url, query: body, headers: {})
      xml = Nokogiri::XML.parse(result.body, nil, nil, Nokogiri::XML::ParseOptions::RECOVER)
      orders = xml.search("OrderStatus")
      orders.each do |order_node|
        data = []
        data << order_node.search("YourOrderNumber").text
        data << order_node.search("Consignments/Consignment/Number").text
        arrays << data
      end
    rescue => e
      Airbrake.notify(e, "Source: #{source_id}, #{e.message}")
    end
    arrays.empty? ? [[]] : arrays
  end

  def process_file
    nil
  end
end

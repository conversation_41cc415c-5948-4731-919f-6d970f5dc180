require "open-uri"
require "net/ftp"
class FileReader
  attr_reader :final_file_name

  def full_file_path(opts = {})
    if opts[:datetime].present?
      "#{@file_path}#{opts[:datetime]}_#{@file_name}"
    elsif opts[:rename_filename].present?
      now = @timezone ? ActiveSupport::TimeZone.new(@timezone).now : Time.now
      if opts[:rename_filename].scan(/(?=\/)/).count > 1
        FileReader.parse_url(opts[:rename_filename], now)
      else
        "#{@file_path}#{FileReader.parse_url(opts[:rename_filename], now)}"
      end
    else
      now = @timezone ? ActiveSupport::TimeZone.new(@timezone).now : Time.now
      "#{@file_path}#{FileReader.parse_url(@file_name, now)}"
    end
  end

  def get_error_message(message, opts = {})
    if message.downcase.index("no such file") || message.downcase.index("not found")
      "File #{@final_file_name} not found (#{message})"
    elsif !opts.blank? && opts[:err_type]
      case opts[:err_type]
      when :ftp
        "Please check your FTP username & password."
      when :sftp
        "Please check your SFTP username & password."
      else
        "Failed to retrieve source: #{message}"
      end
    else
      "Failed to retrieve source: #{message}"
    end
  end

  def get_data(_first_only = false)
    nil
  end

  def read(first_only = false)
    tries = 3
    begin
      data = get_data(first_only)
      unless data.nil?
        status = true
        remark = nil
      end
    rescue EOFError => e
      if tries > 0
        tries -= 1
        sleep 5.seconds
        retry
      else
        remark = "Please check file name #{@final_file_name} (#{e.message})."
      end
      
    rescue SocketError => _
      remark = "Please check source host."
    rescue OpenURI::HTTPError => _
      remark = "File not found from the URL/host."
    rescue Net::FTPPermError => e
      puts e.message
      puts e.backtrace.join("\n")

      remark = get_error_message(e.message, err_type: :ftp)
    rescue Net::SFTP::StatusException => e
      puts e.message
      puts e.backtrace.join("\n")
      remark = get_error_message(e.message, err_type: :sftp)
    rescue Errno::ETIMEDOUT => _
      remark = "Failed to connect with FTP/SFTP server."
    rescue CSV::MalformedCSVError => _
      remark = "Please ensure CSV column separator is correct"
    rescue => e
      if tries > 0
        tries -= 1
        puts "retry #{tries}"
        puts e.message
        sleep 1.seconds
        retry
      else
        puts e.message
        puts e.backtrace.join("\n")
        Airbrake.notify(e, {source: self})
        remark = get_error_message(e.message)
      end

    end
    {status: status, data: data, remark: remark}
  end

  def self.parse_url(url, date)
    begin
      prev_date = date - 1.day
      new_url = url.clone
      new_url.gsub!("%{b-}", prev_date.strftime("%b"))
      new_url.gsub!("%{^b-}", prev_date.strftime("%^b"))
      new_url.gsub!("%{d-}", prev_date.strftime("%d"))
      new_url.gsub!("%{Y-}", prev_date.strftime("%Y"))
      new_url.gsub!("%{y-}", prev_date.strftime("%y"))
      new_url.gsub!("%{m-}", prev_date.strftime("%m"))
      new_url.gsub!("%{B-}", prev_date.strftime("%B"))
      new_url.gsub!("%{^B-}", prev_date.strftime("%^B"))

      new_url.gsub!("%b", date.strftime("%b"))
      new_url.gsub!("%^b", date.strftime("%^b"))
      new_url.gsub!("%d", date.strftime("%d"))
      new_url.gsub!("%Y", date.strftime("%Y"))
      new_url.gsub!("%y", date.strftime("%y"))
      new_url.gsub!("%m", date.strftime("%m"))
      new_url.gsub!("%H", date.strftime("%H"))
      new_url.gsub!("%B", date.strftime("%B"))
      new_url.gsub!("%^B", date.strftime("%^B"))

      new_url.gsub!("%{year}", date.year.to_s)
      new_url.gsub!("%{day}", date.day.to_s)
      new_url.gsub!("%{month}", date.month.to_s)
      new_url.gsub!("%{full_day}", date.day.to_s.rjust(2, "0"))
      new_url.gsub!("%{full_month}", date.month.to_s.rjust(2, "0"))
      new_url.gsub!("%{full_prev_month}", prev_date.month.to_s.rjust(2, "0"))
      new_url.gsub!("%{full_prev_day}", prev_date.day.to_s.rjust(2, "0"))
      new_url.gsub!("%{prev_day}", prev_date.day.to_s)
    rescue => _
      # ignore when can't match
    end
    new_url
  end
end

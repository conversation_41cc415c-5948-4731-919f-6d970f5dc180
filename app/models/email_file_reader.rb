require "net/ftp"
class EmailFileReader < FileReader
  def initialize(opts = {})
    @email_log = opts.fetch(:email_logs)
    @encoding = opts.fetch(:encoding)
    @source_file_host = opts[:source_file_host]
    @timezone = opts[:timezone]
  end

  def final_file_name
    file_name
  end

  def full_file_path(_opts = {})
    now = @timezone ? ActiveSupport::TimeZone.new(@timezone).now : Time.now
    "#{file_path}#{FileReader.parse_url(file_name, now)}"
  end

  def file_name
    @email_log.source_file.file.filename
  end

  def file_path
    @email_log.source_file.path
  end

  # no need to get session
  def process_file
    file_name
  end

  def get_data(first_only = false)
    if @email_log.nil?
      raise "Please send email with shipping feed file as attachment to test column mapping."
    end
    FeedTransferer.transfer_file(@email_log.source_file, @source_file_host)
    email_log = @email_log
    if @encoding.blank?
      if first_only
        File.readlines(email_log.source_file.path)[0]
      else
        File.read(email_log.source_file.path)
      end
    elsif first_only
      File.new(email_log.source_file.path, @encoding).readline
    else
      File.new(email_log.source_file.path, @encoding).read
    end
  end

  def read(first_only = false)
    file_data = get_data(first_only)
    {data: file_data, status: true}
  end
end

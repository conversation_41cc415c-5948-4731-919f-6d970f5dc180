class Payment
  def initialize(shop, opts = {})
    @shop = shop
    @opts = opts
  end

  def calculate_price(credit)
    (credit.to_i < 10_000) ? credit.to_i * Settings.flexi.cost : 39
  end

  def create_billing(charge_id, charge_name, charge_price, status)
    billing = @shop.billings.new(
      charge_id: charge_id,
      shop_name: (@shop.provider == "Shopify") ? @shop.shopify_domain : @shop.ecwid_store_url,
      plan_name: charge_name,
      total_charge: charge_price,
      status: status
    )

    billing.save
  end
end

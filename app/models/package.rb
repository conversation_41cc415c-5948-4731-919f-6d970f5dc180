class Package
  attr_reader :key, :title, :cost, :limit
  def initialize(key)
    @package = Settings.packages[key]
    if @package
      @key = key
      @title = @package.title
      @cost = @package.cost
      @limit = @package.limit
    end
  end

  def activate(shop, charge_id)
    shop.charge_id = charge_id
    shop.charged_at = DateTime.now
    shop.package = @key
    shop.source_limit = @limit
    shop.save(validate: false)
  end

  def deactivate(shop)
    shop.charge_id = nil
    shop.package = "trial"
    shop.source_limit = 5
    shop.save(validate: false)
  end
end

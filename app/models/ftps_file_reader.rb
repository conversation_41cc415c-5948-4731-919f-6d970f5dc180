require "double_bag_ftps"
class FtpsFileReader < FtpFileReader
  def get_session
    if @host.index(":")
      tokens = @host.split(":")
      ftp = Net::FTP.new(tokens.first, port: tokens.last, ssl: {verify_mode: OpenSSL::SSL::VERIFY_NONE}, private_data_connection: true)

    else
      ftp = Net::FTP.new(@host, ssl: {verify_mode: OpenSSL::SSL::VERIFY_NONE}, private_data_connection: true)

    end
    ftp.login(@login, @password)
    ftp.passive = @ftp_mode == "passive"

    if block_given?
      yield ftp
      ftp.close
    end
    ftp
  end
end

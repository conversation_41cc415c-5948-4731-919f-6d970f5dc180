class SftpFileReader < FileReader
  def self.attr_list
    %i[host login password path_to_file process_type file_rename ssh_key]
  end

  attr_accessor(*attr_list)

  def initialize(opts)
    @host = opts[:host]
    @login = opts[:login]
    @password = opts[:password]
    @ftp_mode = opts[:ftp_mode]
    @ssh_key = opts[:ssh_key]
    @path_to_file = opts[:path_to_file]
    path_separator = "/"
    path_separator = "\\" if @path_to_file.split("\\").length > 1
    file_path = @path_to_file.split(path_separator)
    @file_name = file_path.pop
    @file_path = file_path.join(path_separator)
    @file_path << path_separator unless @file_path.ends_with? path_separator
    @final_file_name = @file_name
    @process_type = opts[:process_type]
    @file_rename = opts[:file_rename]
    @timezone = opts[:timezone]

    @final_files = []
  end

  def get_session
    opts = {timeout: 45.seconds, append_all_supported_algorithms: true, user_known_hosts_file: %w[/dev/null]}
    if @ssh_key.present?
      opts[:key_data] = @ssh_key
    elsif @password.present?
      opts[:password] = @password
    end

    url = @host
    if url.index(":")
      tokens = url.split(":")
      opts[:port] = tokens.last
      url = tokens.first
    end
    Net::SFTP.start(url, @login, opts) do |sftp|
      yield sftp
    end

    true
  end

  def get_data(first_only = false)
    raise "Invalid host" if @host.nil?

    data = nil
    opts = {append_all_supported_algorithms: true, user_known_hosts_file: %w[/dev/null]}
    if @ssh_key.present?
      opts[:key_data] = @ssh_key
    elsif @password.present?
      opts[:password] = @password
    end

    url = @host
    if url.index(":")
      tokens = url.split(":")
      opts[:port] = tokens.last
      url = tokens.first
    end
    sftp = Net::SFTP.start(url, @login, opts)

    now = @timezone ? ActiveSupport::TimeZone.new(@timezone).now : Time.now

    file_name = FileReader.parse_url(@file_name, now)
    @final_file_name = file_name
    if file_name.index("*")
      files = sftp.dir.glob(@file_path, file_name)
      begin
        # sort the latest file first
        files.sort { |a, b| b.attributes.mtime <=> a.attributes.mtime }.each do |file|
          @final_file_name = file.name if file
          file_data = if first_only
            sftp.file.open("#{@file_path}/#{@final_file_name}", "r").readline
          else
            sftp.file.open("#{@file_path}/#{@final_file_name}", "r").read
          end
          @final_files << @final_file_name
          if file_data.is_a? String
            data = "" if data.nil?
            data << file_data
          else
            data = file_data
            break
          end
        end
      rescue Net::SFTP::StatusException => e
        Rails.logger.debug "Can't read SFTP wildcard #{@file_path}/#{@final_file_name}: #{e.message}"
        data
      end

    else
      begin
        data = if first_only
          sftp.file.open("#{@file_path}/#{@final_file_name}", "r").readline
        else
          sftp.file.open("#{@file_path}/#{@final_file_name}", "r").read
        end
      rescue Net::SFTP::StatusException => e
        raise Net::SFTP::StatusException.new(OpenStruct.new(code: 2, message: "Can't read SFTP file #{@file_path}/#{@final_file_name}: #{e.message}"), "no such file")
      end
    end
    data
  end

  def process_file
    matching_files = []
    get_session do |sftp|
      now = @timezone ? ActiveSupport::TimeZone.new(@timezone).now : Time.now

      file_name = FileReader.parse_url(@file_name, now)
      if file_name.index("*")

        #   files = sftp.dir.glob(@file_path, file_name)

        #   # sort the latest file first

        #   files.sort { |a, b| b.attributes.mtime <=> a.attributes.mtime }.each do |file|
        #     matching_files << file.name
        #   end
        matching_files = @final_files
      else
        matching_files << file_name
      end

      datetime = now.to_i
      matching_files.each do |file_name|
        @file_name = file_name
        @final_file_name = file_name
        ff_path = full_file_path
        case @process_type
        when "rename"
          sftp.rename(ff_path, full_file_path({rename_filename: @file_rename}))
        when "append_date"
          sftp.rename(ff_path, full_file_path({datetime: datetime}))
        when "delete"
          sftp.remove(ff_path)
        when "move"
          begin
            sftp.rename!(ff_path, "#{@file_rename.chomp("/")}/#{@file_name}")
          rescue Net::SFTP::StatusException => e
            Rails.logger.error "#{@host} #{e.message} move from #{ff_path} to #{"#{@file_rename.chomp("/")}/#{@file_name}"}"
          end

        end
      end
    end

    matching_files
  end
end

class BackgroundProcess < ApplicationRecord
  class << self
    def create_background_process
      background_process = BackgroundProcess.new
      background_process.running_source_count = Source.where(status: "running").order("created_at desc").count
      background_process.running_job_count = Delayed::Job.where(queue: "update_fulfillments").count

      background_process.save

      queuing_profiles_count = Source.where(status: "queuing").count
      if queuing_profiles_count > 30
        BackendMailer.queuing_profile_email(queuing_profiles_count).deliver!
      end
    end

    def show_background_process
      start_time = Time.zone.now - 1.day
      end_time = Time.zone.now
      background_process = BackgroundProcess.where("created_at >= ? AND created_at <= ?", start_time, end_time)
      time = []
      background_process.pluck("created_at").each do |t|
        time.append(t.strftime("%I:%M%p"))
      end
      {
        timestamp: time,
        running_source: background_process.pluck("running_source_count"),
        running_job: background_process.pluck("running_job_count")
      }
    end
  end
end

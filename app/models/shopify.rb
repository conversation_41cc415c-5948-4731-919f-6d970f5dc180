class Shopify < Shop
  include ShopifyApp::SessionStorage
  include ShopifyClient

  def self.from_omniauth(auth)
    info = nil

    shop = where(shopify_domain: auth.uid).first_or_initialize do |shop|
      shop.provider = "Shopify"
      shop.shopify_token = auth.credentials.token
      shop.save
      info = shop.get_shop_information
      shop.customer_support_email = info.customer_email
      shop.installed_at = Time.now
      shop.timezone = info.iana_timezone
      shop.primary_location_id = info.primary_location_id

      # If you are using confirmable and the provider(s) you use validate emails,
      # uncomment the line below to skip the confirmation emails.
      # shop.skip_confirmation!
    end
    # for reinstalls
    if shop.persisted?
      shop.shopify_token = auth.credentials.token
      shop.uninstalled_at = nil
    end

    info ||= shop.get_shop_information

    shop.email = info.email
    shop.notification_email = shop.email if shop.notification_email.blank?
    shop.country_name = info.country_name
    shop.city = info.city
    shop.shopify_plan_name = info.plan_name
    if shop.save
      user = User.where(email: info.email.downcase, shop_id: shop.id).first_or_initialize do |user|
        user.password = Devise.friendly_token[0, 20]
        user.save
        UserMailer.welcome_email(shop.email, shop.shopify_domain, shop.provider).deliver_now
      end

      shop.user_id = user.id
      shop.save

    end

    Airbrake.notify("user error while oauth", {shop: shop}) unless shop.valid?

    shop
  end

  def send_payment(opts)
    payment = ShopifyPayment.new(self, opts)
    payment.call
  end

  def klass_for_payment
    "ShopifyPayment".constantize
  end

  def store_domain
    shopify_domain
  end

  def api_version
    ShopifyApp.configuration.api_version
  end
end

class ImplicitFtpFileReader < FtpFileReader
  def get_session
    if @host.index(":")
      tokens = @host.split(":")
      ftp = ImplicitTlsFTP.open(tokens.first, port: tokens.last, ssl: {verify_mode: OpenSSL::SSL::VERIFY_NONE}, open_timeout: 120.seconds, read_timeout: 3000)
      ftp.connect(tokens.first, tokens.last)
    else
      ftp = ImplicitTlsFTP.open(@host, ssl: {verify_mode: OpenSSL::SSL::VERIFY_NONE}, open_timeout: 120.seconds, read_timeout: 3000)
      ftp.connect(@host)
    end
    ftp.login(@login, @password)
    ftp.passive = @ftp_mode == "passive"

    if block_given?
      yield ftp
      ftp.close
    end
    ftp
  end
end

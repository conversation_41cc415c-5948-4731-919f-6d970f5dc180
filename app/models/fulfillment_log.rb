class FulfillmentLog < ApplicationRecord
  include Exportable
  belongs_to :source
  belongs_to :shop
  belongs_to :sync_log
  delegate :shop, to: :source
  has_one :paypal_order

  scope :paypal_fulfillments, ->(shop_id) { joins(:paypal_order).where("fulfillment_logs.shop_id = ?", shop_id) }

  class << self
    def export_columns(options = {})
      platform = options.fetch(:platform, "shopify")

      case platform
      when "shopify"
        {
          order_number: "Shopify Order",
          sku: "SKU",
          quantity: "Quantity",
          tracking_no: "Tracking Number",
          tracking_company: "Tracking Company",
          created_at: "Fulfilled At"
        }
      when "ecwid"
        {
          order_number: "Ecwid Order",
          tracking_no: "Tracking Number",
          created_at: "Fulfilled At"
        }
      when "big_commerce"
        {
          order_number: "BigCommerce Order",
          tracking_no: "Tracking Number",
          tracking_company: "Tracking Company",
          created_at: "Fulfilled At"
        }
      end
    end
  end

  def paypal_order?
    paypal_order != nil
  end

  def paypal_success
    return unless paypal_order?

    (paypal_order.success == true) ? "Success" : "Fail"
  end

  def paypal_transaction
    return unless paypal_order?

    paypal_order.transaction_id
  end
end

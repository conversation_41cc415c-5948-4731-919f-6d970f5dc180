class Source::BigCommerce < Source
  def self.sti_name
    "big_commerce"
  end

  def test_column_mapping
    data = download_file(build_file_reader)
    source_mappings = []
    default_data = TestColumnMappingData.new

    if data.empty? || data[:data].empty?
      return {status: false, data: default_data, sample_shopify_order: nil, remark: data[:remark]}
    end

    data = data[:data]
    column_mapping = {}
    orders_header = data[1]
    orders_header = data[0] if orders_header.nil?
    if orders_header
      keys = {
        order_no: order_no_mapping,
        tracking_no: tracking_no_mapping,
        ignore_column: ignore_key
      }
      keys.each do |k, v|
        column_mapping[k] = orders_header[v].to_s.strip if v && orders_header[v]
      end

      source_mappings = keys.keys

      data = TestColumnMappingData.new(column_mapping)
    end

    {status: true, data: data, source_mappings: source_mappings, sample_shopify_order: nil, remark: nil}
  rescue => e
    default_data = TestColumnMappingData.new
    puts e.message
    puts e.backtrace.join("\n")
    Airbrake.notify(e, {source: self})
    {status: false, data: default_data, source_mappings: source_mappings, sample_shopify_order: nil, remark: e.message}
  end
end

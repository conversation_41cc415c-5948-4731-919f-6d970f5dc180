class Source::Shopify < Source
  def self.sti_name
    "shopify"
  end

  def test_column_mapping
    data = download_file(build_file_reader)
    source_mappings = []
    default_sample_shopify_order = SampleShopifyOrder.new
    default_data = TestColumnMappingData.new

    if data.empty? || data[:data].empty?
      return {status: false, data: default_data, sample_shopify_order: default_sample_shopify_order, remark: data[:remark]}
    end

    data = data[:data]
    column_mapping = {}
    orders_header = orders_header.nil? ? data[0] : data[1]

    if orders_header
      keys = {
        order_no: order_no_mapping,
        item_sku: sku_mapping,
        item_quantity: quantity_mapping,
        tracking_no: tracking_no_mapping,
        tracking_company: tracking_company_mapping,
        tracking_url: tracking_url_mapping,
        ignore_column: ignore_key
      }
      keys.each do |k, v|
        column_mapping[k] = orders_header[v].to_s.strip if v && orders_header[v]
      end

      source_mappings = keys.keys

      data = TestColumnMappingData.new(column_mapping)
    end

    sample_order = shop.get_orders(order_status, financial_status, "unshipped").first
    sample_shopify_order = SampleShopifyOrder.from_shopify_order(sample_order)

    {status: true, data: data, source_mappings: source_mappings, sample_shopify_order: sample_shopify_order, remark: nil}
  rescue => e
    default_sample_shopify_order = SampleShopifyOrder.new
    default_data = TestColumnMappingData.new
    puts e.message
    puts e.backtrace.join("\n")
    Airbrake.notify(e, {source: self})
    {status: false, data: default_data, source_mappings: source_mappings, sample_shopify_order: default_sample_shopify_order, remark: e.message}
  end
end

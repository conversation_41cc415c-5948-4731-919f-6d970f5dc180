class Source < ApplicationRecord
  include Templatable

  self.inheritance_column = "platform"

  require "csv"
  mount_uploader :source_file, SourceFileUploader

  has_many :fulfillment_logs
  has_many :sync_logs, dependent: :destroy
  has_many :file_replacements, dependent: :destroy
  has_many :email_logs
  belongs_to :shop, optional: true

  attr_encrypted :source_password, key: ENV["PASSWORD_ENCRYPTION_KEY"], algorithm: "aes-256-cbc", mode: :single_iv_and_salt, insecure_mode: true

  validates :name, presence: true
  validates :schedule_type, presence: true, if: :needs_scheduler?
  validates :schedule_interval, presence: true, if: :scheduled_hourly?
  validates :order_no_mapping, presence: true, numericality: {only_integer: true}
  validates :sku_mapping, numericality: {only_integer: true}, allow_blank: true
  validates :quantity_mapping, numericality: {only_integer: true}, allow_blank: true
  validates :tracking_no_mapping, numericality: {only_integer: true}, allow_blank: true
  validates :tracking_company_mapping, numericality: {only_integer: true}, allow_blank: true
  validates :fulfillment_status, presence: true

  scope :active, -> { where(sync_status: "started") }
  scope :syncable, ->(sync_time) { active.where("(next_schedule_time < ?) or (last_processing_time < ?)", sync_time, sync_time - 1.day) }

  accepts_nested_attributes_for :file_replacements, allow_destroy: true

  after_create do
    update(ignore_empty_sku: false) if sku_mapping.nil?
  end

  before_save do
    self.location_id = shop.primary_location_id if shop && location_id.blank?
    unless source_host.blank?
      self.source_host = source_host.sub(%r{^ftp://}, "")
    end
    unless source_parent_path.blank?
      self.source_parent_path = source_parent_path.sub(%r{(/*)$}, "/")
    end
    set_schedule_to_email_if_source_type_email
    if needs_scheduler?
      if changed.include?("schedule_time") && schedule_started?
        self.next_schedule_time = find_next_schedule_time(Time.zone.now)
      end
    else
      self.sync_status = "paused"
    end

    self.schedule_interval = 10 if schedule_interval.nil?
    self.platform = shop.provider.underscore if shop
  end

  before_update :broadcast_progress, if: :status_changed?

  # sync status = status to control sync, for user/system to control
  state_machine :sync_status, initial: :paused do
    after_transition on: :started do |source, _transition|
      schedule_time = source.find_next_schedule_time(Time.zone.now)
      source.update_next_schedule_time(schedule_time)
    end

    after_transition on: :paused do |source, _transition|
      source.next_schedule_time = nil
      source.save
    end

    event :started do
      transition all => :started
    end

    event :paused do
      transition all => :paused
    end
  end

  def self.find_sti_class(type_name)
    "Source::#{type_name.camelize}".constantize
  rescue NameError, TypeError
    super
  end

  def assign_defaults
    self.name = "Source #{shop ? shop.sources.count + 1 : "Default Source"}"
    self.source_host = "localhost" if source_host.blank?
    self.schedule_type = "hourly" if schedule_type.blank?
    self.schedule_interval = 6
    self.order_no_mapping = "0"
  end

  # assigns email after create
  def assign_source_email
    if email.blank?
      shop_name = shop.name.to_s.split(".")[0]
      self.email = "#{shop_name}#{Time.now.to_i}@#{Settings.profile_email_domain}"
    end
  end

  def set_schedule_to_email_if_source_type_email
    self.schedule_type = "on_email_received" if source_type == "email"
  end

  def find_next_schedule_time(initial_time = Time.now.utc)
    case schedule_type
    when "hourly"
      initial_time + schedule_interval.hours
    when "daily"
      if shop
        now = ActiveSupport::TimeZone.new(shop.timezone).now
        time = ActiveSupport::TimeZone.new(shop.timezone).parse(schedule_time)

        if schedule_day == "weekday"
          if now.friday?
            time += 3.days
          elsif now.saturday?
            time += 2.day
          elsif now.sunday?
            time += 1.day
          elsif now.to_i > time.to_i
            time += 1.day
          end
        elsif now.to_i > time.to_i
          time += 1.day
        end
        time
      end
    end
  end

  def assigned_source_password
    if template_id
      source = Source.find(template_id)
      return source.source_password if source
    end
    source_password
  end

  def set_progress(number, total = 100)
    progress = 0
    pre_percentage = 8.0
    if number.is_a? Symbol
      case number
      when :progress_source
        progress = 3.0
      when :progress_orders
        progress = pre_percentage
      when :finish
        progress = 100.0
      end
      self.progress = progress
      broadcast_progress
    else
      progress = ((number.to_f / (total.to_f / (100.0 - pre_percentage.to_f)))).round
      if progress > self.progress + 5
        self.progress = progress
        broadcast_progress
      end
    end
  end

  def update_next_schedule_time(schedule_time)
    self.next_schedule_time = schedule_time
    save
  end

  def deactivate
    paused
  end

  def needs_scheduler?
    !%w[file_upload email].include?(source_type)
  end

  def scheduled_hourly?
    schedule_type == "hourly" && needs_scheduler?
  end

  def update_sync_status(is_active)
    is_active ? started : paused
  end

  def build_file_reader
    @reader ||= FileReaderFactory.get_file_reader(source_type.to_sym,
      file: source_file,
      url: source_url,
      host: source_host,
      login: source_login,
      password: source_password,
      path_to_file: path_to_file,
      ftp_mode: ftp_mode,
      source_file_host: source_file_host,
      process_type: source_process,
      file_rename: source_rename,
      ssh_key: ssh_key,
      email_logs: get_email_log,
      timezone: shop.timezone,
      encoding: file_encoding,
      google_sheet_name: google_sheet_name)
  end

  def get_email_log
    email_logs.order("created_at desc").first
  end

  def process_file
    file_reader = build_file_reader
    file_reader.process_file
  end

  def download_file(file_reader, first_only = false)
    is_google_sheet = file_reader.instance_of?(GoogleSheetFileReader)
    # is_smiffys_rest_api = file_reader.instance_of?(SmiffysFileReader)
    data = file_reader.read(first_only)
    file_name = file_reader.full_file_path
    downloaded_data = data[:data]

    config = {headers: false}

    config[:col_sep] = column_separator unless column_separator.nil?
    unless row_separator.blank?
      config[:row_sep] = Settings.row_separators[row_separator].match
    end

    if data[:data]
      unless [GoogleSheetFileReader, SmiffysFileReader, WholecellFileReader, MstgolfFileReader, BigcaringFileReader].include?(file_reader.class)
        data[:data] = FileParser.new(self, file_name, downloaded_data, config).parse
      end
      if first_only && data[:data].first.is_a?(Array)
        data[:data] = data[:data].first
      end
    else
      data[:data] = []
    end
    data[:file_name] = file_reader.final_file_name unless is_google_sheet
    data
  end

  def guided_mapping
    download_file(build_file_reader, true)
  end

  # only debug one order
  def debug_orders(all_orders = false)
    FulfillmentWorker.new(self).debug_orders(all_orders)
  end

  def list_orders
    FulfillmentWorker.new(self).list_orders
  end

  def sync_fulfillments(trigger = :undefined, update_mode = false)
    if shop.failure_count > 4
      deactivate
      return {status: false, error: "Disabled schedule because shop can't reach"}
    end
    result = FulfillmentWorker.new(self).call(trigger, update_mode)
    if result[:status] == "running" || shop.notification_email.blank?
      return result
    end

    if result[:status] == "success"
      shop.send_email_notification(:sync_success, result, self)
    elsif result[:status] == "duplicate_runs"
      # do nothing
    elsif result[:error] && (result[:error].index("Please check file name") == 0)
      shop.send_email_notification(:sync_file_not_found, result, self)
    else
      shop.send_email_notification(:sync_failure, result, self)
    end
    result
  end

  def schedule_started?
    sync_status == "started" && has_schedule_settings?
  end

  def has_schedule_settings?
    return false if source_type == "file_upload"

    schedule_interval.present? || schedule_time.present?
  end

  def update_fulfillments(sync_time, caller = :scheduler, update_mode = false)
    if caller == :scheduler
      log = sync_logs.order("id desc").first
      if log && (sync_time - 10.minutes < log.created_at) && (log.created_at < sync_time + 10.minutes)
        return {status: "duplicate_runs"}
      end
    end
    result = sync_fulfillments(caller, update_mode)
    return result if result[:status] == "running"

    if status == "error"
      Rails.logger.info "error:"
      Rails.logger.info result[:error]
    end
    next_schedule_time = has_schedule_settings? ? find_next_schedule_time(sync_time) : nil
    update(
      last_processing_time: Time.now,
      next_schedule_time: next_schedule_time,
      status: result[:status]
    )
    result
  end

  def need_after_process?
    !((source_process == "none") || source_process.blank?)
  end

  def set_queuing
    update(status: "queuing")
  end

  def broadcast_progress(latest_log = nil, label_name = nil)
    sync_log = nil

    @latest_log ||= sync_logs.order("id desc").first
    if @latest_log
      sync_log = {
        id: @latest_log.id.to_s,
        error_message: @latest_log.error_message,
        processed_at: @latest_log.processed_at,
        status: @latest_log.status,
        number_fulfillment_updated: @latest_log.number_fulfillment_updated,
        caller: @latest_log.caller
      }
    end

    payload = {
      id: id.to_s,
      name: name,
      status: status,
      source_type: source_type,
      updated_at: updated_at,
      schedule_type: schedule_type,
      schedule_time: schedule_time,
      schedule_interval: schedule_interval,
      sync_status: sync_status,
      sync_log: sync_log,
      progress: ((status == "running") ? progress : 0)
    }

    puts "boardcast #{payload[:progress]} #{progress}"
    params = {
      action: label_name || "update_status",
      payload: payload
    }
    if shop
      connections = shop.active_websocket_connections
      puts "connection #{connections.inspect}"
      shop.ws_update_progress(connections, params)
    end
  end
end

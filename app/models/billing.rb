class Billing < ApplicationRecord
  belongs_to :shop

  def create_refund(refund_value)
    credit_params = {
      description: "#{plan_name} Refund Credit",
      amount: refund_value,
      test: Rails.env.development?
    }

    shop.create_refund(credit_params)
  end

  def check_subscription_status
    return false unless shop_name.index(".myshopify.com")
    return true if status === "active"
    is_credits_sales = plan_name.index("Credit")
    data = is_credits_sales ? shop.get_one_time_charge(charge_id) : shop.get_billing(charge_id)
    return false unless data.status === "active"
    if is_credits_sales
      # Add credits
      credits = data.return_url.split("credit=").last.to_i
      if credits
        shop.increment!(:credit, credits)
        update(status: data.status)
      end
    else
      # Update subscription
      plan_name = data.return_url.split("package=").last
      package = Package.new(plan_name)
      unless plan_name == shop.package
        package.activate(shop, data.id.to_s)
        update(status: data.status)
      end
    end
    true
  end
end

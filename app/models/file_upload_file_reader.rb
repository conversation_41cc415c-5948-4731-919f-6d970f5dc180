require "open-uri"
class FileUploadFileReader < FileReader
  def self.attr_list
    %i[file file_path]
  end

  attr_accessor(*attr_list)

  def initialize(opts)
    @file = opts[:file]
    @encoding = opts[:encoding]
    @source_file_host = opts[:source_file_host]
    @file_path = @file.path
    @file_name = @file.filename
    @final_file_name = process_file
  end

  def get_data(first_only = false)
    if @file_path.nil?
      raise 'Please proceed with "Start Upload" or "Sync Now" to test column mapping.'
    end

    FeedTransferer.transfer_file(@file, @source_file_host)

    if @encoding.blank?
      if first_only
        File.readlines(@file_path)[0]
      else
        File.read(@file_path)
      end
    elsif first_only
      File.new(@file_path, @encoding).readline
    else
      File.new(@file_path, @encoding).read
    end
  end

  def process_file
    @file_path ? @file_path.split("/").last : "N/A"
  end
end

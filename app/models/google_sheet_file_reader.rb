require "google/apis/sheets_v4"

class GoogleSheetFileReader < FileReader
  def initialize(opts = {})
    @source_url = opts.fetch(:url)
    @google_sheet_name = opts.fetch(:google_sheet_name)
    @source_id = opts[:file].model.id
    @spreadsheet_id = get_spreadsheet_id(@source_url)
    @sheets_service = login
  end

  def process_file
    file_name
  end

  def file_name
    @google_sheet_name
  end

  def final_file_name
    file_name
  end

  def read(first_only = false)
    data = get_data(first_only)
    {data: data, status: true}
  end

  def get_data(first_only = false)
    return nil unless @sheets_service.present?

    arrays = []
    begin
      spreadsheet = @sheets_service.get_spreadsheet(@spreadsheet_id)
      sheet_names = @google_sheet_name.split(",").collect(&:strip)
      sheets = spreadsheet.sheets.select { |a| sheet_names.include?(a.properties.title.strip) }
      sheets = [spreadsheet.sheets.first] if sheets.empty?
      if first_only
        result = @sheets_service.get_spreadsheet_values(@spreadsheet_id, "#{sheets.first.properties.title}!1:1")
        arrays << result.values if result.values
      else
        sheets.each do |sheet|
          sheet_name = sheet.properties.title
          result = @sheets_service.get_spreadsheet_values(@spreadsheet_id, sheet_name)
          arrays << result.values if result.values
        end
      end
      arrays = arrays.flatten(1)
    rescue Google::Apis::ClientError => e
      if e.message.index "This operation is not supported for this document"
        Rails.logger.debug "FILE have to save as Google Sheet"
        raise StandardError.new("Please save this link as Google Sheet")
      elsif e.message.include?("Requested entity was not found.")
        Rails.logger.error "Google Sheet not found: #{e.message}"
      else
        Airbrake.notify(e, "Google Sheet - SourceId: #{@source_id} #{e.message}")
      end
    rescue => e
      Rails.logger.debug e.message
      Rails.logger.debug e.backtrace.join("\n")
      Airbrake.notify(e, "Google Sheet - SourceId: #{@source_id} #{e.message}")
    end
    arrays
  end

  def login
    ENV["GOOGLE_APPLICATION_CREDENTIALS"] = Rails.root.join("uptracker_google_service.json").to_s
    scope = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
    authorization = Google::Auth.get_application_default(scope)
    sheets_service = Google::Apis::SheetsV4::SheetsService.new
    sheets_service.authorization = authorization

    sheets_service
  rescue => e
    Airbrake.notify(e, "GOOGLE File issue - SourceId: #{@source_id} #{e.message}")
    nil
  end

  private

  def get_spreadsheet_id(url)
    begin
      uri = URI.parse(url.strip)
      uri_params = CGI.parse(uri.query)
      url_key = uri_params["key"][0]
    rescue => _
    end
    unless url_key
      # for special case no key parameters in url
      uri_array = uri.path.split("/")
      url_key = uri_array[3] if uri_array[1] == "spreadsheets"
      url_key = uri_array[5] if url_key.blank?
      url_key = uri_array[3] if url_key.blank?
    end
    url_key
  end
end

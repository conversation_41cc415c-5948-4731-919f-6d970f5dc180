class BigCommerce < Shop
  include BigCommerceClient

  def self.auth
    @auth ||= EcwidApi::OAuth.new do |config|
      config.client_id = ENV["ECWID_CLIENT_ID"]
      config.client_secret = ENV["ECWID_CLIENT_SECRET"]
      config.redirect_uri = Settings.ecwid.redirect_uri
      config.scope = "read_orders, update_orders"
    end
  end

  def self.get_authorization_url
    auth.oauth_url
  end

  def self.from_omniauth(auth)
    shop_domain = auth[:shop].gsub("http://", "").gsub("https://", "").delete("/")
    shop = where(bigcommerce_domain: shop_domain).first_or_initialize do |shop|
      shop.provider = "BigCommerce"
      shop.bigcommerce_domain = shop_domain
      shop.installed_at = Time.now
      shop.uninstalled_at = nil
    end

    shop.save

    Airbrake.notify("user error while oauth", {shop: shop}) unless shop.valid?

    shop
  end

  def self.from_callback(params)
    store_hash = params.context.split("/").last
    shop = where(bigcommerce_store_hash: store_hash).first_or_initialize do |shop|
      shop.provider = "BigCommerce"
      shop.bigcommerce_access_token = params.access_token
      shop.update_shop_info
      shop.email = params.user.email
      shop.notification_email = shop.email if shop.notification_email.blank?
      shop.bigcommerce_client_id = ENV["BIGCOMMERCE_CLIENT_ID"]
      shop.installed_at = Time.now
      shop.uninstalled_at = nil
    end

    if shop.save
      user = User.where(email: params.user.email, shop_id: shop.id).first_or_initialize do |user|
        user.password = Devise.friendly_token[0, 20]
        user.save
        UserMailer.welcome_email(shop.email, shop.bigcommerce_domain, shop.provider).deliver_now
      end
      shop.user_id = user.id
      shop.save
    end
    Airbrake.notify("user error while oauth", {shop: shop}) unless shop.valid?

    shop
  end

  def update_shop_info
    info = get_shop_information
    return nil unless info

    self.timezone = info[:timezone][:name] if info[:timezone]
    self.bigcommerce_domain = info[:domain]

    save if changed?
  end

  def store_domain
    bigcommerce_domain
  end

  def connected
    bigcommerce_store_hash.present? && bigcommerce_client_id.present? && bigcommerce_access_token.present?
  end
end

require "open-uri"
class UrlFileReader < FileReader
  def self.attr_list
    %i[url file_path]
  end

  def final_file_name
    @url
  end

  attr_accessor(*attr_list)

  def initialize(opts)
    url = opts[:url]
    url = if url.index("?dl=").nil? && url.index("dropbox")
      url + "?dl=1"
    else
      url.gsub("?dl=0", "?dl=1")
    end
    @url = url
    @file_path = @url
    @login = opts[:login]
    @password = opts[:password]
    @timezone = opts[:timezone]
  end

  def get_data(first_only = false)
    @url = FileReader.parse_url(@url, @timezone ? ActiveSupport::TimeZone.new(@timezone).now : Time.now)
    @file_path = @url
    uri = URI.parse(@url)
    if @login.present? && @password.present?
      if first_only
        open(@url, http_basic_authentication: [@login, @password]).readline
      else

        # http = Net::HTTP.new(uri.host, uri.port)
        # request = Net::HTTP::Get.new(uri.request_uri)
        # request.basic_auth(@login, @password)        
        faraday = Faraday.new(uri, request: { timeout: 5 }) 
        faraday.use Faraday::Request::BasicAuthentication, username, password
        faraday.request :digest, username, password
        response = faraday.get
        response.body

        # response = http.request(request)
        # response.body
        
      end
    elsif first_only
      open(@url).readline
    else
      
      conn = Faraday.new(uri, request: { timeout: 5 }) 
      #open(@url).read
      res = conn.get 
      res.body

    end
  end

  def process_file
    @file_path
  end
end

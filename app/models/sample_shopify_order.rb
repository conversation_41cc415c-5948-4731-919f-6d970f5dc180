class SampleShopifyOrder
  def self.from_shopify_order(shopify_order)
    return new unless shopify_order.is_a?(ShopifyAPI::Order)

    args = {
      id: shopify_order.id,
      number: shopify_order.number,
      order_name: shopify_order.name,
      order_number: shopify_order.order_number
    }
    new(args)
  end

  attr_reader :id, :number, :order_name, :order_number

  def initialize(data = {})
    @id = data[:id]
    @order_name = data[:order_name]
    @order_number = data[:order_number]
    @number = data[:number]
  end
end

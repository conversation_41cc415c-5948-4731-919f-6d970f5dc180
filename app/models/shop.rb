class Shop < ApplicationRecord
  belongs_to :user, optional: true
  has_many :sources
  has_many :fulfillment_logs
  has_many :sync_logs
  has_many :billings
  has_many :users
  has_many :orders
  has_secure_token :api_token
  serialize :email_subscriptions, Array
  self.inheritance_column = :provider

  def email_required?
    false
  end

  def email_changed?
    false
  end

  # use this instead of email_changed? for rails >= 5.1
  def will_save_change_to_email?
    false
  end

  def display_name
    shopify_domain
  end

  def platform
    provider.underscore
  end

  def expired_day
    days = Settings.free_trial_days - (Time.zone.now.to_date - installed_at.to_date).to_i
    days = 0 if days < 0
    days
  end

  def store_domain
    shopify_domain.to_s unless shopify_domain.blank?
    if bigcommerce_domain.blank?
      ecwid_store_url.to_s
    else
      bigcommerce_domain.to_s
    end
  end

  def active?
    return false if uninstalled_at
    return true if use_credit
    return true if charge_id.present?
    return false if installed_at + Settings.free_trial_days.days < Time.now

    true
  end

  def allow_new_source?
    return true if use_credit

    package == "pro"
  end

  def source_limit_reach?
    source_limit <= sources.count
  end

  def any_running?
    sources.where(status: "running").count > 0
  end

  def should_upgrade?
    return false if charge_id.present?

    true
  end

  def deactivate_shop
    sources.each(&:deactivate)
  end

  def reset_payment
    self.charge_id = nil
    self.charged_at = nil
    self.package = "trial"
    self.source_limit = 5
    self.stripe_subscription_id = nil
    self.use_credit = true
    save
    UserMailer.billing_revoke(self).deliver if email
  end

  def update_details
    return if domain.present? && email.present?

    shop = get_shop_information
    self.email = shop.email
    self.domain = shop.domain
    save!
  rescue => _
    # do nothing
  end

  def name
    shopify_domain
  end

  def name=(value)
    self.shopify_domain = value
  end

  def email_subscribed_to?(subscription = nil)
    email_subscriptions.include?(subscription.to_s)
  end

  def connected
    true
  end

  def email_notification_time
    Time.zone = timezone
    current_time = Time.zone.now

    if email_notification_custom_time_enabled && email_notification_start_time.present? && email_notification_end_time.present?
      start_time = Time.zone.parse(email_notification_start_time)
      end_time = Time.zone.parse(email_notification_end_time)

      if current_time < start_time
        start_time
      elsif current_time > end_time
        start_time + 1.days
      else
        current_time
      end
    else
      current_time
    end
  end

  def total_spent
    billings.select("sum(total_charge) as total").where(status: "success", refunded: false)[0][:total]
  end

  def send_email_notification(notification_type, result = nil, source = nil)
    if email_subscribed_to?(notification_type)
      case notification_type
      when :sync_success
        UserMailer.notify_sync_success(self, result, source).deliver_later(wait_until: email_notification_time)
      when :sync_file_not_found, :sync_failure
        UserMailer.notify_sync_failure(self, result, source).deliver_later(wait_until: email_notification_time)
      end
    end
  end

  def active_websocket_connections
    @dynamodb ||= AwsServices.dynamodb_client
    # Currently do simple scan due to small object size
    # Have to take lastEvaluatedKey to consideration if needed.
    resp = @dynamodb.scan({
      expression_attribute_values: {
        ":shopId" => id.to_s
      },
      filter_expression: "shopId = :shopId",
      projection_expression: "connectionId",
      table_name: ENV["DYNAMODB_TABLE_NAME"]
    })

    resp.items.map { |item| item["connectionId"] }
  rescue => _
    []
  end

  def ws_update_progress(connection_ids, data)
    connection_ids.each do |connection_id|
      broadcast_progress_to_websocket(connection_id, data)
    end
  end

  def broadcast_progress_to_websocket(connection_id, data)
    return false unless Settings.enable_broadcast_websocket

    @api_gateway_client ||= AwsServices.api_gateway_client
    @api_gateway_client.post_to_connection({
      data: data.to_json,
      connection_id: connection_id
    })
  rescue => e
    # do nothing
    Rails.logger.error "error while broadcasting to: #{connection_id}: #{e.message}  #{e.class}"

    delete_connection_from_dynamodb(connection_id)
  end

  def delete_connection_from_dynamodb(connection_id)
    @dynamodb ||= AwsServices.dynamodb_client
    @dynamodb.delete_item({
      key: {
        connectionId: connection_id
      },
      table_name: ENV["DYNAMODB_TABLE_NAME"]
    })
  rescue => e
    # do nothing
    Rails.logger.error "error while deleting connection #{connection_id}: #{e.message} #{e.class}"
    Airbrake.notify("error while deleting connection #{connection_id}: #{e.messagitge}")
  end
end

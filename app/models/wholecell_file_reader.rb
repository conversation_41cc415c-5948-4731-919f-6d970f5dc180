class WholecellFileReader < FileReader
  BASE_URL = "https://api.wholecell.io"
  def self.attr_list
    %i[username password source_id connection]
  end

  attr_accessor(*attr_list)

  def initialize(opts)
    @username = opts[:file].model.source_login
    @password = opts[:file].model.source_password
    @source_id = opts[:file].model.id
    @connection = Faraday.new(url: BASE_URL) do |conn|
      conn.adapter Faraday.default_adapter
      conn.basic_auth(username, password)
    end
  end

  def get_data(first_only = false)
    tries ||= 3
    result = []
    page = 1
    begin
      loop do
        begin
          response = connection.get("/api/v1/sales_orders?page=#{page}")
          response = JSON.parse(response.body)
        rescue
          if response.body.index("Throttled")
            tries -= 1
            sleep(1)
            retry
          end
          {}
        end
        orders = response["data"]
        last_page = response["pages"] || 1

        break if orders.blank?

        orders.map do |order|
          data = []
          data << order["transaction_number"]
          data << get_shipment_tracking(order["id"])
          result << data
        end

        break if first_only || page >= last_page
        page += 1
      end
    rescue => e
      Airbrake.notify(e, "Source: #{source_id}, #{e.message}")
    end
    result
  end

  def get_shipment_tracking(order_number)
    tries ||= 3
    begin
      response = connection.get("/api/v1/sales_orders/#{order_number}/shipments")
      shipments = JSON.parse(response.body)["data"]
    rescue
      if response.body.index("Throttled")
        tries -= 1
        sleep(1)
        retry
      end
      []
    end
    puts "shipments: #{shipments}"
    return shipments.first["tracking_number"] if shipments.present?
    ""
  end
end

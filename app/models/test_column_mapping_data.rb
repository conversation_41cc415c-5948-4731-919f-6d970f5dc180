class TestColumnMappingData
  attr_reader :order_no, :item_sku, :item_quantity, :tracking_no, :tracking_company, :tracking_url, :ignore_column
  def initialize(data = {})
    @order_no = data[:order_no]
    @item_sku = data[:item_sku]
    @item_quantity = data[:item_quantity]
    @tracking_no = data[:tracking_no]
    @tracking_company = data[:tracking_company]
    @tracking_url = data[:tracking_url]
    @ignore_column = data[:ignore_column]
  end
end

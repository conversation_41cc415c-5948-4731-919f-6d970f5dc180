module Templatable
  extend ActiveSupport::Concern

  def duplicate(opts = {})
    klass = opts.delete(:target_class) || Source
    template_id = opts.delete(:template_id)
    shop = opts.delete(:shop)

    profile_name = opts.delete(:name) || "Copy of #{name}"

    template = klass.create(attributes.except(*source_exclude_attributes)) do |t|
      t.name = profile_name
      t.template_id = template_id
      t.platform = klass.sti_name
      t.shop_id = shop.id if shop
      t.source_password = source_password if klass == Source::ShopifyTemplate
    end
    Rails.logger.info template.errors.inspect

    template
  end

  def make_as_template(opts = {})
    duplicate(opts.merge!(target_class: Source::ShopifyTemplate))
  end

  def create_source_from_template(opts = {})
    duplicate(opts.merge!(target_class: Source, template_id: id))
  end

  private

  def source_exclude_attributes
    %w[id shop_id]
  end
end

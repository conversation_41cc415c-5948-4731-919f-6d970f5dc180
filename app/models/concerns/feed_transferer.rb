class FeedTransferer
  def self.transfer_file(file, source_file_host)
    # don't transfer if it's not job env grabbing files
    return true unless Rails.env.production?

    new(file, source_file_host).perform
  end

  attr_accessor :file

  def initialize(file, source_file_host)
    @file = file
    @source_file_host = source_file_host.to_s
    if @source_file_host.index("web1")
      @host = "private-web1.stock-sync.com"
      @second_host = "private-web2.stock-sync.com"
    elsif @source_file_host.index("web2")
      @host = "private-web2.stock-sync.com"
      @second_host = "private-web1.stock-sync.com"
    else
      @host = "private-web1.stock-sync.com"
      @second_host = "private-web2.stock-sync.com"
    end
  end

  def perform
    file_path = if @file.is_a? String
      @file
    else
      @file.path
    end
    return false if file_path.blank?

    dest_file = file_path
    original_file = file_path

    dest_folder = dest_file.split("/")
    dest_folder.pop
    dest_folder = dest_folder.join("/")
    transfer(original_file, dest_folder, dest_file)
  end

  private

  def transfer(original_file, dest_folder, dest_file)
    tries ||= 3
    `mkdir -p #{dest_folder}`
    if @source_file_host != Socket.gethostname
      begin
        download(@host, original_file, dest_file)
      rescue Net::SCP::Error => e
        download(@second_host, original_file, dest_file)
      end
    end
    true
  rescue Errno::ENOENT => e
    # file not found
    if e.message.index("No such file or directory")
      # assump user upload another file and the original file being replace
    else
      Rails.logger.error("FilePath: #{original_file} #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
    end
    false
  rescue => e
    tries -= 1
    if tries > 0
      sleep 1.seconds
      retry
    else
      Rails.logger.error("Exception: #{@file} #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      false
    end
  end

  private

  def download(host, orig_file, dest_file)
    puts "SCP Download #{host}, #{Settings.web_server_username}, #{orig_file}, #{dest_file}"
    Net::SCP.download!(host, Settings.web_server_username, orig_file, dest_file)
    puts "file exist: #{File.exist?(dest_file)} #{dest_file}"
    puts "File size: #{File.size(dest_file)}"
  end
end

module BigCommerceClient
  extend ActiveSupport::Concern

  def create_session
    Bigcommerce.configure do |config|
      config.store_hash = bigcommerce_store_hash
      config.client_id = bigcommerce_client_id
      config.access_token = bigcommerce_access_token
    end
  end

  def get_shop_information
    create_session
    Bigcommerce::StoreInfo.info
  end

  def find_order(id)
    create_session
    Bigcommerce::Order.find(id)
  end

  def get_locations
    []
  end

  def update_shipment(shipments, tracking_number)
    bigcommerce_shipment = shipments.first
    Bigcommerce::Shipment.update(
      shipment.order_id,
      shipment.id,
      tracking_number: tracking_number,
      order_address_id: bigcommerce_shipment.order_address_id
    )
  end

  def order_shipment(order_id)
    Bigcommerce::Shipment.all(order_id)
  rescue Bigcommerce::NotFound => _
    nil
  end

  def update_order_status(order_address_id)
    # update order status shipped
    Bigcommerce::Order.update(order_address_id, status_id: 2)
  end
end

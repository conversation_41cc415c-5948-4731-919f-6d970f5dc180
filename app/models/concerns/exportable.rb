module Exportable
  extend ActiveSupport::Concern

  class_methods do
    def to_csv(options = {})
      opts = {
        platform: options.delete(:platform) || "shopify"
      }
      columns = export_columns(opts)

      column_names = columns.keys
      CSV.generate(**options) do |csv|
        csv << columns.values
        all.each do |log|
          csv << log.attributes.values_at(*column_names.map(&:to_s))
        end
      end
    end
  end
end

module ShopifyClient
  extend ActiveSupport::Concern

  attr_accessor :session

  ### ok ###
  def create_session
    @session ||= ShopifyAPI::Auth::Session.new(shop: shopify_domain, access_token: shopify_token)
    ShopifyAPI::Context.activate_session(@session)
    @session
  end

  ### ok ###
  def clear_session
    ShopifyAPI::Context.deactivate_session
  end

  ### ok ###
  def get_shop_information
    create_session unless session
    begin
      ShopifyAPI::Shop.all.first # returns an array of Shop
    rescue ShopifyAPI::Errors::HttpResponseError => e
      err = {error_message: e.message.to_s, id: id}
      Airbrake.notify("GET SHOP Info: (#{id}", **err)
      Rails.logger.debug(e.message)
      Rails.logger.debug(e.backtrace.join("\n"))
      nil
    end
  end

  def get_shop_scopes
    create_session unless session
    ShopifyAPI::AccessScope.all # returns an array of Shop
  end

  ### ok ###
  def find_order(shopify_order_id)
    create_session unless session
    ShopifyAPI::Order.find(id: shopify_order_id)
  end

  ### ok ###
  def find_order_by_name(name, opts = {})
    create_session unless session
    params = {status: "any", name: name}.merge(opts)
    orders = ShopifyAPI::Order.all(**params)
    (orders.length > 0) ? orders : []
  end

  def get_fulfillment_order(order_id)
    tries = 3
    begin
      create_session unless session
      fo = ShopifyAPI::FulfillmentOrder.all(order_id: order_id)
      return fo
    rescue => e
      return [] if e.message.index("Payment Required")

      tries -= 1
      if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
        sleep 1.second
        retry
      else
        err = {error_message: e.message, order_id: order_id}
        Airbrake.notify("get_fulfillment_order", **err)
      end
    end
    []
  end

  ### ok ###
  def get_locations
    tries = 3
    begin
      create_session unless session
      return ShopifyAPI::Location.all
    rescue => e
      if e.message.index("Payment Required")
        increment!(:failure_count)
        return []
      end

      tries -= 1
      if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
        sleep 1.second
        retry
      else

        increment!(:failure_count)
        err = {error_message: e.message.to_s}
        Airbrake.notify("get_locations: #{id}", **err)
      end
    rescue ShopifyAPI::Errors::HttpResponseError => e
      increment!(:failure_count)
      Rails.logger.error "get_locations: #{e.message} #{id}"
    end
    []
  end

  def get_orders(order_status, financial_status, fulfillment_status, limit = 1, created_at_min = nil, ids = [], next_page_info = nil)
    tries = 3
    create_session unless session
    if next_page_info
      params = {}
      params[:page_info] = next_page_info
    else
      params = {financial_status: financial_status, fulfillment_status: fulfillment_status, limit: limit}
      params[:created_at_min] = created_at_min if created_at_min
      params[:status] = order_status unless order_status.blank?
    end
    if ids.present?
      shopify_orders = []
      ids.each_slice(250) do |batch|
        params[:ids] = batch.join(",")
        temp = []
        begin
          temp = ShopifyAPI::Order.all(**params)
        rescue => e
          tries -= 1
          if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
            sleep 1.second
            retry
          else
            Airbrake.notify("get_orders_with_ids: #{e.message}")
          end
        rescue => e
          Airbrake.notify("get_orders_with_ids: #{e.message}")
        rescue ShopifyAPI::Errors::HttpResponseError => e
          Airbrake.notify("get_orders_with_ids: #{e.message}")
        end
        shopify_orders += temp
      end
      return shopify_orders
    else
      begin
        return ShopifyAPI::Order.all(**params)
      rescue => e
        tries -= 1
        if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
          sleep 1.second
          retry
        else
          Airbrake.notify("get_orders: #{e.message}")

        end
      rescue ShopifyAPI::Errors::HttpResponseError => e
        Airbrake.notify("get_orders: #{e.message}")
      end
    end
    []
  end

  ### ok ###
  def total_orders(financial_status, fulfillment_status, created_at_min = nil)
    params = {status: "any", financial_status: financial_status, fulfillment_status: fulfillment_status}
    params[:created_at_min] = created_at_min if created_at_min
    ShopifyAPI::Order.count(**params).body.fetch("count")
  end

  def next_page(orders)
    tries = 3
    begin
      orders.fetch_next_page
    rescue => e
      tries -= 1
      if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
        sleep 1.second
        retry
      else
        Airbrake.notify("next_page: #{e.message}")
        raise e
      end
    end
  end

  def all_orders
    shopify_orders = []
    limit = 250
    page = 1

    current_page_orders = get_orders("any", "paid", "any", limit, page, 6.months.ago)
    loop do
      shopify_orders += current_page_orders
      Rails.logger.info "load orders - #{page} #{shopify_orders.count}"
      if ShopifyAPI::Order.next_page?
        current_page_orders = next_page(current_page_orders)
        page += 1
      else
        break
      end
    end
    shopify_orders.flatten
  end

  def close_order(order)
    success = false
    error_message = nil

    tries = 5
    begin
      success = order.close
      error_message = object.errors.full_messages.to_sentence unless success
    rescue => e
      tries -= 1
      if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
        sleep 1.second
        retry
      else
        Airbrake.notify("close_order: #{e.message}")
      end
    end

    {success: success, error_message: error_message}
  end

  def save_object(object)
    success = false
    error_message = nil

    tries = 5
    begin
      success = object.save!
      error_message = object.errors.full_messages.to_sentence unless success
    rescue => e
      tries -= 1
      if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
        sleep 1.second
        retry
      else
        Airbrake.notify("save_object: #{e.message}")
      end
    end

    {success: success, error_message: error_message}
  end

  def save_fulfillment(shopify_fulfillment)
    success = true
    error_message = nil
    fulfillment_id = nil
    f = {fulfillment: shopify_fulfillment}
    tries = 5
    result = nil
    begin
      # sample result
      # {"data"=>{"fulfillmentCreateV2"=>{"fulfillment"=>{"id"=>"gid://shopify/Fulfillment/4476152250413", "status"=>"SUCCESS"}, "userErrors"=>[]}}, "extensions"=>{"cost"=>{"requestedQueryCost"=>10, "actualQueryCost"=>10, "throttleStatus"=>{"maximumAvailable"=>1000.0, "currentlyAvailable"=>990, "restoreRate"=>50.0}}}}

      result = ShopifyAPI::LightGraphQL.query(GraphqlHelper.fulfillment_create(f))
      if result && result["errors"]
        error_message = result["errors"].first.try(:[], "extensions").try(:[], "problems").first.try(:[], "message")
        success = false
      elsif result
        fulfillment_result = result["data"]["fulfillmentCreate"]["fulfillment"]
        if fulfillment_result && fulfillment_result["status"] == "SUCCESS"
          fulfillment_id = fulfillment_result["id"].split("/").last.to_i
        end
        # Sample error
        # {"errors"=>[{"message"=>"Variable $fulfillment of type FulfillmentV2Input! was provided invalid value for trackingInfo.urls.0 (Invalid url 'www.dhl.com', missing scheme)", "locations"=>[{"line"=>1, "column"=>30}], "extensions"=>{"value"=>{"trackingInfo"=>{"company"=>"DHL", "numbers"=>["25678738"], "urls"=>["www.dhl.com"]}, "lineItemsByFulfillmentOrder"=>[{"fulfillmentOrderId"=>"gid://shopify/FulfillmentOrder/6383346450682", "fulfillmentOrderLineItems"=>[{"id"=>"gid://shopify/FulfillmentOrderLineItem/13535793905914", "quantity"=>1}]}], "notifyCustomer"=>true}, "problems"=>[{"path"=>["trackingInfo", "urls", 0], "explanation"=>"Invalid url 'www.dhl.com', missing scheme", "message"=>"Invalid url 'www.dhl.com', missing scheme"}]}}]}

        error_message = result["data"]["fulfillmentCreate"]["userErrors"].join(",")
      end
    rescue => e
      tries -= 1
      if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
        # trial/development store can create not more than 5 fulfillment per minute
        if e.respond_to? :response
          if e.response.respond_to?(:code) && e.response.code.to_i == 429
            sleep 1.minute
          else
            sleep 1.second
          end
        else
          sleep 1.second
        end
        retry
      else
        success = false
        error_message = "#{e.message} #{e.backtrace.first}"
        error = {order: shopify_fulfillment.inspect.to_s, error_message: e.message, res: JSON.dump(result || {}).to_s}
        Airbrake.notify("save_fulfillment: Shop(#{id})", **error)
      end
    end

    {success: success, fulfillment: fulfillment_id, error_message: error_message}
  end

  def update_fulfillment(order, fulfillment, notify_customer, shipment_status = nil)
    f = if order.is_a?(ShopifyAPI::Fulfillment)
      order
    else
      order.fulfillments.reject { |a| a.status == "cancelled" }.first
    end
    if f
      data = if fulfillment && fulfillment[:trackingInfo]
        {fulfillment_id: f.id, notify_customer: notify_customer, tracking_info: {number: fulfillment[:trackingInfo][:numbers].join(","), company: fulfillment[:trackingInfo][:company], url: fulfillment[:trackingInfo][:urls].first}}
      elsif fulfillment
        tracking_info = {
          number: fulfillment[:tracking_number],
          company: fulfillment[:tracking_company],
          url: fulfillment[:tracking_url]&.first
        }
        {fulfillment_id: f.id, notify_customer: notify_customer, tracking_info: tracking_info}
      else
        {fulfillment_id: f.id, notify_customer: notify_customer}
      end

      if shipment_status && shipment_status != f.shipment_status
        shipment_data = {fulfillmentId: "gid://shopify/Fulfillment/#{f.id}", status: shipment_status}
        create_fulfillment_event(shipment_data)
      end

      # skip save if all tracking info remain the same
      if fulfillment && f.tracking_number.present? && f.tracking_number == data[:tracking_info][:number]
        return {success: true, fulfillment: f}
      end
      tries = 5
      begin
        success = f.update_tracking(fulfillment: data)
        error_message = f.errors.full_messages.to_sentence unless success
      rescue => e
        tries -= 1
        if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
          # trial/development store can create not more than 5 fulfillment per minute
          if e.response.respond_to?(:code) && e.response.code.to_i == 429
            sleep 1.minute
          else
            sleep 1.second
          end
          retry
        else
          error = {order_id: f.order_id, error_message: e.message}
          Airbrake.notify("update_fulfillment: Shop(#{id})", **error)
        end
      end
    else
      return create_fulfillment(fulfillment, notify_customer, nil)
    end
    {success: success, fulfillment: f, error_message: error_message}
  end

  def move_fulfillment(fulfillment_order, location_id)
    tries = 5
    begin
      fulfillment_order.move(fulfillment_order: {new_location_id: location_id, fulfillment_order_line_items: fulfillment_order.line_items})
      return true
    rescue => e
      tries -= 1
      if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
        # trial/development store can create not more than 5 fulfillment per minute
        if e.response.respond_to?(:code) && e.response.code.to_i == 429
          sleep 1.minute
        else
          sleep 1.second
        end
        retry
      else
        error = {loc: location_id, error_message: e.message}
        Airbrake.notify("move_fulfillment: Shop(#{id})", **error)
      end
    end
    false
  end

  def create_fulfillment_event(fulfillment_event)
    success = true
    error_message = nil
    fulfillment_id = nil
    uf = {fulfillmentEvent: fulfillment_event}

    tries = 5
    update_result = nil
    begin
      update_result = ShopifyAPI::LightGraphQL.query(GraphqlHelper.fulfillment_event_create(uf))

      if update_result && update_result["errors"]
        error_message = update_result["errors"].first.try(:[], "extensions").try(:[], "problems").first.try(:[], "message")
        success = false

      elsif update_result
        fulfillment_data = update_result.dig("data", "fulfillmentEventCreate", "fulfillmentEvent")
        if fulfillment_data
          fulfillment_id = fulfillment_data["id"].split("/").last.to_i
        end
      end

      error_message = update_result["data"]["fulfillmentEventCreate"]["userErrors"].join(",")
    rescue => e
      tries -= 1
      if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
        # trial/development store can create not more than 5 fulfillment per minute
        if e.respond_to? :response
          if e.response.respond_to?(:code) && e.response.code.to_i == 429
            sleep 1.minute
          else
            sleep 1.second
          end
        else
          sleep 1.second
        end
        retry
      else
        success = false
        error_message = "#{e.message} #{e.backtrace.first}"
        error = {order: fulfillment_event.inspect.to_s, error_message: e.message}
        Airbrake.notify("create_fulfillment_event: Shop(#{id})", **error)
      end
    end

    {success: success, fulfillment: fulfillment_id, error_message: error_message}
  end

  def create_fulfillment(fulfillment, notify_customer, shipment_status = nil)
    fulfillment = fulfillment.merge({notifyCustomer: notify_customer})
    save_fulfillment(fulfillment)
  end

  def get_one_time_charge(charge_id)
    tries ||= 3
    create_session unless session
    ShopifyAPI::ApplicationCharge.find(id: charge_id)
  rescue ActiveResource::UnauthorizedAccess, ActiveResource::ResourceNotFound, ActiveResource::ForbiddenAccess => e
    nil
  rescue => e
    tries -= 1
    return nil if e.message.index("402")
    if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
      sleep 1.seconds
      retry
    else
      error = {error_message: e.message, charge: charge_id.to_s}
      Airbrake.notify("get_one_time_charge", **error)
      nil
    end
  end

  def get_transactions(order_id)
    tries ||= 3
    create_session unless session
    ShopifyAPI::Transaction.all(order_id: order_id)
  rescue ActiveResource::UnauthorizedAccess, ActiveResource::ResourceNotFound, ActiveResource::ForbiddenAccess => e
    []
  rescue => e
    tries -= 1
    return nil if e.message.index("402")
    if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
      sleep 1.seconds
      retry
    else
      error = {error_message: e.message, order_id: order_id.to_s}
      Airbrake.notify("get_transactions", **error)
      []
    end
  end

  def get_billing(charge_id)
    tries ||= 3
    create_session unless session
    ShopifyAPI::RecurringApplicationCharge.find(id: charge_id)
  rescue ActiveResource::UnauthorizedAccess, ActiveResource::ResourceNotFound, ActiveResource::ForbiddenAccess => e
    nil
  rescue => e
    tries -= 1
    return nil if e.message.index("402")

    if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
      sleep 1.seconds
      retry
    else
      Rails.logger.error("get_billing: #{{user: @shop.id, charge: charge_id}.inspect} #{e.message}")
      nil
    end
  end

  ### to test ###
  def cancel_fulfillment(shopify_order_id, fulfillment_id)
    tries = 3
    create_session unless session

    fulfillment = ShopifyAPI::Fulfillment.find(id: fulfillment_id, order_id: shopify_order_id)
    fulfillment.cancel
  rescue => e
    tries -= 1
    if tries > 0 && (!e.is_a?(RuntimeError) || (e.is_a?(RuntimeError) && (e.message =~ /.*Exceeded 2 calls per second.*/).present?))
      sleep 1.second
      retry
    else

      Airbrake.notify("cancel_fulfillment: #{shopify_order_id}: #{e.message}", e)
      false
    end
  end

  ### to test ###
  def create_refund(params)
    create_session unless session
    application_credit = ShopifyAPI::ApplicationCredit.new
    params.each do |k, v|
      application_credit.send("#{k}=", v)
    end
    application_credit.save!
    application_credit
  rescue => _
    nil
  end
end

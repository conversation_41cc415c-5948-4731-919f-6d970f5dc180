class ShopifyPayment < Payment
  def initialize(*args)
    super
  end

  def process_callback(params)
    notification = OffsitePayments.integration(:shopify_payment).notification(params, shopify_token: @shop.shopify_token, shopify_domain: @shop.shopify_domain, charge_type: "application")
    status = notification.retrieve_payment_status
    create_billing(
      params[:charge_id],
      params[:charge_name],
      params[:charge_price],
      status
    )
    return false unless status == "active"
    @shop.increment!(:credit, params[:credit].to_i)
    true
  end
end

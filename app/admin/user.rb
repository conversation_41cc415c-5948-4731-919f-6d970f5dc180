ActiveAdmin.register User do
  menu parent: "reports_or_logs"
  menu priority: 10

  actions :all, except: %i[new destroy]

  filter :email

  index do
    id_column
    column :email
    column("Number of Shops") { |user| user.shops.count }
    column :created_at
  end

  show do
    attributes_table(*User.column_names.map(&:to_sym))

    panel "Shops" do
      table_for user.shops do
        column("ID") { |shop| link_to shop.id, admin_shop_url(shop) }
        column :provider
        column :package
        column :installed_at
      end
    end
  end
end

ActiveAdmin.register SyncLog do
  menu parent: "reports_or_logs"
  menu priority: 6
  belongs_to :source
  remove_filter :source, :shop
  actions :index, :show

  index do
    id_column
    column :source
    column :status
    column "No. Orders", &:total_process_orders
    column :created_at do |log|
      "<strong>#{log.caller}</strong> #{log.created_at} (#{time_ago_in_words(log.created_at)})".html_safe
    end
    column :error_message
    column :taken do |log|
      distance_of_time_in_words(log.created_at, log.processed_at).to_s
    end
    column :success do |log|
      link_to "#{log.number_fulfillment_updated} / #{log.total_source_row} ",
        admin_source_fulfillment_logs_url("q[sync_log_id_equals]" => log.id), target: "_blank"
    end
  end
end

ActiveAdmin.register FileReplacement do
  remove_filter :source

  belongs_to :source

  filter :column

  permit_params :column, :replace_from, :replace_to

  index do
    id_column
    column :column
    column :replace do |replace|
      "#{replace.replace_from} -> #{replace.replace_to} (#{replace.nil_if_not_found})"
    end
    column :created_at
    column :updated_at
  end

  form do |f|
    f.semantic_errors(*f.object.errors.keys)

    f.inputs "Edit Replacement" do
      input :column, as: :select, collection: [["Tracking Company Mapping", "tracking_company_mapping"]]
      input :replace_from
      input :replace_to
      input :nil_if_not_found
    end
    f.actions
  end
end

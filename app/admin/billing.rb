ActiveAdmin.register Billing do
  menu parent: "reports_or_logs"

  actions :all, except: %i[destroy update new]

  action_item :top_spender, only: :index do
    link_to "Top Spender", top_spender_admin_billings_url
  end

  action_item :check_pending_subscription, only: :show do
    if billing.shop.provider === "Shopify" && ["active", "success"].exclude?(billing.status)
      link_to "Check Billing Status", check_pending_subscription_admin_billing_path(billing)
    end
  end

  action_item :create_refund, only: :show do
    link_to "Create Refund", create_refund_admin_billing_path(billing)
  end

  member_action :check_pending_subscription, method: :get do
    billing = Billing.find(params[:id])
    status = billing.check_subscription_status
    redirect_to admin_billing_path(billing), notice: status ? "Billing has been activated" : "No change has been detected"
  end

  member_action :create_refund, method: :get do
    billing = Billing.find(params[:id])

    render "create_refund", locals: {billing: billing}
  end

  member_action :process_refund, method: :post do
    billing = Billing.find(params[:id])
    credit = billing.create_refund(params[:refund_value])

    if credit.valid?
      billing.update(refunded: true)
      redirect_to admin_billing_path(billing), notice: "Refund success"
    else
      redirect_to admin_billing_path(billing), alert: credit.errors.full_messages.join(", ")
    end
  end

  collection_action :top_spender, method: :get do
    @lists = Shop.select("shops.id, shopify_domain, max(shops.created_at) as created_at, sum(billings.total_charge) as amount").joins(:billings).where("billings.status = 'success' or billings.status = 'active' and billings.refunded = ?", false).group("shops.id, shopify_domain").order("sum(billings.total_charge) desc")
    render "top_spender"
  end

  filter :status
  filter :shop_name
  filter :shop_id

  scope "Pending" do |billings|
    billings.where(status: "pending")
  end

  index download_links: false do
    id_column
    column :shop_name
    column :plan_name
    column :status
    column :total_charge
    column :created_at do |billing|
      "#{billing.created_at} #{time_ago_in_words(billing.created_at)}"
    end
    column :refunded
    actions
  end
end

ActiveAdmin.register FulfillmentLog do
  menu parent: "reports_or_logs"
  menu priority: 5
  remove_filter :source
  actions :index, :show
  includes :sync_log

  belongs_to :source

  filter :tracking_no
  filter :order_number
  filter :sku
  filter :fulfillment_status
  filter :created_at
  filter :sync_log_id

  scope :all, default: true
  scope :failed do |logs|
    logs.where("fulfillment_status = ?", "false")
  end

  member_action :view_fulfillments, method: :get do
    log = FulfillmentLog.find(params[:id])
    @source = Source.find(params[:source_id])
    @fulfillments = if params[:order_only] == "true"
      @source.shop.find_order(log.shopify_order_id)
    else
      @source.shop.find_order(log.shopify_order_id).fulfillments
    end
    render "view_fulfillments"
  end

  index do
    id_column
    column :sync_log_id
    column(:amount) do |log|
      "#{log.lineitem_amount}/#{log.order_amount}"
    end
    column :order_number
    column(:fulfillment_id) do |log|
      "#{link_to "view", view_fulfillments_admin_source_fulfillment_log_url(log.source, log)}<br>#{log.shopify_fulfillment_id}".html_safe
    end
    column(:order_id) do |log|
      link_to "view", view_fulfillments_admin_source_fulfillment_log_url(log.source, log, order_only: true)
    end
    column(:product_info) do |log|
      "sku: #{log.sku} qty: #{log.quantity}"
    end
    column :tracking do |log|
      "#{log.tracking_no} #{log.tracking_company}"
    end
    column :time do |log|
      "#{log.created_at} #{time_ago_in_words(log.created_at)}"
    end
    column :error_message
  end

  show do
    default_main_content
    panel "Paypal Orders" do
      table_for fulfillment_log.paypal_order do
        column :transaction_id
        column :success
      end
    end
  end
end

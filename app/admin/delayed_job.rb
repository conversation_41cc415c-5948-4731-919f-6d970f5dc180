ActiveAdmin.register Delayed::Job, as: "Delayed Job" do
  menu parent: "reports_or_logs"
  config.sort_order = "created_at_asc"

  actions :index, :show, :destroy

  scope :all, default: true
  scope "Executing" do |jobs|
    jobs.where.not(locked_at: nil, locked_by: nil)
  end
  scope "Failed" do |jobs|
    jobs.where.not(failed_at: nil)
  end
  scope "Queuing" do |jobs|
    jobs.where("failed_at is NULL AND locked_at is NULL AND locked_by is NULL")
  end

  index do
    selectable_column
    id_column
    column :attempts
    column :run_at
    column :failed_at do |job|
      time_ago_in_words(job.failed_at) if job.failed_at
    end
    column :locked_at do |job|
      time_ago_in_words(job.locked_at) if job.locked_at
    end
    column :locked_by
    column :created_at do |job|
      time_ago_in_words(job.created_at)
    end
    actions
  end

  show do
    attributes_table do
      row :id
      row :priority
      row :attempts
      row :handler do |job|
        debug job.handler
      end
      row :last_error
      row :run_at
      row :locked_at
      row :failed_at
      row :locked_by
      row :queue
      row :created_at
      row :updated_at
    end
  end
end

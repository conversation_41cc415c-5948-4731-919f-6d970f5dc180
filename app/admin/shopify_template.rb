ActiveAdmin.register Source::ShopifyTemplate do
  actions :all, except: [:new]

  permit_params :name, :source_host, :source_login, :schedule_type, :schedule_time, :status, :last_processing_time,
    :order_no_mapping, :sku_mapping, :quantity_mapping, :tracking_no_mapping, :tracking_company_mapping, :source_process, :source_rename,
    :has_header, :sync_status, :schedule_interval, :order_key, :ssh_key, :source_type, :tracking_url_mapping, :ignore_key, :ignore_value,
    :ignore_empty_sku, :source_url, :column_separator, :find_val, :source_process, :allow_blank_tracking_no, :tracking_company_default,
    :shopify_order_key_constants, :order_identifier_constants, :financial_status, :path_to_file, :ftp_mode, :parent_node, :after_fulfilled_order_financial_status,
    :row_separator, :fulfillment_status, :order_status, :order_days_ago, :source_file_name, :ecwid_payment_status, :ecwid_fulfillment_status, :file_encoding, :column_ranges, :auto_detect_tracking, :line_item_identifier

  filter :name
  filter :status
  filter :schedule_type
  filter :schedule_time
  filter :source_process

  form do |f|
    f.semantic_errors(*f.object.errors.keys)

    f.inputs "Edit Source" do
      input :name
      input :column_ranges
      input :order_no_mapping
      input :sku_mapping
      input :quantity_mapping
      input :tracking_no_mapping
      input :tracking_company_mapping
      input :tracking_url_mapping
      input :order_key, as: :select, collection: Settings.order_keys.map { |k, v| [v, k] }
      input :ignore_key
      input :ignore_value
      input :source_type, as: :select, collection: Settings.source_types.map { |k, v| [v, k] }
      input :source_host
      input :source_login
      input :source_file_name
      input :path_to_file
      input :ftp_mode
      input :source_url
      input :source_process, as: :select, collection: Settings.source_process.map { |k, v| [v, k] }
      input :schedule_type, as: :select, collection: Settings.job_type.map { |k, v| [v, k] }
      input :schedule_time
      input :has_header
      input :schedule_interval
      input :column_separator, as: :select, collection: Settings.column_separators.map { |k, v| [v, k] }
      input :find_val
      input :row_separator, as: :select, collection: Settings.row_separators.map { |o| [o[1].name, o[0]] }
      input :ignore_empty_sku
      input :allow_blank_tracking_no
      input :notify_customer
      input :tracking_company_default
      input :shopify_order_key_constants
      input :order_identifier_constants
      input :financial_status
      input :fulfillment_status
      input :order_status
      input :ecwid_payment_status
      input :ecwid_fulfillment_status
      input :parent_node
      input :after_fulfilled_order_financial_status
      input :source_rename
      input :order_days_ago
      input :file_encoding
      input :auto_detect_tracking
      input :line_item_identifier
    end
    f.actions
  end

  index do
    id_column
    column :shop
    column :name
    column :order_no_mapping
    column :sku_mapping
    column :quantity_mapping
    column :tracking_no_mapping
    column :tracking_company_mapping
    column :tracking_url_mapping
    actions
  end
end

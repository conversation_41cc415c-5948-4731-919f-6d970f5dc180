ActiveAdmin.register Shop do
  menu priority: 2

  actions :all, except: %i[new destroy]

  filter :shopify_domain
  filter :bigcommerce_domain
  filter :ecwid_store_url
  filter :email
  filter :installed_at
  filter :package
  filter :created_at
  filter :charged_at
  filter :country_name
  filter :city

  scope :all, default: true
  scope("Shopify") { |scope| scope.where(provider: "Shopify") }
  scope("Ecwid") { |scope| scope.where(provider: "Ecwid") }
  scope("BigCommerce") { |scope| scope.where(provider: "BigCommerce") }

  action_item :add_source, only: :show do
    link_to "Add Source", add_source_admin_shop_url(shop)
  end

  member_action :get_shopify_order, method: :post do
    shop = Shop.find(params[:id])
    ShopifyAPI::LightGraphQL.set(shop)
    @shopify_order = shop.find_order_by_name(params[:order_number], {})
    @variants = {}
    @shopify_order.each do |order|
      order.line_items.each do |li|
        v = ShopifyAPI::LightGraphQL.query(GraphqlHelper.query_product_variant(variant_id: "gid://shopify/ProductVariant/#{li["variant_id"]}"))
        inventory_levels = []
        (v.dig("inventoryItem", "inventoryLevels", "nodes") || []).each do |level|
          inventory_levels << {location_id: level.dig["location"]["id"], available: level["quantities"][0]["quantity"]}
          @variants[li["variant_id"]] = {inventory_management: v["inventoryItem"]["tracked"], inventory_levels: inventory_levels}
        end
      rescue ShopifyAPI::Errors::HttpResponseError => e
        Rails.logger.error e.message
      rescue ActiveResource::ResourceNotFound => _
      rescue TypeError => e
        Rails.logger.error e.message
      end
    end
  end

  member_action :view_locations, method: :get do
    shop = Shop.find(params[:id])
    shop.create_session
    @locations = ShopifyAPI::Location.all
  end

  sidebar "Get Shopify Order using Order Name", only: :show do
    form_tag get_shopify_order_admin_shop_url(resource) do |_f|
      text_field_tag("order_number") << submit_tag("Check")
    end
  end

  sidebar "Billing", only: :show do
    "#{label_tag("Total Spent: #{resource.total_spent}")}<br>
    #{link_to("view history", admin_billings_url(q: {"[shop_id_equals]" => resource.id}))}<br>
    #{link_to("view locations", view_locations_admin_shop_url(resource))}
    ".html_safe
  end

  member_action :add_source, method: :get do
    shop = Shop.find(params[:id])
    source = shop.sources.new
    source.assign_defaults
    source.assign_source_email

    if source.save(validate: false)
      Rails.logger.info source.errors.first
      redirect_to admin_shop_url(shop), notice: "New source created"
    else
      redirect_to admin_shop_url(shop), error: source.errors.first
    end
  end

  action_item :impersonate, only: :show do
    if current_shop != shop
      link_to "Impersonate", impersonate_admin_shops_url(id: shop.id)
    elsif current_shop == shop && current_shop != true_shop
      link_to "Stop impersonate", stop_impersonate_admin_shops_url(id: shop.id)
    else
      "Current Shop"
    end
  end

  action_item :current_shop, only: :index do
    if current_shop != true_shop
      link_to "Impersonated Shop", admin_shop_url(current_shop)
    end
  end

  collection_action :impersonate, method: :get do
    shop = Shop.find(params[:id])
    if current_user
      impersonate_shop(shop)
      flash[:notice] = "Successfully impersonating #{shop.user.email}"
    else
      flash[:warning] = "You must be login as any shop to impersonate"
    end
    redirect_back fallback_location: admin_shops_url
  end

  collection_action :stop_impersonate, method: :get do
    shop = Shop.find(params[:id])
    stop_impersonate_shop
    flash[:notice] = "Successfully stopped impersonating #{shop.user.email}"
    redirect_back fallback_location: admin_shops_url
  end

  permit_params :package, :charged_id, :installed_at, :source_limit, :use_credit, :credit, :schedule_min_hour, :timezone, :low_credit_alert, :failure_count

  show do
    panel "Sources" do
      table_for shop.sources.order("last_processing_time desc") do
        column(:source) { |s| link_to s.name, admin_source_url(s) }
        column(:source_type)
        column(:last_processing_time)
        column(:created_at)
      end
    end
    attributes_table do
      row :id do |shop|
        "#{shop.id} #{if shop.shopify_domain
                        link_to "Shopify History", "https://partners.shopify.com/174529/apps/810288/history?search=#{shop.shopify_domain}", "target" => "_blank"
                      end}".html_safe
      end
      row :info do |shop|
        "#{shop.provider} #{shop.store_domain}"
      end
      row :email do |shop|
        "main: #{shop.email}, notification: #{shop.notification_email}, support: #{shop.customer_support_email}"
      end
      row :source_limit
      row :use_credit
      row :credit do |shop|
        "#{shop.credit} #{shop.package}"
      end
      row :timezone
      row :date do |shop|
        "Created: #{shop.created_at} / Updated: #{shop.updated_at} / Installed: #{shop.installed_at}"
      end
      row :charge do |shop|
        "#{shop.package} #{shop.charge_id} #{shop.charged_at}"
      end
      row :uninstalled_at
      row :failure_count
    end
  end

  index do
    column(:id) { |s| link_to s.id, admin_shop_url(s), target: "_blank" }
    column :store_domain, &:store_domain
    column :provider
    column :package
    column :credit
    column :charged_at
    column :created_at
    actions dropdown: true do |shop|
      if current_shop != shop
        item "Impersonate", impersonate_admin_shops_url(id: shop.id), class: "member_link"
      end
      if current_shop == shop && current_shop != true_shop
        item "Stop Impersonate", stop_impersonate_admin_shops_url(id: shop.id), class: "member_link"
      end
      span "Current Shop" if current_shop == shop
    end
  end

  form do |f|
    f.semantic_errors(*f.object.errors.keys)

    f.inputs "Edit Shop" do
      input :source_limit
      input :schedule_min_hour
      input :use_credit
      input :timezone
      input :credit
      input :failure_count
      input :low_credit_alert
    end
    f.actions
  end
end

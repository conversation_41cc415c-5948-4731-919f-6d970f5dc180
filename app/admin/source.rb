ActiveAdmin.register Source do
  menu priority: 4

  actions :all, except: %i[new destroy]

  filter :name
  filter :status
  filter :schedule_type
  filter :schedule_time
  filter :source_process

  scope :all, default: true
  scope("Shopify") { |scope| scope.where(platform: "shopify") }
  scope("Ecwid") { |scope| scope.where(platform: "ecwid") }
  scope("BigCommerce") { |scope| scope.where(platform: "big_commerce") }

  permit_params :name, :source_host, :source_login, :schedule_type, :schedule_time, :status, :last_processing_time,
    :order_no_mapping, :sku_mapping, :quantity_mapping, :tracking_no_mapping, :tracking_company_mapping, :source_process, :source_rename,
    :has_header, :sync_status, :schedule_interval, :order_key, :ssh_key, :source_type, :tracking_url_mapping, :ignore_key, :ignore_value,
    :ignore_empty_sku, :source_url, :column_separator, :find_val, :source_process, :allow_blank_tracking_no, :tracking_company_default, :shipment_status_mapping, :shipment_status_converter,
    :shopify_order_key_constants, :order_identifier_constants, :financial_status, :path_to_file, :ftp_mode, :parent_node, :after_fulfilled_order_financial_status,
    :row_separator, :fulfillment_status, :order_status, :order_days_ago, :source_file_name, :ecwid_payment_status, :ecwid_fulfillment_status, :file_encoding, :column_ranges,
    :location_id, :location_mapping, :notify_customer, :auto_detect_tracking, :shipment_status, :premapping_keys, :only_selected_location, :sku_prefix, :bigcommerce_order_status, :exclude_inventory_management, :line_item_identifier, :tracking_company_converter, :google_sheet_name, :connection_settings
  json_editor
  sidebar :debug_source, only: :show do
    ul do
      li link_to "View data", load_source_admin_source_url(resource), target: "_blank"
      li link_to "Download File", download_source_admin_source_url(resource), target: "_blank"
      li link_to "Test Column Mapping", test_column_mapping_admin_source_url(resource), target: "_blank"
      # li link_to "List orders to be fulfilled", debug_fulfillments_admin_source_url(resource, all_orders: false), target: "_blank"
      # li link_to "List orders to be fulfilled(all orders)", debug_fulfillments_admin_source_url(resource, all_orders: true), target: "_blank"
      li link_to "List all order numbers", debug_orders_admin_source_url(resource), target: "_blank", class: "load-order-num"
      li link_to "View replacements", admin_source_file_replacements_url(resource), target: "_blank"
    end
  end

  sidebar :actions, only: :show do
    ul do
      li link_to "Sync now", run_admin_source_url(resource), data: {confirm: "Are you sure?"}
      li link_to "Sync now (Update fulfilled orders too) Warning: Very Slow!", update_fulfillments_admin_source_url(resource), data: {confirm: "Are you sure?"}
    end
    ul do
      li link_to "Reset status (kill)", set_status_empty_admin_source_url(resource), data: {confirm: "Are you sure?"}
      li link_to "Make Template", copy_admin_source_url(resource), data: {confirm: "Are you sure?"}
    end
  end

  sidebar :logs, only: :show do
    ul do
      li link_to "Fulfillment Logs", admin_source_fulfillment_logs_url(resource), target: "_blank"
      # li link_to "Failed Fulfillment Logs", admin_source_fulfillment_logs_url(resource, params: {"q[fulfillment_status_equals]" => "false"}), target: "_blank"
      li link_to "Activity Logs", admin_source_sync_logs_url(resource), target: "_blank"
    end
  end

  collection_action :amount_report, method: :get do
    @data = FulfillmentLog.select("DATE(created_at) as date, sum(order_amount) as total_amount, sum(usd_amount) as usd_amount").where("created_at > ?", 6.months.ago).group("DATE(created_at)").order("DATE(created_at) desc")
    render "admin/sources/amount_report"
  end

  collection_action :top_stores, method: :get do
    @data = FulfillmentLog.select("shop_id, shops.shopify_domain, date_trunc('month', fulfillment_logs.created_at) as month, date_trunc('year', fulfillment_logs.created_at) as year, count(*) as fulfilled_count").group("shop_id, shops.shopify_domain, month, year").order("count(*) desc").joins(:shop).limit(50)
    @one_month_data = FulfillmentLog.select("shop_id, shops.shopify_domain, count(*) as fulfilled_count").where("fulfillment_logs.created_at >= ?", 1.month.ago).group("shop_id, shops.shopify_domain").order("count(*) desc").joins(:shop).limit(30)
    @type_counts = FulfillmentLog.select("sources.source_type, count(*) as count").joins(:source).where("fulfillment_logs.created_at > ?", 1.month.ago).group("sources.source_type").order("count(*) desc")
    render "admin/sources/top_stores"
  end

  member_action :copy do
    source = Source.find(params[:id])
    source.make_as_template
    source.save
    Rails.logger.info source.errors.inspect

    flash[:notice] = "Successfully copied #{source.id}"
    redirect_to admin_source_url(source.id)
  end

  member_action :copy_to do
    source = Source.find(params[:id])
    new_source = source.duplicate
    new_source.shop_id = params[:shop_id]
    new_source.save
    redirect_to admin_shop_url(params[:shop_id]), notice: "Copied successful"
  end

  member_action :load_source, method: :get do
    source = Source.find(params[:id])
    @data = source.download_file(source.build_file_reader)
  end

  member_action :download_source, method: :get do
    source = Source.find(params[:id])
    if source.source_file_host != Socket.gethostname
      if Socket.gethostname.index("web1")
        FeedTransferer.transfer_file(source.source_file.path, "web2.stock-sync.com")
      else
        FeedTransferer.transfer_file(source.source_file.path, "web1.stock-sync.com")
      end
    end
    send_file(source.source_file.path,
      filename: source.source_file.file.filename,
      type: source.source_file.content_type,
      disposition: "attachment",
      url_based_filename: true)
  end

  member_action :test_column_mapping, method: :get do
    source = Source.find(params[:id])
    @column_mapping = source.test_column_mapping
  end

  member_action :debug_fulfillments, method: :get do
    source = Source.find(params[:id])
    @fulfillments = source.debug_orders(params[:all_orders])
  end

  member_action :debug_shopify_order, method: :get do
    @source = Source.find(params[:id])
    @fulfillments = @source.shop.find_order(params[:order_id])
    @fulfillments = JSON.parse(@fulfillments.to_json)
    render "admin/fulfillment_logs/view_fulfillments"
  end

  member_action :debug_orders, method: :get do
    @source = Source.find(params[:id])
    @orders = @source.list_orders
    @total_order = @orders.count
  end

  member_action :pre_debug_order do
    @source = Source.find(params[:id])
    # @orders = OrderFetcher.new(source: @source, update_mode: false)
    @orders = @source.list_orders
    render plain: @orders.count, layout: false
  end

  member_action :run, method: :get do
    source = Source.find(params[:id])
    source.status = ""
    source.save
    AdminFulfillmentsJob.perform_later(source.id, "support", false)
    redirect_to admin_source_url(source), notice: "Process running."
  end

  member_action :update_fulfillments, method: :get do
    source = Source.find(params[:id])
    AdminFulfillmentsJob.perform_later(source.id, "support", true)
    # source.update_fulfillments(Time.zone.now, :support, true)
    redirect_to admin_source_url(source), notice: "Process running(and updating)"
  end

  member_action :upload_file, method: :post do
    source = Source.find(params[:id])
    source.update(source_file: params[:source_file], source_file_host: Socket.gethostname)
    redirect_to admin_source_url(source), notice: "Upload completed."
  end

  member_action :update_password, method: :post do
    source = Source.find(params[:id])
    source.source_password = params[:source_password]
    if source.save
      Rails.logger.info source.errors.first
      redirect_to admin_source_url(source), notice: "New password updated"
    else
      redirect_to admin_source_url(source), error: source.errors.first
    end
  end

  member_action :set_status_empty, method: :get do
    source = Source.find(params[:id])
    source.update(status: "")
    redirect_to admin_source_url(source), notice: "Status set to empty succesfully."
  end

  member_action :set_active, method: :get do
    source = Source.find(params[:id])
    is_active = params[:status] == "true"
    source.update_sync_status(is_active)
    redirect_to admin_source_url(source), notice: "Status set to empty succesfully."
  end

  index do
    id_column
    column :shop do |s|
      if s.shop
        case s.shop.provider.downcase
        when "shopify"
          s.shop.shopify_domain
        when "bigcommerce"
          s.shop.bigcommerce_domain
        when "ecwid"
          s.shop.ecwid_store_url
        end
      end
    end
    column :name
    column :mapping do |s|
      "OrdNo:#{s.order_no_mapping} TrackNo:#{s.tracking_no_mapping} SKU:#{s.sku_mapping} Qty:#{s.quantity_mapping}"
    end
    column(:fulfillment_logs) { |source| link_to "Fulfillment Logs", admin_source_fulfillment_logs_url(source) }
    column(:sync_logs) { |source| link_to "Sync Logs", admin_source_sync_logs_url(source) }
    actions
  end

  sidebar "Copy to Shop ID", only: :show do
    form_tag copy_to_admin_source_url(resource), method: :get do |f|
      text_field_tag("shop_id", "", size: 20) << submit_tag("Copy to")
    end
  end

  form do |f|
    f.semantic_errors(*f.object.errors.keys)

    f.inputs "Edit Source" do
      input :name
      input :column_ranges
      input :order_no_mapping
      input :sku_mapping
      input :quantity_mapping
      input :tracking_no_mapping
      input :tracking_company_mapping
      input :tracking_url_mapping
      input :location_mapping
      input :order_key, as: :select, collection: Settings.order_keys.map { |k, v| [v, k] }
      input :ignore_key
      input :ignore_value
      input :source_type, as: :select, collection: Settings.source_types.map { |k, v| [v, k] }
      input :source_host
      input :source_login, as: :string
      # input :source_parent_path
      input :source_file_name
      input :path_to_file
      input :google_sheet_name
      input :ftp_mode
      input :source_url
      input :source_process, as: :select, collection: Settings.source_process.map { |k, v| [v, k] }
      input :schedule_type, as: :select, collection: Settings.job_type.map { |k, v| [v, k] }
      input :schedule_time
      input :has_header
      input :schedule_interval
      input :column_separator, as: :select, collection: Settings.column_separators.map { |k, v| [v, k] }
      input :find_val
      input :row_separator, as: :select, collection: Settings.row_separators.map { |o| [o[1].name, o[0]] }
      input :ignore_empty_sku
      input :ignore_key
      input :ignore_value
      input :sku_prefix
      input :allow_blank_tracking_no
      input :notify_customer
      input :tracking_company_default
      input :shipment_status_mapping
      input :shipment_status_converter, as: :jsonb
      input :shopify_order_key_constants
      input :order_identifier_constants
      input :financial_status
      input :fulfillment_status
      input :order_status
      input :bigcommerce_order_status
      input :ecwid_payment_status
      input :ecwid_fulfillment_status
      input :parent_node
      input :after_fulfilled_order_financial_status
      input :source_rename
      input :order_days_ago
      input :file_encoding
      input :only_selected_location
      input :location_id
      input :auto_detect_tracking
      input :shipment_status
      input :premapping_keys
      input :exclude_inventory_management
      input :line_item_identifier
      input :connection_settings, as: :jsonb
    end
    f.actions
  end

  show do
    attributes_table do
      row :shop do |source|
        store_name = ""
        case source.shop.platform
        when "shopify"
          store_name = source.shop.shopify_domain
        when "bigcommerce"
          store_name = source.shop.bigcommerce_domain
        when "ecwid"
          store_name = source.shop.ecwid_store_url
        end
        link_to(store_name, admin_shop_path(source.shop.id))
      end
      row :platform do |source|
        source.shop.platform
      end
      row :email do |source|
        link_to(source.email, "https://app.mailgun.com/app/sending/domains/in.fulfillsync.com/logs?skip=0&search=#{source.email}", target: "_blank").html_safe
      end
      row :order_days_ago
      row :location do |source|
        if source.location_id
          if source.only_selected_location

            locations = source.shop.get_locations
            locations = locations.nil? ? [] : locations
            loc = locations.find { |a| a.id == source.location_id.to_i }
            "<strong>#{loc ? loc.name : "no found"}</strong> #{loc&.active ? "active" : "INACTIVE (PLEASE CHANGE LOCATION)"} ".html_safe
          else
            "Auto-select"
          end
        else
          "Auto-select"
        end
      end
      row :schedule_type
      row :schedule_time
      row :status
      row :last_processing_time

      row :sync_status do |source|
        "#{source.sync_status} #{link_to (source.started? ? "pause" : "start"), set_active_admin_source_url(source, status: !source.started?)}".html_safe
      end
      row :next_schedule_time do |source|
        if source.next_schedule_time
          "#{source.next_schedule_time} - #{(source.next_schedule_time > Time.now) ? "running in <strong>#{time_ago_in_words(source.next_schedule_time)}</strong>" : "DELAY!! #{time_ago_in_words(source.next_schedule_time)}"} ".html_safe
        end
      end
      row :schedule_interval
      row :notify_customer
      row :created_at
      row :updated_at
      row :upload_file do |profile|
        form_tag upload_file_admin_source_url(profile), multipart: true do |_f|
          file_field_tag("source_file") << submit_tag("Upload")
        end
      end
      row "Source password" do |source|
        form_tag update_password_admin_source_url(source) do |_f|
          text_field_tag("source_password") << submit_tag("Update")
        end
      end
    end

    panel "Connection Settings" do
      attributes_table_for resource do
        # row :has_header
        row :source_type
        row("Host") do |source|
          body = "<span id='copy-source-host' style='margin-right: 7px'>#{source.source_host || "-"}</span>"
          if source.source_host.present?
            body << link_to(image_tag("https://img.icons8.com/material-outlined/20/000000/copy.png", style: "vertical-align:middle"), "#", id: "source-host")
          end
          body.html_safe
        end
        row("Direct URL") do |source|
          body = "<span id='copy-source-url' style='margin-right: 7px'>#{source.source_url || "-"}</span>"
          if source.source_url.present?
            body << link_to(image_tag("https://img.icons8.com/material-outlined/20/000000/copy.png", style: "vertical-align:middle"), "#", id: "source-url")
          end
          body.html_safe
        end
        row("Username") do |source|
          body = "<span id='copy-source-login' style='margin-right: 7px'>#{source.source_login || "-"}</span>"
          if source.source_login.present?
            body << link_to(image_tag("https://img.icons8.com/material-outlined/20/000000/copy.png", style: "vertical-align:middle"), "#", id: "source-login")
          end
          body.html_safe
        end
        row("Password") do |source|
          body = "<span id='copy-source-password' style='margin-right: 7px'>#{source.source_password || "-"}</span>"
          if source.source_password.present?
            body << link_to(image_tag("https://img.icons8.com/material-outlined/20/000000/copy.png", style: "vertical-align:middle"), "#", id: "source-password")
          end
          body.html_safe
        end
        row :path_to_file
        row :google_sheet_name
        # row :source_parent_path
        # row :source_file_name
        row :ssh_key
        row :source_process
        row :source_rename
      end
    end

    panel "Column Mapping/Settings" do
      attributes_table_for resource do
        row :order_no_mapping
        row :sku_mapping
        row :quantity_mapping
        row :tracking_no_mapping
        row :tracking_company_mapping
        row :tracking_url_mapping
        row :order_key
        row :order_identifier_constants
        row :ignore_key
        row :ignore_value
        row :column_separator
        row :find_val
        row :file_encoding
        row :allow_blank_tracking_no
        row :ignore_empty_sku
        row :sku_prefix
        row :parent_node
        row :fulfillment_status
        row :bigcommerce_order_status
        row :order_status
        row :ecwid_payment_status
        row :auto_detect_tracking
        row :shipment_status
        row :line_item_identifier
      end
    end
  end
end

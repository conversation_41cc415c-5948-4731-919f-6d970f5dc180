ActiveAdmin.register_page "Dashboard" do
  menu priority: 1, label: proc { I18n.t("active_admin.dashboard") }

  action_item :amount_report do
    link_to "Amount Report", amount_report_admin_sources_url
  end

  action_item :top_stores do
    link_to "Top stores", top_stores_admin_sources_url
  end

  content title: proc { I18n.t("active_admin.dashboard") } do
    div class: "blank_slate_container", id: "dashboard_default_message" do
      "<iframe src='https://statuspage.freshping.io/25323-UpTracker' width='100%' height='500'></iframe>".html_safe
    end

    columns do
      column do
        panel "System Monitoring" do
          render "running_jobs"
        end
      end
    end

    columns do
      column do
        panel "Source where status is running" do
          table_for Source.where(status: "running").order("created_at desc") do
            column :id, &:id
            column :last_update do |source|
              time_ago_in_words(source.updated_at)
            end
            column :shop_name do |source|
              source.shop&.shopify_domain
            end
            column "Go" do |source|
              link_to(source.name, admin_source_path(source))
            end
          end
        end
      end
    end

    columns do
      column do
        panel "Source where status is queuing" do
          table_for Source.where(status: "queuing").order("created_at desc") do
            column :id, &:id
            column :last_update do |source|
              time_ago_in_words(source.updated_at)
            end
            column :shop_name do |source|
              source.shop.shopify_domain
            end
            column "Go" do |source|
              link_to(source.name, admin_source_path(source))
            end
          end
        end
      end
    end
  end

  controller do
    def index
      params[:background_process] = BackgroundProcess.show_background_process
    end
  end
end

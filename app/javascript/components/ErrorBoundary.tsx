import { LayoutCenter } from './LayoutCenter';

export function ErrorBoundary() {
  return (
    <LayoutCenter>
      <div style={{ textAlign: 'center' }}>
        <header>
          <img
            src="/FufillsyncIcon.png"
            style={{ width: 140 }}
            alt="FulfillSync logo"
          />
          <h2 style={{ fontSize: 26, margin: 21, fontWeight: 600 }}>
            An error occured
          </h2>
          <div
            style={{
              fontSize: 16,
              margin: 21,
              lineHeight: 1.5,
              color: '#85858A',
            }}
          >
            <p>Unknown error occured while processing your request.</p>
            <p>Please try again.</p>
          </div>
        </header>
        <button
          style={{
            backgroundColor: '#606ABE',
            borderRadius: '6px',
            color: '#fff',
            padding: '10px 16px',
            fontSize: '20px',
            cursor: 'pointer',
            border: '1px solid',
          }}
          onClick={() => window.location.reload()}
        >
          Retry
        </button>
      </div>
    </LayoutCenter>
  );
}

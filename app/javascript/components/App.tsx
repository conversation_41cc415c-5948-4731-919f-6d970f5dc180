import { Apollo<PERSON><PERSON>ider } from '@apollo/client';
import {
  SkeletonPage as _SkeletonPage,
  AppProvider,
  Frame,
  SkeletonBodyText,
} from '@shopify/polaris';
import enTranslations from '@shopify/polaris/locales/en.json'; // polaris-react.shopify.com/components/utilities/app-provider?example=app-provider-default

import '@shopify/polaris/build/esm/styles.css';

import { SnackbarProvider } from 'notistack';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';
import * as React from 'react';
import {
  createBrowserRouter,
  Outlet,
  redirect,
  RouterProvider,
} from 'react-router-dom';

import { ErrorBoundary } from '@/components/ErrorBoundary';
import { WebSocketProvider } from '@/components/WebSocketContext';
import { apolloClient } from '@/gql/apolloClient';
import {
  CurrentShopQuery,
  SourceDetailQuery,
  SourceQuery,
  SourcesQuery,
} from '@/gql/queries';
import * as routes from '@/routes';

const getCurrentShop = async () =>
  apolloClient
    .query({ query: CurrentShopQuery })
    .then((res) => res.data.currentShop);

const getSourceById = async (sourceId: string) =>
  apolloClient
    .query({
      query: SourceQuery,
      variables: { sourceId },
      fetchPolicy: 'network-only',
    })
    .then((res) => res.data.source);

const getSourceDetailById = async (sourceId: string) =>
  apolloClient
    .query({
      query: SourceDetailQuery,
      variables: { sourceId },
      fetchPolicy: 'network-only',
    })
    .then((res) => res.data.source);

const getSources = async () =>
  apolloClient
    .query({
      query: SourcesQuery,
    })
    .then((res) => res.data.sources);

function SkeletonPage() {
  return (
    <_SkeletonPage primaryAction>
      <SkeletonBodyText />
    </_SkeletonPage>
  );
}

const router = createBrowserRouter([
  {
    path: routes.dashboard,
    loader: getCurrentShop,
    errorElement: <ErrorBoundary />,
    element: (
      <ApolloProvider client={apolloClient}>
        <WebSocketProvider>
          <SnackbarProvider />
          <React.Suspense fallback={<SkeletonPage />}>
            <Outlet />
          </React.Suspense>
        </WebSocketProvider>
      </ApolloProvider>
    ),
    children: [
      {
        index: true,
        loader: async ({ request }) => {
          const shop = await getCurrentShop();
          if (shop.provider === 'BigCommerce' && !shop.connected) {
            return redirect(routes.editIntegrations);
          }
          posthog.identify(shop.id, { shop_id: shop.id });
          const url = new URL(request.url);
          const id = url.searchParams.get('id');
          const sources = (await getSources()) ?? [];

          if (sources.length === 0) {
            return redirect(routes.welcome);
          }

          if (id) {
            const source = sources.find((s) => s.id === id);
            if (!source) return redirect(routes.dashboard);
          }

          return null;
        },
        async lazy() {
          const { Dashboard } = await import('@/pages/Dashboard');
          return { Component: Dashboard };
        },
      },
      {
        path: routes.newSource,
        async lazy() {
          const { NewSource } = await import('@/pages/NewSource');
          return { Component: NewSource };
        },
      },
      {
        path: routes.sourceDetail,
        loader: async ({ params }) => {
          const source = await getSourceDetailById(params.id ?? '');
          return { source };
        },
        async lazy() {
          const { SourceDetail } = await import('@/pages/SourceDetail');
          return { Component: SourceDetail };
        },
      },
      {
        path: routes.sourceSettings,
        loader: async ({ params }) => {
          const source = await getSourceById(params.id ?? '');
          return { source };
        },
        async lazy() {
          const { Settings } = await import('@/pages/Settings');
          return { Component: Settings };
        },
      },
      {
        path: routes.fulfillments,
        async lazy() {
          const { FulfillmentLogs } = await import('@/pages/FulfillmentLogs');
          return { Component: FulfillmentLogs };
        },
      },
      {
        path: routes.editPreferences,
        async lazy() {
          const { Preferences } = await import('@/pages/Preferences');
          return { Component: Preferences };
        },
      },
      {
        path: routes.editIntegrations,
        async lazy() {
          const { Integrations } = await import('@/pages/Integrations');
          return { Component: Integrations };
        },
      },
      {
        path: routes.paypal,
        async lazy() {
          const { Paypal } = await import('@/pages/Paypal');
          return { Component: Paypal };
        },
      },
      {
        path: routes.billing,
        async lazy() {
          const { Billing } = await import('@/pages/Billing');
          return { Component: Billing };
        },
      },
      {
        path: routes.welcome,
        async lazy() {
          const { Welcome } = await import('@/pages/Welcome');
          return { Component: Welcome };
        },
      },
    ],
  },
  {
    path: '*',
    async lazy() {
      const { NotFound } = await import('@/pages/NotFound');
      return { Component: NotFound };
    },
  },
]);

export function App() {
  return (
    <AppProvider i18n={enTranslations}>
      <Frame>
        <PostHogProvider
          apiKey="phc_WfkcmvIvCVsmlAGOMt9a5ljvH4BHDZefb8A7NjQsRbw"
          options={{
            api_host: 'https://us.i.posthog.com',
          }}
        >
          <RouterProvider router={router} fallbackElement={<SkeletonPage />} />
        </PostHogProvider>
      </Frame>
    </AppProvider>
  );
}

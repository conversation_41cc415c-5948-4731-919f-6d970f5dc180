import Cookies from 'js-cookie';

export const formatNumberWithDecimal = (value) =>
  new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);

export const formatNumber = (value) =>
  new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
  }).format(value);

export const getShopId = (): string | undefined =>
  Cookies.get('uptracker_current_shop');

import { useQuery } from '@apollo/client';
import {
  Banner,
  Card,
  IndexTable,
  Modal,
  Spinner,
  Text,
} from '@shopify/polaris';
import camelCase from 'lodash/camelCase';
import * as React from 'react';

import { Link } from '@/components/Link';
import { TestColumnMappingQuery, type SourcesQueryResult } from '@/gql/queries';
import { useNotification } from '@/hooks/useNotification';
import { getSourceSettingsRoute } from '@/routes';
import type { TestColumnMapping } from '@/types';

interface TestColumnMappingModalProps {
  source: SourcesQueryResult['sources'][0];
  onClose: () => void;
}

type SourceMappingColumnKey = keyof Pick<
  TestColumnMappingModalProps['source'],
  | 'orderNoMapping'
  | 'trackingNoMapping'
  | 'trackingCompanyMapping'
  | 'trackingUrlMapping'
  | 'skuMapping'
  | 'quantityMapping'
>;

export function TestColumnMappingModal(props: TestColumnMappingModalProps) {
  const notification = useNotification();
  const { source } = props;

  const testColumnMappingQuery = useQuery(TestColumnMappingQuery, {
    fetchPolicy: 'network-only',
    variables: { id: source.id },
    onError: (error) => {
      props.onClose();
      notification.open({
        options: { variant: 'error' },
        message: error?.graphQLErrors?.[0]?.message ?? error?.message,
      });
    },
  });

  const testColumnMappingData = React.useMemo(() => {
    return testColumnMappingQuery.data
      ? testColumnMappingQuery.data.source.columnMappingData
      : ({
          data: {
            ignoreColumn: null,
            itemQuantity: null,
            itemSku: null,
            orderNo: null,
            trackingCompany: null,
            trackingNo: null,
            trackingUrl: null,
          },
          remark: null,
          status: false,
          sampleShopifyOrder: {
            id: null,
            number: null,
            orderName: null,
            orderNumber: null,
          },
          sourceMappings: null,
        } satisfies TestColumnMapping);
  }, [testColumnMappingQuery]);

  const { sampleShopifyOrder } = testColumnMappingData;

  const items = React.useMemo(() => {
    const testSourceMappings = testColumnMappingData.sourceMappings ?? [];
    const testColumns = testColumnMappingData.data;
    type TestColumnKey = keyof typeof testColumns;

    return (
      [
        {
          columnName: 'Order #',
          sourceColumnKey: 'orderNoMapping',
          testColumnKey: 'order_no',
        },
        {
          columnName: 'Tracking Number',
          sourceColumnKey: 'trackingNoMapping',
          testColumnKey: 'tracking_no',
        },
        {
          columnName: 'Tracking Company',
          sourceColumnKey: 'trackingCompanyMapping',
          testColumnKey: 'tracking_company',
        },
        {
          columnName: 'Tracking URL',
          sourceColumnKey: 'trackingUrlMapping',
          testColumnKey: 'tracking_url',
        },
        {
          columnName: 'Item SKU',
          sourceColumnKey: 'skuMapping',
          testColumnKey: 'item_sku',
        },
        {
          columnName: 'Item Quantity',
          sourceColumnKey: 'quantityMapping',
          testColumnKey: 'item_quantity',
        },
      ] satisfies Array<{
        columnName: string;
        sourceColumnKey: SourceMappingColumnKey;
        testColumnKey: (typeof testSourceMappings)[0];
      }>
    )
      .filter((o) => testSourceMappings.includes(o.testColumnKey))
      .map(
        (o) =>
          ({
            columnName: o.columnName,
            columnMapping: source[o.sourceColumnKey] ?? '-',
            columnValue:
              testColumns[camelCase(o.testColumnKey) as TestColumnKey] ?? '-',
          }) satisfies {
            columnName: string;
            columnMapping: string;
            columnValue: string;
          }
      );
  }, [source, testColumnMappingData]);

  return (
    <Modal
      open
      onClose={props.onClose}
      title={`Test Column Mapping for ${source.name}`}
      primaryAction={{ content: 'Close', onAction: props.onClose }}
    >
      <div style={{ padding: '16px' }}>
        {testColumnMappingQuery.loading ? (
          <div style={{ textAlign: 'center', padding: 20 }}>
            <Spinner size="large" />
          </div>
        ) : testColumnMappingData.status === false ? (
          <div style={{ padding: '20px', paddingBottom: '300px' }}>
            <Banner tone="warning">{testColumnMappingData.remark}</Banner>
          </div>
        ) : (
          <>
            <Text variant="bodyLg" as="p">
              Your latest shopify order
            </Text>
            <div style={{ marginTop: '16px' }}>
              <Card>
                <Text variant="bodySm" as="p" tone="subdued">
                  Recognize my order by
                </Text>
                {[
                  {
                    name: 'Shopify Order ID',
                    value: sampleShopifyOrder.id,
                    selected: source.orderKey === 'id',
                  },
                  {
                    name: 'Order Number',
                    value: sampleShopifyOrder.orderNumber,
                    selected: source.orderKey === 'order_number',
                  },
                  {
                    name: 'Order Name',
                    value: sampleShopifyOrder.orderName,
                    selected: source.orderKey === 'name',
                  },
                  {
                    name: 'Order Sequence Number',
                    value: sampleShopifyOrder.number,
                    selected: source.orderKey === 'number',
                  },
                ].map(({ name, value, selected }) => (
                  <p key={name}>
                    {selected ? (
                      <>
                        {name} : {value}
                      </>
                    ) : (
                      <></>
                    )}
                  </p>
                ))}
                <div style={{ marginTop: '24px' }}>
                  <Banner>
                    <Text variant="bodyMd" as="p">
                      Note: If columns are mismatched, please adjust
                      <Link url={`${getSourceSettingsRoute(source.id)}?tab=1`}>
                        {' '}
                        here
                      </Link>{' '}
                    </Text>
                  </Banner>
                </div>
              </Card>
            </div>

            <div style={{ margin: '20px 0px' }}>
              <Card padding="0">
                <IndexTable
                  itemCount={items.length}
                  headings={[
                    { title: 'System column' },
                    { title: 'Mapped feed column' },
                    { title: 'Column value' },
                  ]}
                  selectable={false}
                >
                  {items.map((item, index) => (
                    <IndexTable.Row
                      id={item.columnName}
                      key={item.columnName}
                      position={index}
                    >
                      <IndexTable.Cell>{item.columnName}</IndexTable.Cell>
                      <IndexTable.Cell>{item.columnMapping}</IndexTable.Cell>
                      <IndexTable.Cell>{item.columnValue}</IndexTable.Cell>
                    </IndexTable.Row>
                  ))}
                </IndexTable>
              </Card>
            </div>
            <Banner>
              <Text variant="bodyMd" as="p">
                Note: If columns are mismatched, update the column mapping
                before processing
              </Text>
            </Banner>
          </>
        )}
      </div>
    </Modal>
  );
}

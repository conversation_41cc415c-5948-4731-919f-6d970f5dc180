import { Button } from '@shopify/polaris';
import { closeSnackbar } from 'notistack';
import * as React from 'react';
import Sockette from 'sockette';

import { useCurrentShop } from '@/hooks/useCurrentShop';
import { useNotification } from '@/hooks/useNotification';

const WEBSOCKET_URL =
  import.meta.env.MODE === 'production'
    ? 'wss://cgqei5lcrc.execute-api.us-east-2.amazonaws.com/prod'
    : 'wss://86f6zwnfz8.execute-api.us-east-2.amazonaws.com/dev';

interface LatestMessage {
  payload?: {
    id: string;
    name: string;
    progress: number;
    schedule_interval: number;
    schedule_time: null;
    schedule_type: 'hourly';
    source_type: 'url';
    status: 'queuing' | 'running' | '';
    sync_log: {
      caller: 'ui';
      error_message: string;
      id: string;
      number_fulfillment_updated: number;
      processed_at: string;
      status: 'error' | 'running';
    };
    sync_status: 'started';
    updated_at: string;
  };
}

const WebSocketContext = React.createContext<
  { latestMessage: LatestMessage } | undefined
>(undefined);

// expose useHook instead of useContext(WebSocketContext) directly
// https://kentcdodds.com/blog/how-to-use-react-context-effectively#the-custom-consumer-hook
export function useWebSocketContext() {
  const context = React.useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error(
      'useWebSocketContext must be used within a WebSocketProvider'
    );
  }
  return context;
}

export const WebSocketProvider = ({ children }) => {
  const notification = useNotification();
  const shop = useCurrentShop().currentShop;
  const [latestMessage, setLatestMessage] = React.useState({});

  const sockette = React.useMemo(() => {
    return new Sockette(`${WEBSOCKET_URL}?shopId=${shop.id}`, {
      timeout: 5e3,
      maxAttempts: 3,
      onopen: (e) => {
        console.log('Connected', e);
      },
      onmessage: ({ data }) => {
        setLatestMessage(JSON.parse(data));
      },
      onreconnect: (e) => {
        console.log('Reconnecting...', e);
      },
      onmaximum: (e) => {
        console.log('Stop Attempting!', e);
      },
      onclose: (e) => {
        console.log('Closed connection with code ', e.code);
      },
      onerror: (e) => {
        console.log('Error:', e);
      },
    });
  }, [shop]);

  React.useEffect(() => {
    const onNetworkChange = (e) => {
      const isOnline = e.type === 'online';
      if (isOnline) {
        sockette.open();
        closeSnackbar();
      } else {
        sockette.close();

        notification.open({
          message: <p>Please check your internet connection and try again</p>,
          options: {
            variant: 'error',
            persist: true,
            action: () => (
              <Button
                id="reload"
                size="slim"
                onClick={() => window.location.reload()}
              >
                Reload Page
              </Button>
            ),
          },
        });
      }
    };

    window.addEventListener('online', onNetworkChange);
    window.addEventListener('offline', onNetworkChange);

    return () => {
      window.removeEventListener('online', onNetworkChange);
      window.removeEventListener('offline', onNetworkChange);
    };
  }, [notification, sockette]);

  return (
    <WebSocketContext.Provider value={{ latestMessage }}>
      {children}
    </WebSocketContext.Provider>
  );
};

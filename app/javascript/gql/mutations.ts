import { gql, type TypedDocumentNode } from '@apollo/client';

import { getShopId } from '@/components/util';
import type { Source } from '@/types';

export const CancelFulfillmentMutation = gql`
  mutation cancelFulfillment($id: ID) {
    cancelFulfillment(id: $id) {
      id
      fulfillmentStatus
    }
  }
`;

export const CopySourceMutation = gql`
  mutation CopySourceMutation($id: ID) {
    copySource(id: $id) {
      id
    }
  }
`;

export const CreateSourceMutation = gql`
  mutation CreateSourceMutation {
    createSource(shopId: ${getShopId()}) {
      id
      name
      status
      sourceType
      updatedAt
      syncLog {
        id
        errorMessage
      }
    }
  }
`;

export const CreateStripeCheckoutMutation: TypedDocumentNode<
  {
    createStripeCheckout: {
      success: boolean;
      message: string | null;
      sessionUrl: string;
    };
  },
  { price: string; credit: number }
> = gql`
  mutation CreateStripeCheckoutMutation($price: String!, $credit: Int!) {
    createStripeCheckout(price: $price, credit: $credit) {
      success
      sessionUrl
      message
    }
  }
`;

export const CreateTicketMutation = gql`
  mutation CreateTicketMutation(
    $shopifyDomain: String
    $email: String
    $message: String
    $subject: String
  ) {
    createTicket(
      email: $email
      shopifyDomain: $shopifyDomain
      message: $message
      subject: $subject
    ) {
      email
      shopifyDomain
      message
      subject
    }
  }
`;

export const DeleteSourceMutation = gql`
  mutation DeleteSourceMutation($id: ID) {
    deleteSource(id: $id) {
      id
    }
  }
`;

export const EditShopMutation = gql`
  mutation EditShopMutation($shop: ShopInput!) {
    editShop(shopInput: $shop) {
      id
      notificationEmail
      lowCreditAlert
      emailSubscriptions
      bigcommerceStoreHash
      bigcommerceClientId
      bigcommerceAccessToken
      connected
      emailNotificationStartTime
      emailNotificationEndTime
      emailNotificationCustomTimeEnabled
      paypalClientId
      paypalClientSecret
    }
  }
`;

export const MakeACopyMutation = gql`
  mutation MakeACopyMutation($id: ID) {
    makeACopy(id: $id) {
      id
    }
  }
`;

export const SourceMutation: TypedDocumentNode<
  { source: Source },
  { source: Partial<Source> }
> = gql`
  mutation SourceMutation($source: SourceInput!) {
    source(sourceInput: $source) {
      afterFulfilledOrderFinancialStatus
      allowBlankTrackingNo
      autoDetectTracking
      bigcommerceOrderStatus
      columnSeparator
      ecwidPaymentStatus
      email
      excludeInventoryManagement
      fileReplacements
      financialStatus
      ftpMode
      googleSheetName
      id
      ignoreEmptySku
      ignoreKey
      ignoreValue
      lineItemIdentifier
      locationId
      locationMapping
      locationName
      name
      notifyCustomer
      orderDaysAgo
      orderIdentifierConstants
      orderKey
      orderNoMapping
      parentNode
      pathToFile
      quantityMapping
      scheduleDay
      scheduleInterval
      scheduleTime
      scheduleType
      shopifyOrderKeyConstants
      skuMapping
      skuPrefix
      sourceHost
      sourceLogin
      sourcePassword
      sourceProcess
      sourceRename
      sourceType
      sourceUrl
      sshKey
      status
      syncLog {
        id
        errorMessage
        processedAt
      }
      syncStatus
      tagEnabled
      tagValue
      trackingCompanyDefault
      trackingCompanyMapping
      trackingNoMapping
      trackingUrlDefault
      trackingUrlMapping
      updatedAt
    }
  }
`;

export const UploadFileMutation = gql`
  mutation UploadFileMutation(
    $id: ID
    $sourceFile: File
    $updateMode: Boolean
    $cancelSyncNow: Boolean
  ) {
    uploadFile(
      id: $id
      sourceFile: $sourceFile
      updateMode: $updateMode
      cancelSyncNow: $cancelSyncNow
    ) {
      id
      name
    }
  }
`;

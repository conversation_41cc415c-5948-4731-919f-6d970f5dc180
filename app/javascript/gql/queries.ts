import { gql, type TypedDocumentNode } from '@apollo/client';

import type {
  FulfillmentLog,
  PaypalOrder,
  Shop,
  ShopLocation,
  Source,
  SyncLog,
  TestColumnMapping,
} from '@/types';

export const CurrentShopQuery: TypedDocumentNode<{
  currentShop: Shop;
}> = gql`
  query CurrentShopQuery {
    currentShop {
      bigcommerceAccessToken
      bigcommerceClientId
      bigcommerceStoreHash
      connected
      createdAt
      credit
      ecwidStoreId
      ecwidStoreUrl
      email
      emailNotificationCustomTimeEnabled
      emailNotificationEndTime
      emailNotificationStartTime
      emailSubscriptions
      expiredDay
      id
      lowCreditAlert
      notificationEmail
      package
      paypalClientId
      paypalClientSecret
      platform
      provider
      scheduleMinHour
      shopifyDomain
      storeDomain
      timezone
      useCredit
    }
  }
`;

export interface SourcesQueryResult {
  sources: Array<
    Pick<
      Source,
      | 'email'
      | 'id'
      | 'name'
      | 'orderDaysAgo'
      | 'orderKey'
      | 'orderNoMapping'
      | 'platform'
      | 'progress'
      | 'quantityMapping'
      | 'scheduleDay'
      | 'scheduleInterval'
      | 'scheduleTime'
      | 'scheduleType'
      | 'skuMapping'
      | 'skuPrefix'
      | 'sourceType'
      | 'status'
      | 'syncLog' // TODO: Pick<SyncLog>
      | 'syncStatus'
      | 'trackingCompanyMapping'
      | 'trackingNoMapping'
      | 'trackingUrlMapping'
      | 'updatedAt'
    >
  >;
}

export const SourcesQuery: TypedDocumentNode<SourcesQueryResult> = gql`
  query SourcesQuery {
    sources {
      email
      id
      name
      orderDaysAgo
      orderKey
      orderNoMapping
      platform
      progress
      quantityMapping
      scheduleDay
      scheduleInterval
      scheduleTime
      scheduleType
      skuMapping
      skuPrefix
      sourceType
      status
      syncLog {
        errorMessage
        id
        numberFulfillmentUpdated
        processedAt
        status
      }
      syncStatus
      trackingCompanyMapping
      trackingNoMapping
      trackingUrlMapping
      updatedAt
    }
  }
`;

export const TemplatesQuery: TypedDocumentNode<{
  templates: Array<{
    id: string;
    name: string;
  }>;
}> = gql`
  query TemplatesQuery {
    templates {
      id
      name
    }
  }
`;

export const FulfillmentLogsQuery: TypedDocumentNode<{
  fulfillmentLogs: Array<FulfillmentLog>;
}> = gql`
  query FulfillmentLogsQuery(
    $page: Int!
    $filters: [FulfillmentFiltersInput!]!
  ) {
    fulfillmentLogs(page: $page, filters: $filters) {
      id
      shopifyOrderId
      trackingNo
      createdAt
      orderNumber
      trackingCompany
      sku
      quantity
      fulfillmentStatus
      errorMessage
      shopifyFulfillmentId
      bigcommerceShipmentId
    }
  }
`;

export interface SourceQueryResult {
  source: Pick<
    Source,
    | 'afterFulfilledOrderFinancialStatus'
    | 'allowBlankTrackingNo'
    | 'autoDetectTracking'
    | 'bigcommerceOrderStatus'
    | 'columnSeparator'
    | 'ecwidPaymentStatus'
    | 'email'
    | 'excludeInventoryManagement'
    | 'fileReplacements'
    | 'financialStatus'
    | 'ftpMode'
    | 'googleSheetName'
    | 'id'
    | 'ignoreEmptySku'
    | 'ignoreKey'
    | 'ignoreValue'
    | 'lineItemIdentifier'
    | 'locationId'
    | 'locationMapping'
    | 'locationName'
    | 'name'
    | 'notifyCustomer'
    | 'orderDaysAgo'
    | 'orderIdentifierConstants'
    | 'orderKey'
    | 'orderNoMapping'
    | 'parentNode'
    | 'pathToFile'
    | 'platform'
    | 'quantityMapping'
    | 'shopifyOrderKeyConstants'
    | 'skuMapping'
    | 'skuPrefix'
    | 'sourceFile'
    | 'sourceHost'
    | 'sourceLogin'
    | 'sourcePassword'
    | 'sourceProcess'
    | 'sourceRename'
    | 'sourceType'
    | 'sourceUrl'
    | 'sshKey'
    | 'tagEnabled'
    | 'tagValue'
    | 'templateId'
    | 'trackingCompanyDefault'
    | 'trackingCompanyMapping'
    | 'trackingNoMapping'
    | 'trackingUrlDefault'
    | 'trackingUrlMapping'
    | 'updatedAt'
    | 'status'
    | 'progress'
    | 'syncStatus'
    | 'scheduleDay'
    | 'scheduleInterval'
    | 'scheduleTime'
    | 'scheduleType'
    | 'syncLog'
  >;
}

export const SourceQuery: TypedDocumentNode<
  SourceQueryResult,
  { sourceId: Source['id'] }
> = gql`
  query SourceQuery($sourceId: ID!) {
    source(id: $sourceId) {
      afterFulfilledOrderFinancialStatus
      allowBlankTrackingNo
      autoDetectTracking
      bigcommerceOrderStatus
      columnSeparator
      ecwidPaymentStatus
      email
      excludeInventoryManagement
      fileReplacements
      financialStatus
      ftpMode
      googleSheetName
      id
      ignoreEmptySku
      ignoreKey
      ignoreValue
      lineItemIdentifier
      locationId
      locationMapping
      locationName
      name
      notifyCustomer
      orderDaysAgo
      orderIdentifierConstants
      orderKey
      orderNoMapping
      parentNode
      pathToFile
      platform
      quantityMapping
      shopifyOrderKeyConstants
      skuMapping
      skuPrefix
      sourceHost
      sourceLogin
      sourcePassword
      sourceProcess
      sourceRename
      sourceType
      sourceUrl
      sshKey
      tagEnabled
      tagValue
      templateId
      trackingCompanyDefault
      trackingCompanyMapping
      trackingNoMapping
      trackingUrlDefault
      trackingUrlMapping
    }
  }
`;

export const SourceDetailQuery: TypedDocumentNode<
  SourceQueryResult,
  { sourceId: Source['id'] }
> = gql`
  query SourceDetailQuery($sourceId: ID!) {
    source(id: $sourceId) {
      afterFulfilledOrderFinancialStatus
      allowBlankTrackingNo
      autoDetectTracking
      bigcommerceOrderStatus
      columnSeparator
      ecwidPaymentStatus
      email
      excludeInventoryManagement
      fileReplacements
      financialStatus
      ftpMode
      googleSheetName
      id
      ignoreEmptySku
      ignoreKey
      ignoreValue
      lineItemIdentifier
      locationId
      locationMapping
      locationName
      name
      notifyCustomer
      orderDaysAgo
      orderIdentifierConstants
      orderKey
      orderNoMapping
      parentNode
      pathToFile
      platform
      progress
      quantityMapping
      scheduleDay
      scheduleInterval
      scheduleTime
      scheduleType
      shopifyOrderKeyConstants
      skuMapping
      skuPrefix
      sourceFile
      sourceHost
      sourceLogin
      sourcePassword
      sourceProcess
      sourceRename
      sourceType
      sourceUrl
      sshKey
      status
      syncStatus
      tagEnabled
      tagValue
      templateId
      trackingCompanyDefault
      trackingCompanyMapping
      trackingNoMapping
      trackingUrlDefault
      trackingUrlMapping
      updatedAt
    }
  }
`;

export const TestColumnMappingQuery: TypedDocumentNode<{
  source: {
    columnMappingData: TestColumnMapping;
  };
}> = gql`
  query TestColumnMappingQuery($id: ID!) {
    source(id: $id) {
      columnMappingData {
        status
        data {
          ignoreColumn
          itemQuantity
          itemSku
          orderNo
          trackingCompany
          trackingNo
          trackingUrl
        }
        sampleShopifyOrder {
          id
          number
          orderName
          orderNumber
        }
        remark
        sourceMappings
      }
    }
  }
`;

export const ShopLocationsQuery: TypedDocumentNode<{
  currentShop: {
    locations: Array<ShopLocation>;
  };
}> = gql`
  query ShopLocationsQuery {
    currentShop {
      locations {
        id
        name
      }
    }
  }
`;

export const SourceSyncLogsQuery: TypedDocumentNode<{
  sourceSyncLogs: Array<SyncLog>;
}> = gql`
  query SourceSyncLogsQuery($id: ID!) {
    sourceSyncLogs(id: $id) {
      caller
      errorMessage
      id
      numberFulfillmentUpdated
      processedAt
      source {
        id
        name
      }
      status
    }
  }
`;

export const SourceGuidedMappingQuery = gql`
  query SourceGuidedMappingQuery($id: ID!) {
    sourceGuidedMapping(id: $id)
  }
`;

export const ShopPaypalOrderQuery: TypedDocumentNode<{
  shopPaypalOrder: PaypalOrder[];
}> = gql`
  query ShopPaypalOrderQuery($page: Int!) {
    shopPaypalOrder(page: $page) {
      createdAt
      errorMessage
      fulfillmentStatus
      id
      orderNumber
      paypalSuccess
      paypalTransaction
      quantity
      shopifyFulfillmentId
      shopifyOrderId
      sku
      trackingCompany
      trackingNo
    }
  }
`;

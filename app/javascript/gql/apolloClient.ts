import { ApolloClient, InMemoryCache } from '@apollo/client';
import { createUploadLink } from 'apollo-upload-client';

// why we can't update apollo-upload-client
// https://github.com/jaydenseric/apollo-upload-client/issues/171
// https://github.com/jaydenseric/apollo-upload-client/issues/286
// related: https://www.apollographql.com/blog/backend/file-uploads/file-upload-best-practices/

const token = document
  .querySelector('[data-api-token]')
  ?.getAttribute('data-api-token');

export const apolloClient = new ApolloClient({
  link: createUploadLink({
    uri: '/graphql',
    headers: { Authorization: `Token ${token}` },
    credentials: 'same-origin',
  }),
  cache: new InMemoryCache(),
});

import * as Yup from 'yup';

export const getScheduleSchema = (interval) =>
  Yup.object({
    scheduleType: Yup.string(),
    scheduleInterval: Yup.number().when('scheduleType', {
      is: (val) => val === 'hourly',
      then: (schema) =>
        schema
          .typeError(`Interval must be greater than or equal to ${interval}`)
          .min(
            interval,
            `Interval must be greater than or equal to ${interval}`
          ),
    }),
  });

export const AdvanceSetting = Yup.object({
  fileReplacements: Yup.array().of(
    Yup.object({
      _destroy: Yup.string(),
      replace_from: Yup.string().when('_destroy', {
        is: '1',
        then: (schema) => schema.nullable(),
        otherwise: (schema) => schema.nullable().required('Field is required'),
      }),
    })
  ),
  ignoreValue: Yup.string(),
  ignoreKey: Yup.string().when('ignoreValue', {
    is: (val) => val && val.length > 0,
    then: (schema) => schema.required('Field is required'),
  }),
});

export const Credit = Yup.object({
  credit: Yup.number()
    .min(200, 'Fulfillment must be greater than 199')
    .max(20000, 'Fulfillment must be smaller than  20001'),
});

export const Mapping = Yup.object().shape(
  {
    skuMapping: Yup.number().when(['quantityMapping'], {
      is: (quantityMapping) => quantityMapping,
      then: (schema) => schema.required('Field is required'),
    }),
    quantityMapping: Yup.number().when(['skuMapping'], {
      is: (skuMapping) => skuMapping,
      then: (schema) => schema.required('Field is required'),
    }),
  },
  [
    ['quantityMapping', 'skuMapping'],
    ['skuMapping', 'quantityMapping'],
  ]
);

export const Rename = Yup.object({
  name: Yup.string().required('Field is required'),
});

export const Shop = Yup.object({});

const isFTPType = (val) =>
  ['ftp', 'ftps', 'sftp', 'implicit_ftp'].some((x) => val === x);
const notSFTPType = (val) =>
  ['ftp', 'ftps', 'implicit_ftp'].some((x) => val === x);

export const Source = Yup.object({
  sourceType: Yup.string(),
  sourceHost: Yup.string().when('sourceType', {
    is: (val) => isFTPType(val),
    then: (schema) => schema.required('Field is required'),
  }),
  sourceLogin: Yup.string().when('sourceType', {
    is: (val) => isFTPType(val),
    then: (schema) => schema.required('Field is required'),
  }),
  sourcePassword: Yup.string().when('sourceType', {
    is: (val) => notSFTPType(val),
    then: (schema) => schema.required('Field is required'),
  }),
  pathToFile: Yup.string().when('sourceType', {
    is: (val) => isFTPType(val),
    then: (schema) => schema.required('Field is required'),
  }),
  sourceUrl: Yup.string()
    .when('sourceType', {
      is: (val) => val === 'url',
      then: (schema) => schema.required('Field is required'),
    })
    .when('sourceType', {
      is: (val) => val === 'google_sheet',
      then: (schema) =>
        schema
          .matches(/^(https:\/\/docs\.google\.com\/)/, {
            message: 'Invalid url',
          })
          .required('Field is required'),
    }),
  googleSheetName: Yup.string().when('sourceType', {
    is: (val) => val === 'google_sheet',
    then: (schema) => schema.required('Field is required'),
  }),
});

export const Ticket = Yup.object({
  email: Yup.string().required('Field is required').email(),
  message: Yup.string().required('Field is required'),
  subject: Yup.string().required('Field is required'),
});

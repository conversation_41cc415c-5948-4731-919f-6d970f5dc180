import { Button, ButtonGroup } from '@shopify/polaris';
import type { OptionsObject } from 'notistack';
import * as React from 'react';

import { useNotification } from '@/hooks/useNotification';

// TODO: no react in hooks, rethink
export function useConfirmation() {
  const notification = useNotification();
  return {
    close: React.useCallback(() => notification.close(), [notification]),
    open: React.useCallback(
      ({
        message,
        onConfirm,
        onClose,
        options,
      }: {
        message: React.ReactNode;
        onConfirm: () => void;
        onClose?: () => void;
        options?: OptionsObject;
      }) => {
        notification.open({
          options: {
            key: 'unique', // prevent duplicates
            variant: 'info',
            persist: true,
            action: (
              <ButtonGroup>
                <Button
                  variant="primary"
                  size="slim"
                  onClick={() => onConfirm()}
                >
                  Yes
                </Button>
                <Button
                  size="slim"
                  onClick={() => {
                    notification.close();
                    onClose?.();
                  }}
                >
                  No
                </Button>
              </ButtonGroup>
            ),
            ...options,
          },
          message,
        });
      },
      [notification]
    ),
  };
}

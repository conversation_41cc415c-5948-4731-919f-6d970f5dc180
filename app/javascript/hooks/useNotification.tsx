import { closeSnackbar, enqueueSnackbar, type OptionsObject } from 'notistack';
import * as React from 'react';

// TODO: no react in hooks, rethink
export function useNotification() {
  const keyRef = React.useRef<string | number>('');
  return {
    close: React.useCallback(() => {
      closeSnackbar(keyRef.current);
    }, []),
    open: React.useCallback(
      ({
        options,
        message = 'Oppss. Something went wrong. Please try again.',
      }: {
        options: OptionsObject;
        message?: React.ReactNode;
      }) => {
        keyRef.current = enqueueSnackbar(
          <div style={{ color: '#212b36' }}>{message}</div>,
          {
            autoHideDuration: 3000,
            preventDuplicate: true,
            anchorOrigin: {
              horizontal: 'right',
              vertical: 'top',
            },
            style: {
              maxWidth: 320,
              fontSize: 14,
              '--p-banner-background': {
                default: '#f4f6f8',
                warning: '#fdf7e3',
                info: '#ffffff',
                success: '#eff7ed',
                error: '#fdf3f0',
              }[options.variant ?? 'default'],
              backgroundColor: 'var(--p-banner-background, #fff)',

              padding: '12px 16px',
              '--p-banner-border': {
                default: '#637381',
                warning: '#eec200',
                info: '#ffffff',
                success: '#50b83c',
                error: '#de3618',
              }[options.variant ?? 'default'],
              color: {
                default: '#637381',
                warning: '#eec200',
                info: '#eec200',
                success: '#50b83c',
                error: '#de3618',
              }[options.variant ?? 'default'],
              boxShadow:
                'inset 0 3px 0 0 var(--p-banner-border), inset 0 0 0 0 transparent, 0 0 0 1px rgba(63, 63, 68, 0.05), 0 1px 3px 0 rgba(63, 63, 68, 0.15)',
            } as React.CSSProperties,
            ...options,
          }
        );
      },
      []
    ),
  };
}

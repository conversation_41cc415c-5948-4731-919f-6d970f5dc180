import { useQuery, type QueryHookOptions } from '@apollo/client';
import * as React from 'react';

import { SourcesQuery } from '@/gql/queries';

export function useSources(options: QueryHookOptions = {}) {
  const query = useQuery(SourcesQuery, options);
  const sources = React.useMemo(() => {
    return query.data ? query.data.sources : [];
  }, [query]);
  return Object.assign(query, { sources });
}

import { useMutation } from '@apollo/client';
import * as React from 'react';

import { DeleteSourceMutation } from '@/gql/mutations';
import { useNotification } from '@/hooks/useNotification';
import * as routes from '@/routes';

export function useDeleteSource() {
  const notification = useNotification();
  const [deleteSource] = useMutation(DeleteSourceMutation);

  return {
    deleteSource: React.useCallback(
      (id) =>
        deleteSource({ variables: { id } })
          .then(() => {
            notification.open({
              options: { variant: 'success' },
              message:
                'Source deleted successfully. Redirecting to home page...',
            });
            setTimeout(() => {
              window.location.href = routes.dashboard;
            }, 500);
          })
          .catch((error) => {
            console.error(error);
            notification.open({ options: { variant: 'error' } });
          }),
      [deleteSource, notification]
    ),
  };
}

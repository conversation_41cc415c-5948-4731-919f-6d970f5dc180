import { useQuery, type QueryHookOptions } from '@apollo/client';

import { SourceQuery } from '@/gql/queries';

type ApiType = typeof SourceQuery.__apiType;
type Variables = Parameters<Exclude<ApiType, undefined>>[0];

export function useSource(options: QueryHookOptions<any, Variables>) {
  const query = useQuery(SourceQuery, options);
  const source = query.data!.source;
  return Object.assign(query, { source });
}

import { useMutation } from '@apollo/client';
import * as React from 'react';

import { CopySourceMutation } from '@/gql/mutations';
import { useNotification } from '@/hooks/useNotification';
import * as routes from '@/routes';

export function useCopySource() {
  const notification = useNotification();
  const [copySource] = useMutation(CopySourceMutation);

  return {
    copySource: React.useCallback(
      (id) =>
        copySource({ variables: { id } })
          .then(() => {
            notification.open({
              options: { variant: 'success' },
              message:
                'Source copied successfully. Redirecting to home page...',
            });
            setTimeout(() => {
              window.location.href = routes.dashboard;
            }, 500);
          })
          .catch((error) => {
            console.error(error);
            notification.open({
              options: { variant: 'error' },
              message: error.graphQLErrors[0]?.message ?? error?.message,
            });
          }),
      [copySource, notification]
    ),
  };
}

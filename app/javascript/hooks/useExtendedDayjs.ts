import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat'; // https://day.js.org/docs/en/plugin/custom-parse-format
import relativeTime from 'dayjs/plugin/relativeTime';
import timezone from 'dayjs/plugin/timezone';
import updateLocale from 'dayjs/plugin/updateLocale';
import utc from 'dayjs/plugin/utc';

import { useCurrentShop } from '@/hooks/useCurrentShop';

dayjs.extend(customParseFormat); // without extend, error `Invalid Date`
dayjs.extend(utc);
dayjs.extend(timezone); // requires utc plugin

// taken & modified from https://day.js.org/docs/en/customization/relative-time
dayjs.extend(relativeTime, {
  thresholds: [
    { l: 's', r: 1 },
    { l: 'ss', r: 59, d: 'second' },
    { l: 'm', r: 1 },
    { l: 'mm', r: 59, d: 'minute' },
    { l: 'h', r: 1 },
    { l: 'hh', r: 23, d: 'hour' },
    { l: 'd', r: 1 },
    { l: 'dd', r: 29, d: 'day' },
    { l: 'M', r: 1 },
    { l: 'MM', r: 11, d: 'month' },
    { l: 'y', r: 1 },
    { l: 'yy', d: 'year' },
  ],
});

// must be after relativeTime to work
dayjs.extend(updateLocale);
dayjs.updateLocale('en', {
  relativeTime: {
    future: 'in %s',
    past: '%s ago',
    s: 'a few seconds',
    ss: '%d seconds',
    m: '%d minute',
    mm: '%d minutes',
    h: '%d hour',
    hh: '%d hours',
    d: '%d day',
    dd: '%d days',
    M: '%d month',
    MM: '%d months',
    y: '%d year',
    yy: '%d years',
  },
});

let timezoneInitialized = false;

export function useExtendedDayjs() {
  const timezone = useCurrentShop().currentShop.timezone;
  if (!timezoneInitialized) {
    timezoneInitialized = true;
    dayjs.tz.setDefault(timezone.includes('/') ? timezone : 'America/New_York');
  }

  return { xdayjs: dayjs };
}

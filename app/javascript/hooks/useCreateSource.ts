import { useMutation } from '@apollo/client';
import * as React from 'react';
import { useNavigate } from 'react-router-dom';

import { CreateSourceMutation } from '@/gql/mutations';
import { useCurrentShop } from '@/hooks/useCurrentShop';
import { useNotification } from '@/hooks/useNotification';
import * as routes from '@/routes';

export function useCreateSource() {
  const notification = useNotification();
  const [createSource, createSourceMutation] =
    useMutation(CreateSourceMutation);
  const navigate = useNavigate();
  const { currentShop } = useCurrentShop();

  return {
    createSource: React.useCallback(() => {
      if (currentShop.provider === 'Shopify') {
        navigate(routes.newSource);
      } else {
        return createSource()
          .then((res) => {
            notification.open({
              options: { variant: 'success' },
              message: `${res.data.createSource.name} created. Redirecting to settings page...`,
            });
            setTimeout(
              () =>
                (window.location.href = routes.getSourceSettingsRoute(
                  res.data.createSource.id
                )),
              500
            );
          })
          .catch((error) => {
            if (error.graphQLErrors) {
              notification.open({
                options: { variant: 'error' },
                message: error.graphQLErrors[0]?.message ?? error?.message,
              });
            } else {
              console.error(error);
              notification.open({ options: { variant: 'error' } });
            }
          });
      }
    }, [navigate, createSource, currentShop, notification]),
    createSourceMutation,
  };
}

export type Package = 'basic' | 'pro' | 'unlimited' | 'trial';

export interface Shop {
  bigcommerceStoreHash: string | null;
  bigcommerceClientId: string | null;
  bigcommerceAccessToken: string | null;
  connected: boolean;
  createdAt: string;
  credit: number;
  ecwidStoreId: string | null;
  ecwidStoreUrl: string | null;
  email: string;
  emailNotificationCustomTimeEnabled: boolean;
  emailNotificationEndTime: string;
  emailNotificationStartTime: string;
  emailSubscriptions: string[];
  expiredDay: number;
  id: string;
  lowCreditAlert: string;
  notificationEmail: string;
  package: Package;
  paypalClientId: string | null;
  paypalClientSecret: string | null;
  platform: 'shopify' | 'big_commerce';
  provider: 'BigCommerce' | 'Shopify';
  scheduleMinHour: number;
  shopifyDomain: string;
  storeDomain: string;
  timezone: string;
  useCredit: boolean;
}

export interface ShopLocation {
  id: string;
  name: string;
}

export interface SyncLog {
  caller: 'ui';
  errorMessage: string;
  id: string;
  numberFulfillmentUpdated: number | null;
  processedAt: string;
  source: {
    id: string;
    name: string;
  };
  status: 'no_data' | 'success';
}

export type SourceProcess =
  | 'rename'
  | 'move'
  | 'delete'
  | 'append_date'
  | 'none'
  | null;

export type SourceType =
  | 'bigcaring'
  | 'email'
  | 'file_upload'
  | 'ftp'
  | 'ftps'
  | 'google_sheet'
  | 'implicit_ftp'
  | 'mstgolf'
  | 'sftp'
  | 'smiffys'
  | 'url'
  | 'wholecell';

type SourcePlatform = 'big_commerce' | 'ecwid' | 'shopify';

export interface FileReplacement {
  _destroy: '1';
  column: 'tracking_company_mapping' | 'tracking_url_mapping';
  nil_if_not_found: boolean;
  replace_from: string;
  replace_to: string;
}
export interface Source {
  afterFulfilledOrderFinancialStatus: string | null;
  allowBlankTrackingNo: boolean;
  autoDetectTracking: boolean;
  bigcommerceOrderStatus: string;
  columnSeparator: ',';
  ecwidPaymentStatus: string;
  email: string;
  excludeInventoryManagement: string | null;
  fileReplacements: Array<FileReplacement>;
  financialStatus: string;
  ftpMode: 'passive';
  googleSheetName: string;
  id: string;
  ignoreEmptySku: boolean;
  ignoreKey: string | null;
  ignoreValue: string | null;
  lineItemIdentifier: string;
  locationId: string;
  locationMapping: string | null;
  locationName: string | null;
  name: string;
  notifyCustomer: boolean;
  orderDaysAgo: string;
  orderIdentifierConstants: string | null;
  orderKey: 'id' | 'order_number' | 'name' | 'number';
  orderNoMapping: string;
  parentNode: string;
  pathToFile: string | null;
  platform: SourcePlatform;
  progress: number;
  quantityMapping: string | null;
  scheduleDay: string | null;
  scheduleInterval: number;
  scheduleTime: string | null;
  scheduleType: 'daily' | 'hourly';
  shopifyOrderKeyConstants: string | null;
  skuMapping: string | null;
  skuPrefix: string | null;
  sourceFile: File | undefined;
  sourceHost: string;
  sourceLogin: string | null;
  sourcePassword: string | null;
  sourceProcess: SourceProcess;
  sourceRename: string | null;
  sourceType: SourceType;
  sourceUrl: string | null;
  sshKey: string | null;
  status: 'queuing' | 'running';
  syncLog: SyncLog | null;
  syncStatus: 'paused' | 'started';
  tagEnabled: boolean;
  tagValue: string;
  templateId: null;
  trackingCompanyDefault: string | null;
  trackingCompanyMapping: string | null;
  trackingNoMapping: string | null;
  trackingUrlDefault: string | null;
  trackingUrlMapping: string | null;
  updatedAt: string;
}

export interface PaypalOrder {
  createdAt: string;
  errorMessage: string;
  fulfillmentStatus: 'true';
  id: string;
  orderNumber: string;
  paypalSuccess: 'Fail';
  paypalTransaction: string;
  quantity: number;
  shopifyFulfillmentId: string;
  shopifyOrderId: string;
  sku: string;
  trackingCompany: null;
  trackingNo: string;
}

export interface FulfillmentLog {
  __typename: string;
  bigcommerceShipmentId: null;
  createdAt: string;
  errorMessage: string;
  fulfillmentStatus: string;
  id: string;
  orderNumber: string;
  quantity: null;
  shopifyFulfillmentId: string;
  shopifyOrderId: string;
  sku: string;
  trackingCompany: string;
  trackingNo: string;
}

interface ColumnMappingData {
  ignoreColumn: string | null;
  itemQuantity: string | null;
  itemSku: string | null;
  orderNo: string | null;
  trackingCompany: string | null;
  trackingNo: string | null;
  trackingUrl: string | null;
}
interface SampleShopifyOrder {
  id: string | null;
  number: string | null;
  orderName: string | null;
  orderNumber: string | null;
}

type SourceMapping =
  | 'item_quantity'
  | 'item_sku'
  | 'order_no'
  | 'tracking_company'
  | 'tracking_no'
  | 'tracking_url';

export interface TestColumnMapping {
  data: ColumnMappingData;
  remark: string | null;
  sampleShopifyOrder: SampleShopifyOrder;
  sourceMappings: Array<SourceMapping> | null;
  status: boolean;
}

import { useMutation, useQuery } from '@apollo/client';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  EmptySearchResult,
  FormLayout,
  IndexTable,
  Text,
  TextField,
} from '@shopify/polaris';
import { Formik } from 'formik';
import * as React from 'react';

import { CurrentPage } from '@/components/CurrentPage';
import { Link } from '@/components/Link';
import { EditShopMutation } from '@/gql/mutations';
import { ShopPaypalOrderQuery } from '@/gql/queries';
import { useCurrentShop } from '@/hooks/useCurrentShop';
import { useNotification } from '@/hooks/useNotification';
import * as routes from '@/routes';
import * as schemas from '@/schemas';
import type { PaypalOrder } from '@/types';

// import { mockData } from './mock'; // TODO: temp

export function Paypal() {
  const notification = useNotification();
  const shop = useCurrentShop().currentShop;
  const [editShop, editShopMutation] = useMutation(EditShopMutation);
  const [paypalOrders, setPaypalOrders] = React.useState<Array<PaypalOrder>>(
    []
  );

  const [currentPage, setCurrentPage] = React.useState(1);
  const [itemsPerPage] = React.useState(20);
  const paypalOrdersQuery = useQuery(ShopPaypalOrderQuery, {
    variables: { page: currentPage },
  });

  React.useEffect(() => {
    if (paypalOrdersQuery.loading) return;

    const shopPaypalOrder = paypalOrdersQuery.data?.shopPaypalOrder;
    if (shopPaypalOrder) {
      setPaypalOrders((prev) => {
        // TODO: mock data for UI testing / refactoring
        // return prev.concat(mockData.data.shopPaypalOrder);
        return prev.concat(shopPaypalOrder);
      });
    }
  }, [paypalOrdersQuery]);

  console.log('paypalOrders', paypalOrders);

  // Paginated data for IndexTable
  const paginatedData = React.useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return paypalOrders.slice(startIndex, endIndex);
  }, [paypalOrders, currentPage, itemsPerPage]);

  // Calculate total pages
  const totalPages = Math.ceil(paypalOrders.length / itemsPerPage);
  const hasNext = currentPage < totalPages;
  const hasPrevious = currentPage > 1;

  return (
    <CurrentPage
      titleMetadata="Paypal"
      backAction={{
        content: 'Back to dashboard',
        url: routes.dashboard,
      }}
      secondaryActions={[
        {
          content: 'Preferences',
          url: routes.editPreferences,
        },
      ]}
    >
      <Formik
        enableReinitialize
        initialValues={{
          paypalClientId: shop.paypalClientId || '',
          paypalClientSecret: shop.paypalClientSecret || '',
        }}
        validationSchema={schemas.Shop}
        onSubmit={(values) => {
          editShop({
            variables: {
              shop: {
                id: shop.id,
                paypalClientId: values.paypalClientId,
                paypalClientSecret: values.paypalClientSecret,
              },
            },
          })
            .then((res) => {
              notification.open({
                options: { variant: 'success' },
                message: 'Preferences saved successfully.',
              });
              if (res.data.editShop.connected) {
                window.location.href = routes.dashboard;
              }
            })
            .catch((error) => {
              console.error(error);
              notification.open({ options: { variant: 'error' } });
            });
        }}
      >
        {({ handleSubmit, values, errors, setFieldValue }) => (
          <form onSubmit={handleSubmit} style={{ marginBottom: '20px' }}>
            <Card>
              <FormLayout>
                <TextField
                  label="Client ID"
                  onChange={(value) => {
                    setFieldValue('paypalClientId', value);
                  }}
                  value={values.paypalClientId}
                  error={errors.paypalClientId}
                  autoComplete="on"
                />
                <TextField
                  label="Secret"
                  type="password"
                  onChange={(value) => {
                    setFieldValue('paypalClientSecret', value);
                  }}
                  value={values.paypalClientSecret}
                  error={errors.paypalClientSecret}
                  autoComplete="new-password"
                />
              </FormLayout>
              <div
                style={{
                  marginTop: '16px',
                  textAlign: 'right',
                  display: 'block',
                }}
              >
                <Button
                  variant="primary"
                  submit
                  loading={editShopMutation.loading}
                >
                  Save
                </Button>
              </div>
            </Card>
          </form>
        )}
      </Formik>
      <div style={{ marginBottom: '16px' }}>
        <Text variant="headingMd" as="h2">
          Recent synced orders to Paypal
        </Text>
      </div>
      <Card padding="0">
        <IndexTable
          itemCount={paginatedData.length}
          selectable={false}
          loading={paypalOrdersQuery.loading}
          emptyState={
            <EmptySearchResult title="No orders found" withIllustration />
          }
          pagination={{
            hasNext,
            onNext: () => setCurrentPage(currentPage + 1),
            hasPrevious,
            onPrevious: () => setCurrentPage(currentPage - 1),
            label: `${(currentPage - 1) * itemsPerPage + 1}–${Math.min(currentPage * itemsPerPage, paypalOrders.length)} of ${paypalOrders.length} items`,
          }}
          headings={[
            { title: 'Order ID' },
            { title: 'Payment' },
            { title: 'Tracking Info (Tracking no, Company)' },
            { title: 'Status' },
            { title: '' },
          ]}
        >
          {paginatedData.map((po, index) => (
            <IndexTable.Row id={po.id} key={po.id} position={index}>
              <IndexTable.Cell>
                <Link
                  external
                  url={`https://${shop.shopifyDomain}/admin/orders/${po.shopifyOrderId}`}
                >
                  {po.orderNumber}
                </Link>
              </IndexTable.Cell>
              <IndexTable.Cell>Paypal</IndexTable.Cell>
              <IndexTable.Cell>
                <>
                  <div style={{ marginBottom: '4px' }}>
                    Tracking no: {po.trackingNo ?? '-'}
                  </div>
                  <div>Company: {po.trackingCompany ?? '-'}</div>
                </>
              </IndexTable.Cell>
              <IndexTable.Cell>
                <Badge
                  tone={po.paypalSuccess === 'Fail' ? 'critical' : 'success'}
                >
                  {po.paypalSuccess}
                </Badge>
              </IndexTable.Cell>
              <IndexTable.Cell>
                <Link
                  external
                  url={`https://www.paypal.com/activity/payment/${po.paypalTransaction}`}
                >
                  View on Paypal
                </Link>
              </IndexTable.Cell>
            </IndexTable.Row>
          ))}
        </IndexTable>
      </Card>
    </CurrentPage>
  );
}

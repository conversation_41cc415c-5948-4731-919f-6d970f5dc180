import {
  <PERSON><PERSON>,
  Card,
  FormLayout,
  Layout,
  Select,
  Text,
  TextField,
} from '@shopify/polaris';
import { useFormikContext } from 'formik';
import * as React from 'react';

import { Link } from '@/components/Link';
import type { SourceType } from '@/types';

import type { FormValues } from '.';
import { FileNameHelp } from './FileNameHelp';

const sourceTypeOptions: Array<{
  label: string;
  value: SourceType;
}> = [
  {
    label: 'FTP',
    value: 'ftp',
  },
  {
    label: 'FTP with TLS',
    value: 'ftps',
  },
  {
    label: 'Implicit FTP',
    value: 'implicit_ftp',
  },
  {
    label: 'SFTP',
    value: 'sftp',
  },
  {
    label: 'Direct Link(URL)',
    value: 'url',
  },
  {
    label: 'File Upload',
    value: 'file_upload',
  },
  {
    label: 'Receive Email',
    value: 'email',
  },
  {
    label: 'Google Sheet',
    value: 'google_sheet',
  },
  {
    label: 'Smiffys Rest API',
    value: 'smiffys',
  },
  {
    label: 'WholeCell API',
    value: 'wholecell',
  },
];

const sourceProcessOptions: Array<{
  label: string;
  value: FormValues['sourceProcess'];
}> = [
  {
    label: 'Do Nothing To My File',
    value: 'none',
  },
  {
    label: 'Rename My File',
    value: 'rename',
  },
  {
    label: 'Append Date To My File(<datetime>_filename)',
    value: 'append_date',
  },
  {
    label: 'Delete My File',
    value: 'delete',
  },
  {
    label: 'Move My File',
    value: 'move',
  },
];

export function SourceTypeForm() {
  const { values, setFieldValue } = useFormikContext<FormValues>();

  return (
    <>
      <>
        <Layout>
          <Layout.AnnotatedSection
            title="Source Type"
            description={
              <div>
                <p>This is the first step of setting up your source.</p>
                <p>
                  Make sure you fill up your source type with its respective
                  fields.{' '}
                  <Link
                    target="_blank"
                    url="https://fulfilltracking.freshdesk.com/support/solutions/articles/44002602700-set-up-source-type"
                  >
                    Learn more
                  </Link>
                </p>
                <div style={{ marginTop: 20 }} />
                <p>Note:</p>
                <p>
                  Click on Next to continue setting up. Leaving the setting page
                  now will not allow you to sync properly.
                </p>
              </div>
            }
          >
            <Card>
              <FormLayout>
                {!['bigcaring', 'mstgolf'].includes(values.sourceType) && (
                  <Select
                    label="Source Type"
                    options={sourceTypeOptions}
                    value={values.sourceType}
                    onChange={(value) => {
                      setFieldValue('sourceType', value);
                    }}
                    placeholder="Select source type"
                    helpText={
                      ['implicit_ftp', 'ftp', 'ftps', 'sftp'].includes(
                        values.sourceType
                      )
                        ? 'Please whitelist "app.fulfillsync.com" (or use IP: ************) in your server for it to work'
                        : ''
                    }
                  />
                )}
                <div>
                  {{
                    ftp: <Ftp />,
                    ftps: <Ftp />,
                    implicit_ftp: <Ftp />,
                    sftp: <Sftp />,
                    url: <Url />,
                    file_upload: <div />,
                    email: <Email />,
                    google_sheet: <GoogleSheet />,
                    smiffys: <Smiffys />,
                    wholecell: <Wholecell />,
                  }[values.sourceType] || <span>Auto Completed</span>}
                </div>
              </FormLayout>
            </Card>
          </Layout.AnnotatedSection>
        </Layout>
      </>
    </>
  );
}

function Ftp() {
  const { setFieldValue, values, errors } = useFormikContext<FormValues>();
  function selectAfterProcess() {
    if (values.sourceProcess === 'rename' || values.sourceProcess === 'move') {
      const labels = {
        rename: {
          label: 'Rename My File To (optional)',
          helpText: null,
        },
        move: {
          label: 'Move My File To',
          helpText: 'Works only if the folder specified above exists.',
        },
      };

      return (
        <TextField
          label={labels[values.sourceProcess].label}
          onChange={(text) => {
            setFieldValue('sourceRename', text);
          }}
          value={values.sourceRename}
          error={errors.sourceRename}
          helpText={labels[values.sourceProcess].helpText}
          autoComplete="on"
        />
      );
    }
  }

  return (
    <FormLayout>
      <TextField
        label="Host"
        onChange={(text) => {
          setFieldValue('sourceHost', text);
        }}
        value={values.sourceHost}
        error={errors.sourceHost}
        helpText="FTP Host eg. vendor.com:20, ftp.vendor.com, ***********"
        autoComplete="on"
      />
      <FormLayout.Group condensed>
        <TextField
          type="password"
          autoComplete="new-password"
          label="Username"
          onChange={(text) => {
            setFieldValue('sourceLogin', text);
          }}
          value={values.sourceLogin}
          error={errors.sourceLogin}
        />
        <TextField
          autoComplete="new-password"
          label="Password"
          type="password"
          onChange={(text) => {
            setFieldValue('sourcePassword', text);
          }}
          value={values.sourcePassword}
          error={errors.sourcePassword}
        />
      </FormLayout.Group>
      <Text variant="bodySm" as="p">
        Why? Saved login credential to automate file download.
      </Text>
      <FormLayout.Group>
        <TextField
          autoComplete="off"
          label="File / File path"
          onChange={(text) => {
            setFieldValue('pathToFile', text);
          }}
          value={values.pathToFile}
          error={errors.pathToFile}
          helpText={
            <div>
              eg. Path and file name. eg. /www/project/data/inventory.csv. For
              file name with timestamp, please click <FileNameHelp /> for more
              info.
              <div>
                To process multiple files in the path, please use{' '}
                <strong>*</strong> as multi-select.
              </div>
              <div>
                eg. /www/project/data/<strong>*</strong>
              </div>
            </div>
          }
        />
      </FormLayout.Group>
      <Select
        label="After Process"
        options={sourceProcessOptions}
        onChange={(value) => {
          setFieldValue('sourceProcess', value);
        }}
        value={values.sourceProcess}
        helpText={
          values.sourceProcess === 'delete' ? (
            <div>
              Recommend using Append Date, Rename File or Move File for better
              debugging process if any error occurs to retrieve the file.
            </div>
          ) : (
            <div />
          )
        }
      />
      {selectAfterProcess()}
    </FormLayout>
  );
}

function Sftp() {
  const { setFieldValue, values, errors } = useFormikContext<FormValues>();

  function selectAfterProcess() {
    if (values.sourceProcess === 'rename' || values.sourceProcess === 'move') {
      const labels = {
        rename: {
          label: 'Rename My File To (optional)',
          helpText: null,
        },
        move: {
          label: 'Move My File To',
          helpText: 'Works only if the folder specified above exists.',
        },
      };

      return (
        <TextField
          label={labels[values.sourceProcess].label}
          onChange={(text) => {
            setFieldValue('sourceRename', text);
          }}
          value={values.sourceRename}
          error={errors.sourceRename}
          helpText={labels[values.sourceProcess].helpText}
          autoComplete="on"
        />
      );
    }
  }

  return (
    <FormLayout>
      <TextField
        label="Host"
        onChange={(text) => {
          setFieldValue('sourceHost', text);
        }}
        value={values.sourceHost}
        error={errors.sourceHost}
        helpText="FTP Host eg. vendor.com:20, ftp.vendor.com, ***********"
        autoComplete="on"
      />
      <FormLayout.Group condensed>
        <TextField
          type="password"
          autoComplete="new-password"
          label="Username"
          onChange={(text) => {
            setFieldValue('sourceLogin', text);
          }}
          value={values.sourceLogin}
          error={errors.sourceLogin}
        />
        <TextField
          autoComplete="new-password"
          label="Password"
          type="password"
          onChange={(text) => {
            setFieldValue('sourcePassword', text);
          }}
          value={values.sourcePassword}
          error={errors.sourcePassword}
        />
      </FormLayout.Group>
      <Text variant="bodySm" as="p">
        Why? Saved login credential to automate file download.
      </Text>
      <FormLayout.Group>
        <TextField
          label="File / File path"
          onChange={(text) => {
            setFieldValue('pathToFile', text);
          }}
          value={values.pathToFile}
          error={errors.pathToFile}
          helpText={
            <div>
              eg. Path and file name. eg. /www/project/data/inventory.csv. For
              file name with timestamp, please click <FileNameHelp /> for more
              info.
              <div>
                To process multiple files in the path, please use{' '}
                <strong>*</strong> as multi-select.
              </div>
              <div>
                eg. /www/project/data/<strong>*</strong>
              </div>
            </div>
          }
          autoComplete="on"
        />
      </FormLayout.Group>
      <TextField
        label="SSH key"
        multiline={5}
        onChange={(text) => {
          setFieldValue('sshKey', text);
        }}
        value={values.sshKey}
        error={errors.sshKey}
        autoComplete="off"
      />
      <Select
        label="After Process"
        options={sourceProcessOptions}
        onChange={(value) => {
          setFieldValue('sourceProcess', value);
        }}
        value={values.sourceProcess}
        helpText={
          values.sourceProcess === 'delete' ? (
            <div>
              Recommend using Append Date, Rename File or Move File for better
              debugging process if any error occurs to retrieve the file.
            </div>
          ) : (
            <div />
          )
        }
      />
      {selectAfterProcess()}
    </FormLayout>
  );
}

function Wholecell() {
  const { setFieldValue, values, errors } = useFormikContext<FormValues>();
  return (
    <FormLayout>
      <TextField
        label="Username"
        type="text"
        onChange={(text) => {
          setFieldValue('sourceLogin', text);
        }}
        value={values.sourceLogin}
        error={errors.sourceLogin}
        autoComplete="on"
      />
      <TextField
        label="Password"
        type="password"
        onChange={(text) => {
          setFieldValue('sourcePassword', text);
        }}
        value={values.sourcePassword}
        error={errors.sourcePassword}
        autoComplete="off"
      />
    </FormLayout>
  );
}

function Smiffys() {
  const { setFieldValue, values, errors } = useFormikContext<FormValues>();
  return (
    <FormLayout>
      <TextField
        label="Client ID"
        onChange={(text) => {
          setFieldValue('sourceLogin', text);
        }}
        value={values.sourceLogin}
        error={errors.sourceLogin}
        helpText="EF_YOURCUSTOMERID"
        autoComplete="on"
      />
      <TextField
        label="API Key"
        onChange={(text) => {
          setFieldValue('sourcePassword', text);
        }}
        value={values.sourcePassword}
        error={errors.sourcePassword}
        helpText="aa36302a322a82a9a43a5a149439a59a"
        autoComplete="off"
      />
    </FormLayout>
  );
}

function GoogleSheet() {
  const { setFieldValue, values, errors } = useFormikContext<FormValues>();

  return (
    <FormLayout>
      <TextField
        label="Google Sheet URL"
        helpText={
          <span>
            Please change Google permission to{' '}
            <strong>Anyone with the link</strong> for it to work
          </span>
        }
        placeholder="https://docs.google.com/spreadsheets/d/1e5reT2VVxuWnw9er2AJq..."
        onChange={(text) => {
          setFieldValue('sourceUrl', text);
        }}
        value={values.sourceUrl}
        error={errors.sourceUrl}
        autoComplete="on"
      />
      <FormLayout.Group condensed>
        <TextField
          label="Sheet name"
          onChange={(text) => {
            setFieldValue('googleSheetName', text);
          }}
          value={values.googleSheetName}
          error={errors.googleSheetName}
          autoComplete="on"
        />
      </FormLayout.Group>
    </FormLayout>
  );
}

function Email() {
  const { values } = useFormikContext<FormValues>();
  const [copiedToClipboard, setCopiedToClipboard] = React.useState(false);

  React.useEffect(() => {
    if (!copiedToClipboard) return;
    const timeout = setTimeout(() => setCopiedToClipboard(false), 1000);
    return () => clearTimeout(timeout);
  }, [copiedToClipboard]);

  return (
    <TextField
      label="Email Recipient"
      helpText="Please send an email with attachment of your orders, the fulfilment process will run everytime you send an email to the above address."
      value={values.email}
      readOnly
      autoComplete="off"
      connectedRight={
        <Button
          variant="primary"
          onClick={() => {
            navigator.clipboard.writeText(values.email).then(() => {
              setCopiedToClipboard(true);
            });
          }}
        >
          {copiedToClipboard ? 'Copied' : 'Copy'}
        </Button>
      }
    />
  );
}

function Url() {
  const { setFieldValue, values, errors } = useFormikContext<FormValues>();
  return (
    <FormLayout>
      <TextField
        label="Direct URL"
        placeholder="http://example.com/your_path/tracking.csv"
        onChange={(url) => {
          setFieldValue('sourceUrl', url);
        }}
        value={values.sourceUrl}
        error={errors.sourceUrl}
        autoComplete="on"
      />
      <FormLayout.Group condensed>
        <TextField
          type="password"
          autoComplete="new-password"
          label="Username (Optional)"
          onChange={(text) => {
            setFieldValue('sourceLogin', text);
          }}
          value={values.sourceLogin}
          error={errors.sourceLogin}
        />
        <TextField
          type="password"
          autoComplete="new-password"
          label="Password (Optional)"
          onChange={(text) => {
            setFieldValue('sourcePassword', text);
          }}
          value={values.sourcePassword}
          error={errors.sourcePassword}
        />
      </FormLayout.Group>
      <Text variant="bodySm" as="p">
        Why? Saved login credential to automate file download. Please leave
        empty if download link doesn't require any authentication.
      </Text>
    </FormLayout>
  );
}

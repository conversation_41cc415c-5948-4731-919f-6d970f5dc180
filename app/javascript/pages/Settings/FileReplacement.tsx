import {
  But<PERSON>,
  Card,
  Checkbox,
  FormLayout,
  Layout,
  Select,
  Text,
  TextField,
} from '@shopify/polaris';
import { XCircleIcon } from '@shopify/polaris-icons';
import { FieldArray, getIn, useFormikContext } from 'formik';

import type { FileReplacement } from '@/types';

import { type FormValues } from '.';
import classes from './FileReplacement.module.css';

export function FileReplacement() {
  const { values, setFieldValue, errors } = useFormikContext<FormValues>();

  return (
    <Layout.AnnotatedSection
      title="Find/Replace"
      description={
        <>
          <p>Replace certain keywords for better customisation. Example,</p>
          <p>UPS Express = UPS</p>
        </>
      }
    >
      <Card>
        <div style={{ marginBottom: '20px', display: 'inline-block' }}>
          <Text variant="headingSm" as="h3">
            Find/Replace
          </Text>
        </div>
        &nbsp;
        <FieldArray
          name="fileReplacements"
          render={(arrayHelpers) => (
            <>
              {values.fileReplacements.map(
                (fr, index) =>
                  fr._destroy !== '1' && (
                    <div key={index} className={classes.row}>
                      <Button
                        onClick={() => {
                          setFieldValue(
                            `fileReplacements.${index}._destroy`,
                            '1'
                          );
                        }}
                        icon={XCircleIcon}
                      />
                      <div style={{ paddingBottom: '16px' }} />
                      <FormLayout.Group condensed>
                        <Select
                          label="Column"
                          options={
                            [
                              {
                                value: 'tracking_company_mapping',
                                label: 'Tracking Company Mapping',
                              },
                              {
                                value: 'tracking_url_mapping',
                                label: 'Tracking URL Mapping',
                              },
                            ] satisfies Array<{
                              value: FileReplacement['column'];
                              label: string;
                            }>
                          }
                          value={fr.column}
                          onChange={(value) => {
                            setFieldValue(
                              `fileReplacements.${index}.column`,
                              value
                            );
                          }}
                        />
                        <TextField
                          label="Find"
                          value={fr.replace_from}
                          onChange={(value) => {
                            setFieldValue(
                              `fileReplacements.${index}.replace_from`,
                              value
                            );
                          }}
                          // https://github.com/jaredpalmer/formik/issues/2347#issuecomment-1090081787
                          // TODO: replace formik with react-hook-form
                          error={getIn(
                            errors,
                            `fileReplacements.${index}.replace_from`
                          )}
                          autoComplete="off"
                        />
                        <TextField
                          label="Replace with"
                          value={fr.replace_to}
                          onChange={(value) => {
                            setFieldValue(
                              `fileReplacements.${index}.replace_to`,
                              value
                            );
                          }}
                          autoComplete="off"
                        />
                      </FormLayout.Group>
                      <Checkbox
                        label="Nil if not found"
                        checked={fr.nil_if_not_found}
                        onChange={(value) =>
                          setFieldValue(
                            `fileReplacements.${index}.nil_if_not_found`,
                            value
                          )
                        }
                      />
                    </div>
                  )
              )}
              <Button
                variant="plain"
                onClick={() =>
                  arrayHelpers.push({
                    column: 'tracking_company_mapping',
                    replace_from: '',
                    replace_to: '',
                    nil_if_not_found: false,
                  })
                }
              >
                Add find/replace
              </Button>
            </>
          )}
        />
      </Card>
    </Layout.AnnotatedSection>
  );
}

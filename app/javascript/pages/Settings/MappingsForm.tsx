import { useQuery } from '@apollo/client';
import {
  <PERSON><PERSON>,
  Card,
  Checkbox,
  FormLayout,
  Layout,
  Popover,
  Select,
  TextField,
} from '@shopify/polaris';
import { useFormikContext } from 'formik';
import * as React from 'react';

import { Link } from '@/components/Link';
import { ShopLocationsQuery } from '@/gql/queries';
import * as routes from '@/routes';
import type { ShopLocation } from '@/types';

import type { FormValues } from '.';
import { MappingField } from './MappingField';

const lineItemOptions = [
  { label: 'SKU', value: 'sku' },
  { label: 'Line item ID', value: 'id' },
];

const orderKeyOptions = [
  {
    label: 'Order Number',
    value: 'order_number',
    example: '1108',
  },
  {
    label: 'Order Name = Prefix + Order Number',
    value: 'name',
    example: '#1108',
  },
  {
    label: 'Shopify Order ID',
    value: 'id',
    example: '406492184611',
  },
  {
    label: 'Order Sequence Number',
    value: 'number',
    example: '108',
  },
].map(({ label, value, example }) => ({
  value,
  label: `${label} (e.g. ${example})`,
}));

function useShopLocations() {
  const query = useQuery(ShopLocationsQuery);
  const defaultLocations: Array<ShopLocation> = [
    { id: '', name: query.loading ? 'Loading...' : 'Auto Select' },
  ];
  return Object.assign(query, {
    locations: defaultLocations.concat(
      query.data?.currentShop?.locations ?? []
    ),
  });
}

export function MappingsForm() {
  const { values, setFieldValue, errors } = useFormikContext<FormValues>();
  const [visible, setVisible] = React.useState(false);
  const [skuPrefixVisible, setSkuPrefixVisible] = React.useState(false);
  const [popoverOpen, setPopoverOpen] = React.useState(false);
  const locationsQuery = useShopLocations();
  const { locations } = locationsQuery;
  const locationOptions = React.useMemo(
    () => locations.map((l) => ({ value: l.id, label: l.name })),
    [locations]
  );

  return (
    <>
      <Layout>
        <Layout.AnnotatedSection
          title={
            <span>
              Order{' '}
              <Button
                variant="plain"
                url={routes.downloadSampleCsv}
                size="micro"
              >
                (Download sample file)
              </Button>
            </span>
          }
          description={
            <div>
              <p>
                Map your order by recognizing it from the list provided and your
                column number. <strong>Column index begins with 0</strong>. The
                column sequence is 0,1,2, ...
                <span style={{ display: 'block' }}>
                  <Link
                    target="_blank"
                    url="https://fulfilltracking.freshdesk.com/support/solutions/articles/44002602771-set-up-mapping-and-configuration"
                  >
                    Learn more
                  </Link>
                </span>
              </p>
            </div>
          }
        >
          <Card>
            <FormLayout>
              <FormLayout.Group>
                <Select
                  label="Recognize my order by"
                  options={orderKeyOptions}
                  onChange={(values) => {
                    setFieldValue('orderKey', values);
                  }}
                  value={values.orderKey}
                />
                <MappingField
                  label="and matched by (column number)"
                  field="orderNoMapping"
                  mapping={values.orderNoMapping}
                />
              </FormLayout.Group>
            </FormLayout>

            <div style={{ margin: '10px 0px' }}>
              <Button
                variant="plain"
                onClick={() => setVisible((prev) => !prev)}
              >
                Add prefix/suffix to match orders{' '}
              </Button>
            </div>

            <div>
              {visible && (
                <>
                  <p style={{ textTransform: 'none', marginBottom: '10px' }}>
                    The prefix and suffix of the order number that needs to be
                    ignored while matching orders. It also support regular
                    expression.
                  </p>
                  <FormLayout>
                    <FormLayout.Group condensed>
                      <TextField
                        label="Shopify Prefix/Suffix"
                        onChange={(text) => {
                          setFieldValue('shopifyOrderKeyConstants', text);
                        }}
                        value={values.shopifyOrderKeyConstants}
                        helpText="The prefix or suffix of the order number on Shopify store."
                        autoComplete="on"
                      />
                      <TextField
                        label="Feed Prefix/Suffix"
                        placeholder="PRE-"
                        onChange={(text) => {
                          setFieldValue('orderIdentifierConstants', text);
                        }}
                        value={values.orderIdentifierConstants}
                        helpText="The prefix or suffix of the order number from feed file."
                        autoComplete="on"
                      />
                    </FormLayout.Group>
                  </FormLayout>
                </>
              )}
            </div>
          </Card>
        </Layout.AnnotatedSection>
      </Layout>

      <FormLayout>
        <Layout>
          <Layout.AnnotatedSection
            title="Tracking"
            description={
              <div>
                <p>
                  Map your tracking with any or all the fields with your column
                  number.
                </p>
                <br />
                <p>
                  Configure your fulfillment by adding tracking information.
                </p>
                <br />
              </div>
            }
          >
            <Card>
              <FormLayout>
                <FormLayout.Group condensed>
                  <MappingField
                    label={
                      <span>
                        Tracking Number{' '}
                        <small>
                          <i>optional</i>
                        </small>
                      </span>
                    }
                    field="trackingNoMapping"
                    mapping={values.trackingNoMapping}
                  />
                  <Checkbox
                    label="Allow blank tracking no"
                    helpText={
                      'If checked, it will fulfill orders even the tracking number is empty.'
                    }
                    checked={values.allowBlankTrackingNo}
                    onChange={(value) =>
                      setFieldValue('allowBlankTrackingNo', value)
                    }
                  />
                </FormLayout.Group>
                <Checkbox
                  label="Shopify auto detect tracking company"
                  checked={values.autoDetectTracking}
                  onChange={(value) => {
                    setFieldValue('autoDetectTracking', value);
                    value === false &&
                      values.trackingCompanyDefault.length === 0 &&
                      setFieldValue('trackingCompanyDefault', 'Other');
                  }}
                />
                {values.autoDetectTracking === false && (
                  <FormLayout>
                    <FormLayout.Group>
                      <MappingField
                        label={
                          <span>
                            Tracking URL{' '}
                            <small>
                              <i>optional</i>
                            </small>
                          </span>
                        }
                        field="trackingUrlMapping"
                        mapping={values.trackingUrlMapping}
                      />
                      <TextField
                        label={
                          <span>
                            Default Tracking URL{' '}
                            <small>
                              <i>optional</i>
                            </small>
                          </span>
                        }
                        autoComplete="on"
                        onChange={(text) => {
                          setFieldValue('trackingUrlDefault', text);
                        }}
                        value={values.trackingUrlDefault}
                        helpText={
                          <div>
                            {'<tracking_no>'} auto replaced with tracking
                            number. Click{' '}
                            <div style={{ display: 'inline-block' }}>
                              <Popover
                                active={popoverOpen}
                                onClose={() => setPopoverOpen(false)}
                                activator={
                                  <Button
                                    variant="plain"
                                    onClick={() => setPopoverOpen(true)}
                                  >
                                    here
                                  </Button>
                                }
                              >
                                <Popover.Pane>
                                  <Popover.Section>
                                    <p>
                                      {'<tracking_no>'} is the tracking number
                                      associated with the order. An example of
                                      default tracking URL is as follow:{' '}
                                    </p>
                                    <p style={{ marginTop: 5 }}>
                                      {
                                        'http://test.com/tracking?code=<tracking_no>'
                                      }
                                    </p>
                                  </Popover.Section>
                                </Popover.Pane>
                              </Popover>
                            </div>{' '}
                            for example
                          </div>
                        }
                      />
                    </FormLayout.Group>
                    <FormLayout.Group>
                      <MappingField
                        label={
                          <span>
                            Tracking Company{' '}
                            <small>
                              <i>optional</i>
                            </small>
                          </span>
                        }
                        field="trackingCompanyMapping"
                        mapping={values.trackingCompanyMapping}
                      />
                      <TextField
                        label={
                          <span>
                            Default Tracking Company{' '}
                            <small>
                              <i>optional</i>
                            </small>
                          </span>
                        }
                        onChange={(text) => {
                          setFieldValue('trackingCompanyDefault', text);
                        }}
                        value={values.trackingCompanyDefault}
                        autoComplete="on"
                      />
                    </FormLayout.Group>
                    <FormLayout.Group>
                      <MappingField
                        label={
                          <span>
                            Location Mapping{' '}
                            <small>
                              <i>optional</i>
                            </small>
                          </span>
                        }
                        field="locationMapping"
                        mapping={values.locationMapping}
                      />
                      <Select
                        label={
                          <span>
                            Default Location{' '}
                            <small>
                              <i>optional</i>
                            </small>
                          </span>
                        }
                        options={locationOptions}
                        value={values.locationId}
                        disabled={locationsQuery.loading}
                        error={errors.locationId}
                        onChange={(locationId) => {
                          const location = locationOptions.find(
                            (lo) => lo.value === locationId
                          );
                          if (location) {
                            setFieldValue('locationId', locationId);
                            setFieldValue('locationName', location.label);
                          }
                        }}
                      />
                    </FormLayout.Group>
                  </FormLayout>
                )}
              </FormLayout>
            </Card>
          </Layout.AnnotatedSection>
        </Layout>
      </FormLayout>

      <FormLayout>
        <Layout>
          <Layout.AnnotatedSection
            title="Partial Fulfillment"
            description="Item mapping enables Partial Fulfillments.
              You need to specify the Item SKU and Quantity to enable it."
          >
            <Card>
              <FormLayout>
                <FormLayout.Group>
                  <Select
                    label="Recognize line item by"
                    options={lineItemOptions}
                    onChange={(values) => {
                      setFieldValue('lineItemIdentifier', values);
                    }}
                    value={values.lineItemIdentifier}
                  />
                  <MappingField
                    label="and matched by (column number)"
                    field="skuMapping"
                    mapping={values.skuMapping}
                  />
                </FormLayout.Group>
                <FormLayout.Group>
                  <Button
                    variant="plain"
                    onClick={() => setSkuPrefixVisible((prev) => !prev)}
                  >
                    Add sku prefix{' '}
                  </Button>
                </FormLayout.Group>
              </FormLayout>
              {skuPrefixVisible && (
                <div style={{ marginTop: 10 }}>
                  <FormLayout>
                    <FormLayout.Group condensed>
                      <TextField
                        label={
                          <span>
                            SKU prefix{' '}
                            <small>
                              <i>optional</i>
                            </small>
                          </span>
                        }
                        onChange={(text) => {
                          setFieldValue('skuPrefix', text);
                        }}
                        value={values.skuPrefix}
                        autoComplete="on"
                      />
                      <div />
                    </FormLayout.Group>
                  </FormLayout>
                </div>
              )}
              <div style={{ marginTop: 20 }}>
                <FormLayout>
                  <FormLayout.Group condensed>
                    <MappingField
                      label={
                        <span>
                          Item Quantity{' '}
                          <small>
                            <i>optional</i>
                          </small>
                        </span>
                      }
                      field="quantityMapping"
                      mapping={values.quantityMapping}
                    />
                    <div />
                  </FormLayout.Group>
                </FormLayout>
              </div>
              <FormLayout>
                <Checkbox
                  label="Don't fulfill orders without SKU"
                  helpText={
                    "If it's left unchecked and SKU is empty or not mapped, all order items will be fulfilled."
                  }
                  checked={values.ignoreEmptySku}
                  onChange={(value) => setFieldValue('ignoreEmptySku', value)}
                />
              </FormLayout>
            </Card>
          </Layout.AnnotatedSection>
        </Layout>
      </FormLayout>
    </>
  );
}

import { But<PERSON>, Card, FormLayout, Layout, Text } from '@shopify/polaris';
import { useFormikContext } from 'formik';

import { Link } from '@/components/Link';

import type { FormValues } from '.';
import { MappingField } from './MappingField';

export function EcwidMappingsForm() {
  const { values } = useFormikContext<FormValues>();
  return (
    <>
      <>
        <Layout>
          <Layout.AnnotatedSection
            title="Order"
            description={
              <div>
                <p>
                  Map your order by recognizing it from the list provided and
                  your column number. NOTE: Column index mapping begins with 0.
                  The column sequence is 0,1,2, ...{' '}
                  <span style={{ display: 'block' }}>
                    <Link
                      target="_blank"
                      url="https://fulfilltracking.freshdesk.com/support/solutions/articles/44002602771-set-up-mapping-and-configuration"
                    >
                      Learn more
                    </Link>
                  </span>
                </p>
                <br />
                <Button variant="plain" url="/ecwid_sample.csv">
                  Download sample file
                </Button>
              </div>
            }
          >
            <Card>
              <div style={{ marginBottom: '8px', display: 'inline-block' }}>
                <Text variant="headingSm" as="h3">
                  Mapping by Column Index
                </Text>
              </div>
              <FormLayout>
                <MappingField
                  label="Order Number"
                  field="orderNoMapping"
                  mapping={values.orderNoMapping}
                />
              </FormLayout>
            </Card>
          </Layout.AnnotatedSection>
        </Layout>

        <FormLayout>
          <Layout>
            <Layout.AnnotatedSection
              title="Tracking"
              description={
                <div>
                  <p>
                    Map your tracking with any or all the fields with your
                    column number.
                  </p>
                  <br />
                  <p>
                    Configure your fulfillment by adding tracking information.
                  </p>
                </div>
              }
            >
              <Card>
                <div style={{ marginBottom: '8px', display: 'inline-block' }}>
                  <Text variant="headingSm" as="h3">
                    Mapping by Column Index
                  </Text>
                </div>
                <FormLayout>
                  <MappingField
                    label="Tracking Number(optional)"
                    field="trackingNoMapping"
                    mapping={values.trackingNoMapping}
                  />
                </FormLayout>
              </Card>
            </Layout.AnnotatedSection>
          </Layout>
        </FormLayout>
      </>
    </>
  );
}

import { useQuery } from '@apollo/client';
import { Autocomplete } from '@shopify/polaris';
import { useFormikContext } from 'formik';
import * as React from 'react';
import { useParams } from 'react-router-dom';

import { SourceGuidedMappingQuery } from '@/gql/queries';

import type { FormValues } from '.';

interface MappingFieldProps {
  field: string;
  label: React.ReactNode;
  mapping: any; // TODO:
}

// TODO: too magical with field and mapping props, TS can't help
export function MappingField(props: MappingFieldProps) {
  const { field, label, mapping } = props;
  const { errors, setFieldValue } = useFormikContext<FormValues>();
  const params = useParams();

  const guidedMappingQuery = useQuery(SourceGuidedMappingQuery, {
    variables: { id: params.id },
  });

  const options = React.useMemo(() => {
    const data = guidedMappingQuery.loading
      ? []
      : guidedMappingQuery.data?.sourceGuidedMapping?.data ?? [];

    return data.length > 0
      ? data.map((data, index) => ({
          value: index,
          label: index + '. ' + data,
        }))
      : [];
  }, [guidedMappingQuery]);

  return (
    <Autocomplete
      loading={guidedMappingQuery.loading}
      options={options}
      selected={[mapping]}
      onSelect={(value) => {
        setFieldValue(field, value[0].toString());
      }}
      textField={
        <Autocomplete.TextField
          error={errors[field]}
          label={label}
          type="number"
          onChange={(text) => {
            if (parseInt(text) < 0) {
              setFieldValue(field, '0');
            } else {
              setFieldValue(field, text);
            }
          }}
          value={mapping}
          autoComplete="off"
        />
      }
    />
  );
}

import {
  Card,
  ChoiceList,
  FormLayout,
  Layout,
  Text,
  TextField,
} from '@shopify/polaris';
import { useFormikContext } from 'formik';

import { Link } from '@/components/Link';

import type { FormValues } from '.';
import { FileReplacement } from './FileReplacement';

const orderStatusOptions = [
  {
    label: 'Incomplete',
    value: '0',
  },
  {
    label: 'Pending',
    value: '1',
  },
  {
    label: 'Shipped',
    value: '2',
  },
  {
    label: 'Partially Shipped',
    value: '3',
  },
  {
    label: 'Refunded',
    value: '4',
  },
  {
    label: 'Cancelled',
    value: '5',
  },
  {
    label: 'Declined',
    value: '6',
  },
  {
    label: 'Awaiting Payment',
    value: '7',
  },
  {
    label: 'Awaiting Pickup',
    value: '8',
  },
  {
    label: 'Awaiting Shipment',
    value: '9',
  },
  {
    label: 'Completed',
    value: '10',
  },
  {
    label: 'Awaiting Fulfillment',
    value: '11',
  },
  {
    label: 'Manual Verification Required',
    value: '12',
  },
  {
    label: 'Disputed',
    value: '13',
  },
  {
    label: 'Partially Refunded  ',
    value: '14',
  },
];

export function BigCommerceAdvancedSettingsForm() {
  const { values, setFieldValue, errors } = useFormikContext<FormValues>();

  return (
    <>
      <>
        <Layout>
          <Layout.AnnotatedSection
            title="Filters"
            description={
              <div>
                Filters provide better control and more effective sync.{' '}
                <Link
                  target="_blank"
                  url="https://fulfilltracking.freshdesk.com/support/solutions/articles/44002602870-set-up-advanced-settings"
                >
                  Learn more
                </Link>
              </div>
            }
          >
            <Card>
              <div style={{ marginBottom: '8px', display: 'inline-block' }}>
                <Text variant="headingSm" as="h3">
                  Order Filter
                </Text>
              </div>
              <FormLayout>
                <ChoiceList
                  allowMultiple
                  title="Order Status"
                  onChange={(value) => {
                    const str = value.filter((el) => el.length > 0);
                    setFieldValue('bigcommerceOrderStatus', str.join(','));
                  }}
                  choices={orderStatusOptions}
                  selected={values.bigcommerceOrderStatus.split(',')}
                />
                <FormLayout.Group>
                  <TextField
                    label="Filter by column index"
                    placeholder="6"
                    onChange={(text) => {
                      setFieldValue('ignoreKey', text);
                    }}
                    value={values.ignoreKey}
                    error={errors.ignoreKey}
                    autoComplete="off"
                  />
                  <TextField
                    label="when value is"
                    onChange={(text) => {
                      setFieldValue('ignoreValue', text);
                    }}
                    value={values.ignoreValue}
                    error={errors.ignoreValue}
                    placeholder="not_dispatched"
                    autoComplete="off"
                  />
                </FormLayout.Group>
                <p>
                  The filtered rows will be ignored from the source file while
                  updating fulfillment. Leave these two fields blank to sync all
                  rows in your file. Only one value can be filtered for now.
                </p>
              </FormLayout>
            </Card>
          </Layout.AnnotatedSection>
          <Layout.AnnotatedSection
            title="File format"
            description="if XML is use, Parent Node is mandatory. If Xlsx or Xls, please fill in the sheet name else default to first sheet."
          >
            <Card>
              <div style={{ marginBottom: '8px', display: 'inline-block' }}>
                <Text variant="headingSm" as="h3">
                  XML - Parent Node / Excel - Sheet Name
                </Text>
              </div>
              <FormLayout>
                <TextField
                  label="Provide the name that point to where tracking info is locating"
                  placeholder="Orders"
                  onChange={(text) => {
                    setFieldValue('parentNode', text);
                  }}
                  value={values.parentNode}
                  error={errors.parentNode}
                  autoComplete="off"
                />
              </FormLayout>
            </Card>
          </Layout.AnnotatedSection>
          <FileReplacement />
        </Layout>
      </>
    </>
  );
}

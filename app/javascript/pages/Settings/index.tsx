import { FormLayout, Text } from '@shopify/polaris';
import { Formik } from 'formik';
import omit from 'lodash/omit';
import * as React from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { match } from 'ts-pattern';

import { CurrentPage } from '@/components/CurrentPage';
import { TestColumnMappingModal } from '@/components/TestColumnMappingModal';
import type { SourceQueryResult } from '@/gql/queries';
import { useConfirmation } from '@/hooks/useConfirmation';
import { useCopySource } from '@/hooks/useCopySource';
import { useCurrentSource } from '@/hooks/useCurrentSource';
import { useDeleteSource } from '@/hooks/useDeleteSource';
import { useNotification } from '@/hooks/useNotification';
import { useUpdateSource } from '@/hooks/useUpdateSource';
import * as routes from '@/routes';
import * as schemas from '@/schemas';

import { FormSkeleton } from './FormSkeleton';
import { SourceTypeForm } from './SourceTypeForm';

const MappingsForm = React.lazy(() =>
  import('./MappingsForm').then((m) => ({ default: m.MappingsForm }))
);
const EcwidMappingsForm = React.lazy(() =>
  import('./EcwidMappingsForm').then((m) => ({ default: m.EcwidMappingsForm }))
);
const BigCommerceMappingsForm = React.lazy(() =>
  import('./BigCommerceMappingsForm').then((m) => ({
    default: m.BigCommerceMappingsForm,
  }))
);

const AdvancedSettingsForm = React.lazy(() =>
  import('./AdvancedSettingsForm').then((m) => ({
    default: m.AdvancedSettingsForm,
  }))
);
const EcwidAdvancedSettingsForm = React.lazy(() =>
  import('./EcwidAdvancedSettingsForm').then((m) => ({
    default: m.EcwidAdvancedSettingsForm,
  }))
);
const BigCommerceAdvancedSettingsForm = React.lazy(() =>
  import('./BigCommerceAdvancedSettingsForm').then((m) => ({
    default: m.BigCommerceAdvancedSettingsForm,
  }))
);

type ResultSource = SourceQueryResult['source'];
export interface FormValues
  extends Pick<
    ResultSource,
    | 'allowBlankTrackingNo'
    | 'autoDetectTracking'
    | 'bigcommerceOrderStatus'
    | 'columnSeparator'
    | 'ecwidPaymentStatus'
    | 'email'
    | 'fileReplacements'
    | 'financialStatus'
    | 'ftpMode'
    | 'googleSheetName'
    | 'ignoreEmptySku'
    | 'notifyCustomer'
    | 'orderDaysAgo'
    | 'orderKey'
    | 'orderNoMapping'
    | 'parentNode'
    | 'sourceHost'
    | 'sourceType'
    | 'tagEnabled'
    | 'tagValue'
  > {
  _buttonClicked: 'back' | 'submit' | '';
  // TODO: lib wrapper value={field.value ?? '') to remove exclude null below
  // use react-hook-form
  afterFulfilledOrderFinancialStatus: Exclude<
    ResultSource['afterFulfilledOrderFinancialStatus'],
    null
  >;
  excludeInventoryManagement: Exclude<
    ResultSource['excludeInventoryManagement'],
    null
  >;
  ignoreKey: Exclude<ResultSource['ignoreKey'], null>;
  ignoreValue: Exclude<ResultSource['ignoreValue'], null>;
  lineItemIdentifier: Exclude<ResultSource['lineItemIdentifier'], null>;
  locationId: Exclude<ResultSource['locationId'], null>;
  locationMapping: Exclude<ResultSource['locationMapping'], null>;
  locationName: Exclude<ResultSource['locationName'], null>;
  orderIdentifierConstants: Exclude<
    ResultSource['orderIdentifierConstants'],
    null
  >;
  pathToFile: Exclude<ResultSource['pathToFile'], null>;
  quantityMapping: Exclude<ResultSource['quantityMapping'], null>;
  shopifyOrderKeyConstants: Exclude<
    ResultSource['shopifyOrderKeyConstants'],
    null
  >;
  skuMapping: Exclude<ResultSource['skuMapping'], null>;
  skuPrefix: Exclude<ResultSource['skuPrefix'], null>;
  sourceLogin: Exclude<ResultSource['sourceLogin'], null>;
  sourcePassword: Exclude<ResultSource['sourcePassword'], null>;
  sourceProcess: Exclude<ResultSource['sourceProcess'], null>;
  sourceRename: Exclude<ResultSource['sourceRename'], null>;
  sourceUrl: Exclude<ResultSource['sourceUrl'], null>;
  sshKey: Exclude<ResultSource['sshKey'], null>;
  trackingCompanyDefault: Exclude<ResultSource['trackingCompanyDefault'], null>;
  trackingCompanyMapping: Exclude<ResultSource['trackingCompanyMapping'], null>;
  trackingNoMapping: Exclude<ResultSource['trackingNoMapping'], null>;
  trackingUrlDefault: Exclude<ResultSource['trackingUrlDefault'], null>;
  trackingUrlMapping: Exclude<ResultSource['trackingUrlMapping'], null>;
}

export function Settings() {
  const confirmation = useConfirmation();
  const notification = useNotification();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { source } = useCurrentSource();
  const { updateSource } = useUpdateSource();
  const { deleteSource } = useDeleteSource();
  const { copySource } = useCopySource();
  const [openTestColumnMappingModal, setOpenTestColumnMappingModal] =
    React.useState(false);

  const selectedTabIndex = +(searchParams.get('tab') ?? 0);

  const initialValues: FormValues = {
    _buttonClicked: '',
    afterFulfilledOrderFinancialStatus:
      source.afterFulfilledOrderFinancialStatus ?? '',
    allowBlankTrackingNo: source.allowBlankTrackingNo,
    autoDetectTracking: source.autoDetectTracking,
    bigcommerceOrderStatus: source.bigcommerceOrderStatus ?? '',
    columnSeparator: source.columnSeparator ?? '',
    ecwidPaymentStatus: source.ecwidPaymentStatus ?? '',
    email: source.email ?? '',
    excludeInventoryManagement: source.excludeInventoryManagement ?? '',
    fileReplacements: source.fileReplacements ?? [],
    financialStatus: source.financialStatus ?? '',
    ftpMode: source.ftpMode ?? '',
    googleSheetName: source.googleSheetName ?? '',
    ignoreEmptySku: source.ignoreEmptySku,
    ignoreKey: source.ignoreKey ?? '',
    ignoreValue: source.ignoreValue ?? '',
    lineItemIdentifier: source.lineItemIdentifier ?? '',
    locationId: source.locationId ?? '',
    locationMapping: source.locationMapping ?? '',
    locationName: source.locationName ?? '',
    notifyCustomer: source.notifyCustomer,
    orderDaysAgo: source.orderDaysAgo ?? '',
    orderIdentifierConstants: source.orderIdentifierConstants ?? '',
    orderKey: source.orderKey ?? '',
    orderNoMapping: source.orderNoMapping ?? '',
    parentNode: source.parentNode ?? '',
    pathToFile: source.pathToFile ?? '',
    quantityMapping: source.quantityMapping ?? '',
    shopifyOrderKeyConstants: source.shopifyOrderKeyConstants ?? '',
    skuMapping: source.skuMapping ?? '',
    skuPrefix: source.skuPrefix ?? '',
    sourceHost: source.sourceHost ?? '',
    sourceLogin: source.sourceLogin ?? '',
    sourcePassword: source.sourcePassword ?? '',
    sourceProcess: source.sourceProcess ?? 'none',
    sourceRename: source.sourceRename ?? '',
    sourceType: source.sourceType,
    sourceUrl: source.sourceUrl ?? '',
    sshKey: source.sshKey ?? '',
    tagEnabled: source.tagEnabled,
    tagValue: source.tagValue ?? '',
    trackingCompanyDefault: source.trackingCompanyDefault ?? '',
    trackingCompanyMapping: source.trackingCompanyMapping ?? '',
    trackingNoMapping: source.trackingNoMapping ?? '',
    trackingUrlDefault: source.trackingUrlDefault ?? '',
    trackingUrlMapping: source.trackingUrlMapping ?? '',
  };

  return (
    <Formik
      enableReinitialize
      initialValues={initialValues}
      validateOnChange={false}
      validationSchema={match(selectedTabIndex)
        .with(0, () => schemas.Source)
        .with(1, () => schemas.Mapping)
        .with(2, () => schemas.AdvanceSetting)
        .otherwise(() => undefined)}
      onSubmit={(values, { setSubmitting }) => {
        console.log(`onSubmit values:`, values);
        updateSource({
          variables: {
            source: {
              ...omit(values, ['_buttonClicked']),
              id: source.id,
            },
          },
        })
          .then(() => {
            setSubmitting(false);
            if (values._buttonClicked !== 'back') {
              const nextTab =
                selectedTabIndex < 2 ? selectedTabIndex + 1 : null;

              if (nextTab === null) {
                notification.open({
                  options: { variant: 'success' },
                  message:
                    'Source settings saved successfully. Redirecting to source detail page...',
                });

                navigate(`${routes.getSourceDetail(source.id)}`);
              } else {
                notification.open({
                  options: { variant: 'success' },
                  message: 'Source settings saved successfully.',
                });
                navigate(`?tab=${nextTab === null ? 2 : nextTab}`);
              }
            } else {
              notification.open({
                options: { variant: 'success' },
                message: 'Source settings saved successfully.',
              });
              let currentStep = selectedTabIndex;
              currentStep = currentStep <= 0 ? 0 : currentStep - 1;
              navigate(`?tab=${currentStep}`);
            }
          })
          .catch((error) => {
            if (error.graphQLErrors) {
              console.log('graphQL errors', error.graphQLErrors);
            } else {
              console.error('update error:', error);
            }

            notification.open({ options: { variant: 'error' } });
          });
      }}
    >
      {({ handleSubmit, setFieldValue, isSubmitting, errors }) => {
        console.log(`errors:`, errors);

        return (
          <CurrentPage
            titleMetadata={
              <Text variant="headingLg" as="p">
                <Text variant="bodyMd" as="span" tone="subdued">
                  Settings:
                </Text>
                <strong style={{ display: 'block' }}>{source.name}</strong>
              </Text>
            }
            backAction={{
              content: 'Back to source details',
              url: routes.getSourceDetail(source.id),
            }}
            actionGroups={[
              {
                title: 'More actions',
                actions: [
                  {
                    content: 'Duplicate',
                    onAction: () => copySource(source.id),
                  },
                  {
                    content: 'Delete',
                    onAction: () => {
                      confirmation.open({
                        message: 'Are you sure?',
                        onConfirm: () => {
                          deleteSource(source.id).then(() => {
                            confirmation.close();
                          });
                        },
                      });
                    },
                  },
                ],
              },
            ]}
            primaryAction={{
              type: 'submit',
              content: selectedTabIndex === 2 ? 'Update Source' : 'Next',
              onAction: () => {
                setFieldValue(
                  '_buttonClicked',
                  'submit' satisfies FormValues['_buttonClicked']
                ).then(() => handleSubmit());
              },
              loading: isSubmitting,
            }}
            secondaryActions={match(selectedTabIndex)
              .with(1, () => [
                {
                  content: 'Test column mapping',
                  onAction: () => {
                    setOpenTestColumnMappingModal(true);
                  },
                },
                {
                  content: 'Back',
                  onAction: () => {
                    setFieldValue(
                      '_buttonClicked',
                      'back' satisfies FormValues['_buttonClicked']
                    ).then(() => handleSubmit());
                  },
                },
              ])

              .with(2, () => [
                {
                  content: 'Back',
                  onAction: () => {
                    setFieldValue(
                      '_buttonClicked',
                      'back' satisfies FormValues['_buttonClicked']
                    ).then(() => handleSubmit());
                  },
                },
              ])
              .otherwise(() => [])}
          >
            <React.Suspense fallback={<FormSkeleton />}>
              <form onSubmit={handleSubmit}>
                <FormLayout>
                  {match(selectedTabIndex)
                    .with(0, () => <SourceTypeForm />)
                    .with(1, () =>
                      match(source.platform)
                        .with('big_commerce', () => <BigCommerceMappingsForm />)
                        .with('ecwid', () => <EcwidMappingsForm />)
                        .with('shopify', () => <MappingsForm />)
                        .exhaustive()
                    )
                    .with(2, () =>
                      match(source.platform)
                        .with('big_commerce', () => (
                          <BigCommerceAdvancedSettingsForm />
                        ))
                        .with('ecwid', () => <EcwidAdvancedSettingsForm />)
                        .with('shopify', () => <AdvancedSettingsForm />)
                        .exhaustive()
                    )
                    .otherwise(() => (
                      <></>
                    ))}
                </FormLayout>
              </form>
              {openTestColumnMappingModal && (
                <TestColumnMappingModal
                  source={source}
                  onClose={() => setOpenTestColumnMappingModal(false)}
                />
              )}
            </React.Suspense>
          </CurrentPage>
        );
      }}
    </Formik>
  );
}

import {
  Bad<PERSON>,
  Banner,
  Card,
  Checkbox,
  ChoiceList,
  Divider,
  FormLayout,
  Layout,
  Select,
  Text,
  TextField,
} from '@shopify/polaris';
import { useFormikContext } from 'formik';
import * as React from 'react';

import { Link } from '@/components/Link';
import { useCurrentShop } from '@/hooks/useCurrentShop';

import type { FormValues } from '.';
import { FileReplacement } from './FileReplacement';

const columnSeparatorOptions = [
  { value: '\t', label: '<tab>' },
  { value: ' ', label: '<space>' },
  { value: ',', label: ',' },
  { value: ';', label: ';' },
  { value: '|', label: '|' },
];

const financialStatusOptions = [
  {
    label: 'Paid',
    value: 'paid',
  },
  {
    label: 'Partially Paid',
    value: 'partially_paid',
  },
  {
    label: 'Partially Refunded',
    value: 'partially_refunded',
  },
  {
    label: 'Pending',
    value: 'pending',
  },
  {
    label: 'Authorized',
    value: 'authorized',
  },
  {
    label: 'Refunded',
    value: 'refunded',
  },
];

export function AdvancedSettingsForm() {
  const { values, setFieldValue, errors } = useFormikContext<FormValues>();
  const shop = useCurrentShop().currentShop;

  React.useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <>
      <>
        <Layout>
          <Layout.AnnotatedSection
            title="Filters"
            description={
              <div>
                Filters provide better control and more effective sync.{' '}
                <Link
                  target="_blank"
                  url="https://fulfilltracking.freshdesk.com/support/solutions/articles/44002602870-set-up-advanced-settings"
                >
                  Learn more
                </Link>
              </div>
            }
          >
            <Card>
              <div style={{ marginBottom: '8px', display: 'inline-block' }}>
                <Text variant="headingSm" as="h3">
                  Order Filter
                </Text>
              </div>
              <FormLayout>
                <ChoiceList
                  allowMultiple
                  title="Paid Status"
                  onChange={(value) => {
                    const str = value.filter((el) => el.length > 0);
                    setFieldValue('financialStatus', str.join(','));
                  }}
                  choices={financialStatusOptions}
                  selected={values.financialStatus.split(',')}
                />
                <FormLayout.Group>
                  <TextField
                    label="Filter by column index"
                    placeholder="6"
                    onChange={(text) => {
                      if (parseInt(text) < 0 || /[^\s\d]/.test(text)) {
                        setFieldValue('ignoreKey', '');
                      } else {
                        setFieldValue('ignoreKey', text);
                      }
                    }}
                    value={values.ignoreKey}
                    error={errors.ignoreKey}
                    autoComplete="off"
                  />
                  <TextField
                    label="when value is"
                    onChange={(text) => {
                      setFieldValue('ignoreValue', text);
                    }}
                    value={values.ignoreValue}
                    error={errors.ignoreValue}
                    placeholder="not_dispatched"
                    autoComplete="off"
                  />
                </FormLayout.Group>
                <p>
                  The filtered rows will be ignored from the source file while
                  updating fulfillment. Leave these two fields blank to sync all
                  rows in your file. Only one value can be filtered for now.
                </p>
                <TextField
                  label="Exclude Inventory Management"
                  onChange={(text) => {
                    setFieldValue('excludeInventoryManagement', text);
                  }}
                  value={values.excludeInventoryManagement}
                  error={errors.excludeInventoryManagement}
                  helpText="e.g. shopify, blank"
                  autoComplete="off"
                />
              </FormLayout>
              <div style={{ margin: '16px 0px' }}>
                <Divider />
              </div>
              <div style={{ marginBottom: '8px', display: 'inline-block' }}>
                <Text variant="headingSm" as="h3">
                  Notify Customer
                </Text>
              </div>
              <FormLayout>
                <Checkbox
                  label="Notify Customer"
                  helpText={
                    <div>
                      Customize the default shipping confirmation email template
                      to customers upon each fulfillment{' '}
                      <Link
                        url={`https://${shop.shopifyDomain}/admin/email_templates/shipping_confirmation/edit`}
                        external={true}
                      >
                        <strong>here</strong>
                      </Link>
                      .
                    </div>
                  }
                  checked={values.notifyCustomer}
                  onChange={(value) => setFieldValue('notifyCustomer', value)}
                />
              </FormLayout>
            </Card>
          </Layout.AnnotatedSection>

          <Layout.AnnotatedSection
            title="After fulfilled"
            description="Action to perform after fulfillment created. "
          >
            <Card>
              <div style={{ marginBottom: '8px', display: 'inline-block' }}>
                <Text variant="headingSm" as="h3">
                  Update Order Info
                </Text>
              </div>
              {values.afterFulfilledOrderFinancialStatus !== '' ? (
                <div style={{ marginBottom: '8px' }}>
                  <Banner>
                    This feature need extra credits per order updated
                  </Banner>
                </div>
              ) : (
                <></>
              )}
              <FormLayout>
                <Select
                  label="Change order financial status to?"
                  options={[
                    { value: '', label: 'No change' },
                    { value: 'paid', label: 'Paid' },
                    { value: 'close', label: 'Close / Archive' },
                  ]}
                  value={values.afterFulfilledOrderFinancialStatus}
                  error={errors.afterFulfilledOrderFinancialStatus}
                  onChange={(value) => {
                    setFieldValue('afterFulfilledOrderFinancialStatus', value);
                  }}
                />
                <div style={{ margin: '8px 0px' }}>
                  <Divider />
                </div>
                <Badge>Free Feature - No credits required</Badge>
                <Checkbox
                  label="Enable tagging"
                  helpText="Add shopify order tag after fulfill. Please note field below is required if this is enabled."
                  checked={values.tagEnabled}
                  onChange={(value) => setFieldValue('tagEnabled', value)}
                />
                <TextField
                  label="Tag to be added"
                  placeholder="Tag"
                  onChange={(text) => {
                    setFieldValue('tagValue', text);
                  }}
                  value={values.tagValue}
                  error={errors.tagValue}
                  autoComplete="off"
                />
                <small>
                  Requires <strong>NO EXTRA</strong> credit
                </small>
              </FormLayout>
            </Card>
          </Layout.AnnotatedSection>
          <Layout.AnnotatedSection
            title="File format"
            description="If Xlsx or Xls, please fill in the sheet name else default to first sheet. If XML is use, Parent Node is mandatory. "
          >
            <Card>
              <div style={{ marginBottom: '8px', display: 'inline-block' }}>
                <Text variant="headingSm" as="h3">
                  Excel - Sheet Name / XML - Parent Node
                </Text>
              </div>

              <FormLayout>
                <TextField
                  label="Provide the name that point to where tracking info is locating"
                  placeholder="Orders"
                  onChange={(text) => {
                    setFieldValue('parentNode', text);
                  }}
                  value={values.parentNode}
                  error={errors.parentNode}
                  autoComplete="off"
                />
              </FormLayout>

              <div style={{ margin: '16px 0px' }}>
                <Divider />
              </div>
              <div style={{ marginBottom: '8px', display: 'inline-block' }}>
                <Text variant="headingSm" as="h3">
                  CSV Configuration
                </Text>
              </div>
              <FormLayout>
                <Select
                  label="Column Separator"
                  options={columnSeparatorOptions}
                  value={values.columnSeparator}
                  error={errors.columnSeparator}
                  onChange={(value) => {
                    setFieldValue('columnSeparator', value);
                  }}
                />
              </FormLayout>
            </Card>
          </Layout.AnnotatedSection>
          <Layout.AnnotatedSection
            title="Order Duration"
            description={
              <>
                <p>Note:</p>
                <p>
                  Increasing the order duration will cause the source to be
                  slower than normal.
                </p>
              </>
            }
          >
            <Card>
              <div style={{ marginBottom: '8px', display: 'inline-block' }}>
                <Text variant="headingSm" as="h3">
                  Set Order Duration
                </Text>
              </div>
              <FormLayout>
                <Select
                  label="Fulfill/Update orders since last …"
                  options={[
                    { value: '14', label: '14 days' },
                    { value: '30', label: '30 days' },
                    { value: '90', label: '90 days' },
                    { value: '180', label: '180 days' },
                  ]}
                  value={values.orderDaysAgo}
                  error={errors.orderDaysAgo}
                  onChange={(value) => {
                    setFieldValue('orderDaysAgo', value);
                  }}
                />
              </FormLayout>
            </Card>
          </Layout.AnnotatedSection>
          <FileReplacement />
        </Layout>
      </>
    </>
  );
}

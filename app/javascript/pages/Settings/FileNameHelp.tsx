import { Button, Popover, Text } from '@shopify/polaris';
import * as React from 'react';

export function FileNameHelp() {
  const [popoverOpen, setPopoverOpen] = React.useState(false);

  return (
    <Popover
      active={popoverOpen}
      onClose={() => setPopoverOpen(false)}
      activatorWrapper="span"
      activator={
        <Button variant="plain" onClick={() => setPopoverOpen(true)}>
          here
        </Button>
      }
    >
      <Popover.Pane>
        <Popover.Section>
          <Text variant="bodyMd" as="span" fontWeight="semibold">
            Wildcard
          </Text>
          <Text variant="bodyMd" as="span" tone="subdued">
            {' '}
            - Find any matching file
          </Text>
          <div style={{ marginTop: '8px' }}>
            <Text variant="bodySm" as="p">
              e.g. shipping*.csv
            </Text>
          </div>
          <table className="table table-condensed">
            <tbody>
              <tr>
                <td align="left" width="100">
                  <Text variant="bodyMd" as="span" tone="subdued">
                    <small>Symbol</small>
                  </Text>
                </td>
                <td align="left">
                  <Text variant="bodyMd" as="span" tone="subdued">
                    <small>Example</small>
                  </Text>
                </td>
              </tr>
              <tr>
                <td valign="top">*</td>
                <td>
                  matching file name <small>(Latest file first)</small>
                </td>
              </tr>
            </tbody>
          </table>
        </Popover.Section>
        <Popover.Section>
          <Text variant="bodyMd" as="span" fontWeight="semibold">
            Replacing Date{' '}
          </Text>
          <Text variant="bodyMd" as="span" tone="subdued">
            - Daily generated file
          </Text>
          <div style={{ marginTop: '8px' }}>
            <Text variant="bodySm" as="p">
              e.g. /orders/fulfillment_%Y_%m_%d_*.csv<strong> to </strong>{' '}
              /orders/fulfillment_2017_05_09_14_30.csv
            </Text>
          </div>
          <table className="table table-condensed">
            <tbody>
              <tr>
                <td colSpan={3}>
                  Today Date
                  <hr />
                </td>
              </tr>
              <tr>
                <td align="left" width="100">
                  <Text variant="bodyMd" as="span" tone="subdued">
                    <small>Symbol</small>
                  </Text>
                </td>
                <td align="left">
                  <Text variant="bodyMd" as="span" tone="subdued">
                    <small>Example</small>
                  </Text>
                </td>
              </tr>
              <tr>
                <td>%d</td>
                <td>
                  01..31 <small>(Date zero-padded)</small>
                </td>
              </tr>
              <tr>
                <td>%m</td>
                <td>
                  01..12 <small>(Month zero-padded)</small>
                </td>
              </tr>
              <tr>
                <td>%b</td>
                <td>Jan</td>
              </tr>
              <tr>
                <td>%^b</td>
                <td>JAN</td>
              </tr>
              <tr>
                <td>%Y</td>
                <td>1995, 2009, 2017</td>
              </tr>
              <tr>
                <td>%y</td>
                <td>
                  00..99 <small>(Year last 2 digits)</small>
                </td>
              </tr>
            </tbody>
          </table>

          <table>
            <tbody>
              <tr>
                <td colSpan={3}>
                  Yesterday Date
                  <hr />
                </td>
              </tr>
              <tr>
                <td align="left" width="100">
                  <Text variant="bodyMd" as="span" tone="subdued">
                    <small>Symbol</small>
                  </Text>
                </td>
                <td align="left">
                  <Text variant="bodyMd" as="span" tone="subdued">
                    <small>Example</small>
                  </Text>
                </td>
              </tr>
              <tr>
                <td>{'%{d-}'}</td>
                <td>
                  01..31 <small>(Date zero-padded)</small>
                </td>
              </tr>
              <tr>
                <td>{'%{m-}'}</td>
                <td>
                  01..12 <small>(Month zero-padded)</small>
                </td>
              </tr>
              <tr>
                <td>{'%{b-}'}</td>
                <td>Jan</td>
              </tr>
              <tr>
                <td>{'%{^b-}'}</td>
                <td>JAN</td>
              </tr>
              <tr>
                <td>{'%{Y-}'}</td>
                <td>1995, 2009, 2017</td>
              </tr>
              <tr>
                <td>{'%{y-}'}</td>
                <td>
                  00..99 <small>(Year last 2 digits)</small>
                </td>
              </tr>
            </tbody>
          </table>
        </Popover.Section>
      </Popover.Pane>
    </Popover>
  );
}

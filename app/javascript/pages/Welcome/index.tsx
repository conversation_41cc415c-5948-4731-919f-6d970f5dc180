import { Button, Grid, Text } from '@shopify/polaris';
import * as React from 'react';

import { useCreateSource } from '@/hooks/useCreateSource';

export function Welcome() {
  const { createSource } = useCreateSource();
  const [loading, setLoading] = React.useState(false);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        width: '100%',
        maxWidth: 1000,
        padding: 34,
        margin: '0 auto',
      }}
    >
      <Grid
        gap={{
          xs: '50px',
          sm: '50px',
          md: '50px',
          lg: '50px',
        }}
      >
        <GridCell>
          <div>
            <img
              src="/FS_logo.png"
              width="350px"
              height="auto"
              style={{ verticalAlign: 'middle', maxWidth: '100%' }}
            />
            <div style={{ marginBottom: 30 }} />
            <Text variant="headingXl" as="p">
              Over 30 Million Orders Fulfilled
            </Text>

            <p
              style={{
                color: '#637381',
                fontSize: 20,
                margin: '16px 0px',
                lineHeight: '28px',
              }}
            >
              Automate tracking info update and set tracking numbers for
              multiple orders at once
            </p>
            <Button
              variant="primary"
              loading={loading}
              onClick={() => {
                createSource();
                setLoading(true);
              }}
            >
              Get Started
            </Button>
          </div>
        </GridCell>
        <GridCell>
          {/* https://www.howtocanvas.com/create-amazing-pages-in-canvas/responsive-youtube-iframes */}
          <div
            style={{
              position: 'relative',
              paddingTop: '56.25%', // ratio 16:9
            }}
          >
            <iframe
              style={{
                border: 0,
                position: 'absolute',
                top: 0,
                left: 0,
                maxWidth: 560,
                height: '100%',
                width: '100%',
              }}
              src="https://www.youtube-nocookie.com/embed/4LYz9wWxfT0"
              title="YouTube video player"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </div>
        </GridCell>
      </Grid>
    </div>
  );
}

interface GridCellProps {
  children: React.ReactNode;
}

function GridCell({ children }: GridCellProps) {
  return <Grid.Cell columnSpan={{ xs: 6 }}>{children}</Grid.Cell>;
}

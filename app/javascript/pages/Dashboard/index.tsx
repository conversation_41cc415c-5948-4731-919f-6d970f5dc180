import { useQuery } from '@apollo/client';
import {
  Badge,
  Card,
  EmptySearchResult,
  IndexTable,
  InlineStack,
  Link,
  Text,
} from '@shopify/polaris';
import startCase from 'lodash/startCase';
import * as React from 'react';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';

import { CurrentPage } from '@/components/CurrentPage';
import { formatNumber } from '@/components/util';
import { useWebSocketContext } from '@/components/WebSocketContext';
import { SourceSyncLogsQuery } from '@/gql/queries';
import { useCurrentShop } from '@/hooks/useCurrentShop';
import { useExtendedDayjs } from '@/hooks/useExtendedDayjs';
import { useSources } from '@/hooks/useSources';
import * as routes from '@/routes';

import { DashboardSkeleton } from './DashboardSkeleton';

export function Dashboard() {
  const sourcesQuery = useSources();
  const navigate = useNavigate();
  const { xdayjs } = useExtendedDayjs();
  const { currentShop } = useCurrentShop();
  const location = useLocation();

  const sources = React.useMemo(
    () => (sourcesQuery.data ? sourcesQuery.data.sources : []),
    [sourcesQuery]
  );
  const [searchParams] = useSearchParams();
  const sourceId = searchParams.get('id') ?? sources[0]?.id;
  const { latestMessage } = useWebSocketContext();

  const sourceSyncLogsQuery = useQuery(SourceSyncLogsQuery, {
    variables: { id: sourceId },
    skip: !sourceId,
  });

  React.useEffect(() => {
    if (latestMessage.payload?.sync_log && latestMessage.payload?.status) {
      sourceSyncLogsQuery.refetch();
    }
  }, [latestMessage, sourceSyncLogsQuery]);

  console.log('sources', sources);

  return (
    <CurrentPage
      titleMetadata="Dashboard"
      primaryAction={{
        content: 'Create new source',
        onAction: () => {
          navigate(routes.newSource);
        },
      }}
      backAction={
        {
          Shopify: {
            content: currentShop.storeDomain,
            url: `//${currentShop.storeDomain}/admin`,
          },
          Ecwid: {
            content: currentShop.storeDomain,
            url: `//my.ecwid.com/store/${currentShop.ecwidStoreId}`,
          },
          BigCommerce: {
            content: currentShop.storeDomain,
            url: `//${currentShop.storeDomain}/manage`,
          },
        }[currentShop.provider]
      }
      secondaryActions={
        !location.pathname.includes(routes.editIntegrations) &&
        currentShop.platform === 'big_commerce'
          ? [
              {
                content: 'Fulfillment log',
                url: routes.fulfillments,
              },
              {
                content: 'Preferences',
                url: routes.editPreferences,
              },
              {
                content: 'API',
                url: routes.editIntegrations,
              },
            ]
          : [
              {
                content: 'Fulfillment log',
                url: routes.fulfillments,
              },
              {
                content: 'Preferences',
                url: routes.editPreferences,
              },
            ]
      }
    >
      {sourcesQuery.loading ? (
        <DashboardSkeleton />
      ) : (
        <>
          <div style={{ marginBottom: '16px' }}>
            <Card>
              <InlineStack blockAlign="center">
                <div style={{ width: '200px' }}>
                  {currentShop.useCredit ? (
                    <>
                      <Text variant="bodyMd" as="p">
                        Credit
                      </Text>
                      <Text variant="headingMd" as="span">
                        {formatNumber(currentShop.credit)}
                      </Text>{' '}
                      <Link url={routes.billing} removeUnderline>
                        Manage Credit
                      </Link>
                    </>
                  ) : (
                    <>
                      <Text variant="bodyMd" as="p">
                        Plan
                      </Text>
                      <Text variant="headingMd" as="span">
                        {startCase(currentShop.package)}
                      </Text>{' '}
                      <Link url={routes.billing} removeUnderline>
                        Manage Plan
                      </Link>
                    </>
                  )}
                </div>

                <div style={{ display: 'flex' }}>
                  <Link url={routes.paypal} removeUnderline>
                    Sync tracking info
                  </Link>
                  <img
                    style={{
                      height: '18px',
                      verticalAlign: 'middle',
                      marginLeft: '8px',
                    }}
                    src="https://www.paypalobjects.com/webstatic/mktg/Logo/pp-logo-100px.png"
                    alt="PayPal Logo"
                  />
                </div>
              </InlineStack>
            </Card>
          </div>
          <Card padding="0">
            <IndexTable
              itemCount={sources.length}
              selectable={false}
              emptyState={
                <EmptySearchResult
                  title="No sources yet"
                  description="Click create new source to create your source"
                  withIllustration
                />
              }
              headings={[
                { title: 'Source name' },
                { title: 'Source type' },
                { title: 'Last run' },
                { title: 'Status' },
              ]}
            >
              {sources.map((source, index) => (
                <IndexTable.Row id={source.id} key={source.id} position={index}>
                  <IndexTable.Cell>
                    <Link
                      url={routes.getSourceDetail(source.id)}
                      removeUnderline
                    >
                      {source.name}
                    </Link>
                  </IndexTable.Cell>
                  <IndexTable.Cell>
                    {startCase(source.sourceType)}
                  </IndexTable.Cell>
                  <IndexTable.Cell>
                    {source.syncLog
                      ? xdayjs(source.syncLog?.processedAt).fromNow()
                      : '-'}
                  </IndexTable.Cell>
                  <IndexTable.Cell>
                    {source.syncLog &&
                    (source.syncLog?.numberFulfillmentUpdated ?? 0) > 0 ? (
                      <Badge tone="success">
                        {`${source.syncLog?.numberFulfillmentUpdated} Fulfilled`}
                      </Badge>
                    ) : (
                      '-'
                    )}
                  </IndexTable.Cell>
                </IndexTable.Row>
              ))}
            </IndexTable>
          </Card>
        </>
      )}
    </CurrentPage>
  );
}

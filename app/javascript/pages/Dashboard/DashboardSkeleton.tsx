import {
  Card,
  Layout,
  SkeletonBodyText,
  SkeletonDisplayText,
  Text,
} from '@shopify/polaris';
import * as React from 'react';

export function DashboardSkeleton() {
  const mockItems = Array.from({ length: 3 });
  return (
    <Layout>
      <Layout.Section variant="oneThird">
        <div
          style={{
            height: '100%',
            margin: '0 16px',
          }}
        >
          {mockItems.map((_, index) => (
            <React.Fragment key={index}>
              <div style={{ marginTop: 16 }} />
              <SkeletonDisplayText size="small" />
              <div style={{ marginBottom: 16 }} />
              <SkeletonBodyText lines={2} />
            </React.Fragment>
          ))}
        </div>
      </Layout.Section>
      <Layout.Section>
        <Layout>
          <Layout.Section>
            <Card>
              <SkeletonDisplayText size="small" />
              <div style={{ marginTop: 16 }} />
              <SkeletonBodyText />
            </Card>
          </Layout.Section>
          <Layout.Section>
            <Card>
              <Text variant="headingMd" as="h2">
                Recent Activities
              </Text>
              <small style={{ fontWeight: 200 }}>
                <Text variant="bodyMd" as="span" tone="subdued">
                  latest 10 only
                </Text>
              </small>
              {[{ id: '1' }, { id: '2' }].map((item) => (
                <div key={item.id} style={{ padding: '10px 0px' }}>
                  <SkeletonDisplayText size="small" />
                  <div style={{ marginTop: 16 }} />
                  <SkeletonBodyText lines={2} />
                </div>
              ))}
            </Card>
          </Layout.Section>
        </Layout>
      </Layout.Section>
    </Layout>
  );
}

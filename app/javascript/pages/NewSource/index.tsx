import { useMutation, useQuery } from '@apollo/client';
import {
  Card,
  Grid,
  RadioButton,
  Text,
  type CardProps,
} from '@shopify/polaris';

import { CurrentPage } from '@/components/CurrentPage';
import { CreateSourceMutation, MakeACopyMutation } from '@/gql/mutations';
import { TemplatesQuery } from '@/gql/queries';
import { useNotification } from '@/hooks/useNotification';
import * as routes from '@/routes';

export function NewSource() {
  const notification = useNotification();
  const templatesQuery = useQuery(TemplatesQuery);
  const templates = templatesQuery.data?.templates ?? [];
  const [createSource] = useMutation(CreateSourceMutation);
  const [copyMutation] = useMutation(MakeACopyMutation);

  const createNewSource = () => {
    createSource()
      .then((data) => {
        notification.open({
          options: { variant: 'success' },
          message: `${data.data.createSource.name} created. Redirecting to settings page...`,
        });
        setTimeout(
          () =>
            (window.location.href = routes.getSourceSettingsRoute(
              data.data.createSource.id
            )),
          500
        );
      })
      .catch((error) => {
        if (error.graphQLErrors?.length > 0) {
          notification.open({
            options: { variant: 'error' },
            message: error.graphQLErrors[0].message,
          });
        } else {
          console.error(error);
          notification.open({ options: { variant: 'error' } });
        }
      });
  };

  return (
    <CurrentPage
      titleMetadata="Create New Source"
      backAction={{
        content: 'Back to dashboard',
        url: routes.dashboard,
      }}
    >
      <Card>
        <div style={{ padding: '8px' }}>
          <Text variant="headingMd" as="p">
            Start from blank
          </Text>
          <div style={{ marginTop: '8px' }}>
            <Grid>
              <TemplateCard>
                <RadioButton
                  label="Start from scratch"
                  helpText="Create a custom configuration"
                  onChange={createNewSource}
                />
              </TemplateCard>
            </Grid>
          </div>
          <div style={{ marginTop: '16px' }}>
            <Text variant="headingMd" as="p">
              Pre-built template
            </Text>
            <div style={{ marginTop: '8px' }}>
              <Grid>
                {templates.map((template) => (
                  <TemplateCard key={template.id}>
                    <RadioButton
                      label={template.name}
                      onChange={() =>
                        copyMutation({
                          variables: { id: template.id },
                        })
                          .then((data) => {
                            notification.open({
                              options: { variant: 'success' },
                              message:
                                'Created source. Redirecting to settings page...',
                            });
                            setTimeout(
                              () =>
                                (window.location.href =
                                  routes.getSourceSettingsRoute(
                                    data.data.makeACopy.id
                                  )),
                              500
                            );
                          })
                          .catch((error) => {
                            console.error(error);
                            notification.open({
                              options: { variant: 'error' },
                            });
                          })
                      }
                    />
                  </TemplateCard>
                ))}
              </Grid>
            </div>
          </div>
        </div>
      </Card>
    </CurrentPage>
  );
}

interface TemplateCardProps extends Pick<CardProps, 'children'> {}

function TemplateCard({ children }: TemplateCardProps) {
  return (
    <Grid.Cell columnSpan={{ xs: 6, sm: 3, lg: 4 }}>
      <Card>{children}</Card>
    </Grid.Cell>
  );
}

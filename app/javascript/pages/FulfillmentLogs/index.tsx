import { useMutation, useQuery } from '@apollo/client';
import {
  Banner,
  BlockStack,
  Card,
  EmptySearchResult,
  IndexFilters,
  IndexFiltersMode,
  IndexTable,
  InlineStack,
  Modal,
  RadioButton,
  Text,
} from '@shopify/polaris';
import * as React from 'react';

import { CurrentPage } from '@/components/CurrentPage';
import { Link } from '@/components/Link';
import { CancelFulfillmentMutation } from '@/gql/mutations';
import { FulfillmentLogsQuery } from '@/gql/queries';
import { useCurrentShop } from '@/hooks/useCurrentShop';
import { useExtendedDayjs } from '@/hooks/useExtendedDayjs';
import { useNotification } from '@/hooks/useNotification';
import * as routes from '@/routes';
import type { FulfillmentLog } from '@/types';

// import { mockFulfillmentLogs } from './mock';

export function FulfillmentLogs() {
  const shop = useCurrentShop().currentShop;
  const { xdayjs } = useExtendedDayjs();
  const notification = useNotification();

  const [cancelFulfillment] = useMutation(CancelFulfillmentMutation);
  const [filters] = React.useState([]);
  const [fulfillmentLogs, setFulfillmentLogs] = React.useState<
    Array<FulfillmentLog>
  >([]);
  const [selected, setSelected] = React.useState(0);
  const [mode, setMode] = React.useState<IndexFiltersMode>(
    IndexFiltersMode.Default
  );
  const [queryValue, setQueryValue] = React.useState('');
  const [exportModalOpen, setExportModalOpen] = React.useState(false);
  const [exportMonth, setExportMonth] = React.useState('3');

  // Pagination state for IndexTable
  const [currentPage, setCurrentPage] = React.useState(1);
  const [itemsPerPage] = React.useState(25); // Items per page
  const [isRefetching, setIsRefetching] = React.useState(false);
  const fulfillmentLogsQuery = useQuery(FulfillmentLogsQuery, {
    variables: { filters, page: currentPage },
  });
  const itemStrings = ['Show all', 'Show error only'];

  const tabs = itemStrings.map((item, index) => ({
    content: item,
    index,
    onAction: () => setSelected(index),
    id: `${item}-${index}`,
    isLocked: index === 0,
  }));

  React.useEffect(() => {
    if (fulfillmentLogsQuery.loading) return;

    setFulfillmentLogs((prev) => {
      // If it's a refetch (currentPage is 1) or first load, replace the data
      // If it's pagination (currentPage > 1), concatenate the data
      if (isRefetching || currentPage === 1) {
        setIsRefetching(false); // Reset refetch flag
        return fulfillmentLogsQuery.data?.fulfillmentLogs ?? [];
      } else {
        // For pagination, concatenate new data
        return prev.concat(fulfillmentLogsQuery.data?.fulfillmentLogs ?? []);
      }
    });
  }, [
    fulfillmentLogsQuery.data,
    fulfillmentLogsQuery.loading,
    currentPage,
    isRefetching,
  ]);

  const filteredFulfillmentLogs = React.useMemo<Array<FulfillmentLog>>(() => {
    const errorFiltered =
      selected === 0
        ? fulfillmentLogs
        : fulfillmentLogs.filter(
            (fl) => fl.errorMessage && fl.errorMessage.length > 0
          );
    return errorFiltered.filter(
      (fl) =>
        !queryValue ||
        fl.orderNumber.toLowerCase().includes(queryValue.toLowerCase()) ||
        (fl.trackingNo || '')
          .toLowerCase()
          .includes(queryValue.toLowerCase()) ||
        (fl.trackingCompany || '')
          .toLowerCase()
          .includes(queryValue.toLowerCase())
    );
  }, [fulfillmentLogs, selected, queryValue]);

  // Paginated data for IndexTable
  const paginatedData = React.useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredFulfillmentLogs.slice(startIndex, endIndex);
  }, [filteredFulfillmentLogs, currentPage, itemsPerPage]);

  // Calculate total pages
  const totalPages = Math.ceil(filteredFulfillmentLogs.length / itemsPerPage);
  const hasNext = currentPage < totalPages;
  const hasPrevious = currentPage > 1;

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [selected, queryValue]);

  return (
    <CurrentPage
      titleMetadata="Fulfillment Log"
      backAction={{
        content: 'Back to dashboard',
        url: routes.dashboard,
      }}
      secondaryActions={[
        {
          content: 'Preferences',
          url: routes.editPreferences,
        },
        {
          content: 'Export',
          onAction: () => setExportModalOpen(true),
        },
      ]}
    >
      <Card padding="0">
        <IndexFilters
          mode={mode}
          setMode={setMode}
          filters={[]}
          appliedFilters={[]}
          queryPlaceholder="Search order id / tracking no"
          onQueryChange={setQueryValue}
          onQueryClear={() => {
            setQueryValue('');
            if (mode !== IndexFiltersMode.Default) {
              setMode(IndexFiltersMode.Default);
            }
          }}
          onClearAll={() => {
            setQueryValue('');
            setSelected(0);
            setMode(IndexFiltersMode.Default);
          }}
          tabs={tabs}
          selected={selected}
          onSelect={setSelected}
          cancelAction={{
            onAction: () => {
              setQueryValue('');
              setSelected(0);
              setMode(IndexFiltersMode.Default);
            },
            disabled: false,
            loading: false,
          }}
          canCreateNewView={false}
          queryValue={queryValue}
        />
        <IndexTable
          itemCount={paginatedData.length}
          selectable={false}
          loading={fulfillmentLogsQuery.loading}
          emptyState={
            <EmptySearchResult
              title="Your fulfillment log is empty"
              description="Fulfill some orders using our dashboard page."
              withIllustration
            />
          }
          pagination={{
            hasNext,
            onNext: () => setCurrentPage(currentPage + 1),
            hasPrevious,
            onPrevious: () => setCurrentPage(currentPage - 1),
            label: `${(currentPage - 1) * itemsPerPage + 1}–${Math.min(currentPage * itemsPerPage, filteredFulfillmentLogs.length)} of ${filteredFulfillmentLogs.length} items`,
          }}
          headings={[
            { title: 'Order ID' },
            { title: 'Item info (SKU/QTY)' },
            { title: 'Tracking Info (Tracking no, Company)' },
            { title: 'Fulfillment date' },
          ]}
        >
          {paginatedData.map((fl, index) => (
            <IndexTable.Row id={fl.id} key={fl.id} position={index}>
              <IndexTable.Cell>
                {fl.shopifyOrderId ? (
                  <Link
                    url={`//${shop.shopifyDomain}/admin/orders/${fl.shopifyOrderId}`}
                  >
                    {fl.orderNumber}
                  </Link>
                ) : (
                  <span>{fl.orderNumber}</span>
                )}
              </IndexTable.Cell>
              <IndexTable.Cell>
                <>
                  <div style={{ marginBottom: '4px' }}>
                    SKU: {fl.sku ?? '-'}
                  </div>
                  <div>QTY: {fl.quantity ?? '-'}</div>
                </>
              </IndexTable.Cell>
              <IndexTable.Cell>
                <>
                  <div style={{ marginBottom: '4px' }}>
                    Tracking no: {fl.trackingNo ?? '-'}
                  </div>
                  <div>Company: {fl.trackingCompany ?? '-'}</div>
                </>
              </IndexTable.Cell>
              <IndexTable.Cell>
                <InlineStack
                  wrap={false}
                  blockAlign="baseline"
                  align="space-between"
                >
                  <div>
                    <div style={{ marginBottom: '4px' }}>
                      {xdayjs(fl.createdAt).format('MMM D, YYYY h:mm A')}{' '}
                    </div>
                    {fl.errorMessage ? (
                      <Text variant="bodyMd" as="span" truncate>
                        Remark: {fl.errorMessage}
                      </Text>
                    ) : (
                      <></>
                    )}
                  </div>
                  <div
                    style={
                      fl.errorMessage && fl.errorMessage.length > 0
                        ? { marginTop: -10, float: 'right' }
                        : { float: 'right' }
                    }
                  >
                    {fl.shopifyOrderId &&
                      fl.fulfillmentStatus !== 'cancelled' && (
                        <span>
                          {fl.fulfillmentStatus === 'true' && (
                            <Link
                              onClick={() => {
                                cancelFulfillment({
                                  variables: { id: fl.id },
                                })
                                  .then((res) => {
                                    if (
                                      res.data.cancelFulfillment
                                        .fulfillmentStatus === 'cancelled'
                                    ) {
                                      notification.open({
                                        options: { variant: 'success' },
                                        message:
                                          'Fulfillment cancelled successfully.',
                                      });
                                      // Set refetch flag and refetch the fulfillment logs
                                      setIsRefetching(true);
                                      setCurrentPage(1); // Reset to first page
                                      fulfillmentLogsQuery.refetch();
                                    }
                                  })
                                  .catch((error) => console.error(error));
                              }}
                            >
                              <small>Remove</small>
                            </Link>
                          )}
                        </span>
                      )}
                  </div>
                </InlineStack>
              </IndexTable.Cell>
            </IndexTable.Row>
          ))}
        </IndexTable>
      </Card>
      <Modal
        open={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
        title="Export Product"
        primaryAction={{
          content: 'Export logs',
          onAction: () => {
            window.open(
              `/export_fulfillment_logs.csv?month=${exportMonth}`,
              '_blank'
            );
            setExportModalOpen(false);
          },
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            onAction: () => setExportModalOpen(false),
          },
        ]}
      >
        <Modal.Section>
          <Banner title="Note: Our fulfilment log csv only available max to 6 months" />
          <div style={{ marginTop: '16px' }}>
            <BlockStack>
              <RadioButton
                label="3 months"
                checked={exportMonth === '3'}
                id="3"
                onChange={(_, newValue) => setExportMonth(newValue)}
              />
              <RadioButton
                label="6 months"
                id="6"
                checked={exportMonth === '6'}
                onChange={(_, newValue) => setExportMonth(newValue)}
              />
            </BlockStack>
          </div>
        </Modal.Section>
      </Modal>
    </CurrentPage>
  );
}

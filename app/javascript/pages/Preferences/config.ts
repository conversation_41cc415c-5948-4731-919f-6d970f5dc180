export const TIME_ZONES: Array<{ value: string; label: string }> = [
  { value: 'Pacific/Midway', label: '(GMT-11:00) Midway Island' },
  { value: 'Pacific/Pago_Pago', label: '(GMT-11:00) American Samoa' },
  { value: 'Pacific/Honolulu', label: '(GMT-10:00) Hawaii' },
  { value: 'America/Juneau', label: '(GMT-09:00) Alaska' },
  {
    value: 'America/Los_Angeles',
    label: '(GMT-08:00) Pacific Time (US \u0026 Canada)',
  },
  { value: 'America/Tijuana', label: '(GMT-08:00) Tijuana' },
  {
    value: 'America/Denver',
    label: '(GMT-07:00) Mountain Time (US \u0026 Canada)',
  },
  { value: 'America/Phoenix', label: '(GMT-07:00) Arizona' },
  { value: 'America/Chihuahua', label: '(GMT-07:00) Chihuahua' },
  { value: 'America/Mazatlan', label: '(GMT-07:00) Mazatlan' },
  {
    value: 'America/Chicago',
    label: '(GMT-06:00) Central Time (US \u0026 Canada)',
  },
  { value: 'America/Regina', label: '(GMT-06:00) Saskatchewan' },
  { value: 'America/Mexico_City', label: '(GMT-06:00) Mexico City' },
  { value: 'America/Monterrey', label: '(GMT-06:00) Monterrey' },
  { value: 'America/Guatemala', label: '(GMT-06:00) Central America' },
  {
    value: 'America/New_York',
    label: '(GMT-05:00) Eastern Time (US \u0026 Canada)',
  },
  {
    value: 'America/Indiana/Indianapolis',
    label: '(GMT-05:00) Indiana (East)',
  },
  { value: 'America/Bogota', label: '(GMT-05:00) Bogota' },
  { value: 'America/Lima', label: '(GMT-05:00) Lima' },
  { value: 'America/Halifax', label: '(GMT-04:00) Atlantic Time (Canada)' },
  { value: 'America/Caracas', label: '(GMT-04:00) Caracas' },
  { value: 'America/La_Paz', label: '(GMT-04:00) La Paz' },
  { value: 'America/Santiago', label: '(GMT-04:00) Santiago' },
  { value: 'America/St_Johns', label: '(GMT-03:30) Newfoundland' },
  { value: 'America/Sao_Paulo', label: '(GMT-03:00) Brasilia' },
  {
    value: 'America/Argentina/Buenos_Aires',
    label: '(GMT-03:00) Buenos Aires',
  },
  { value: 'America/Montevideo', label: '(GMT-03:00) Montevideo' },
  { value: 'America/Guyana', label: '(GMT-04:00) Georgetown' },
  { value: 'America/Godthab', label: '(GMT-03:00) Greenland' },
  { value: 'Atlantic/South_Georgia', label: '(GMT-02:00) Mid-Atlantic' },
  { value: 'Atlantic/Azores', label: '(GMT-01:00) Azores' },
  { value: 'Atlantic/Cape_Verde', label: '(GMT-01:00) Cape Verde Is.' },
  { value: 'Europe/Dublin', label: '(GMT+00:00) Dublin' },
  { value: 'Europe/Lisbon', label: '(GMT+00:00) Lisbon' },
  { value: 'Europe/London', label: '(GMT+00:00) London' },
  { value: 'Africa/Casablanca', label: '(GMT+00:00) Casablanca' },
  { value: 'Africa/Monrovia', label: '(GMT+00:00) Monrovia' },
  { value: 'Etc/UTC', label: '(GMT+00:00) UTC' },
  { value: 'Europe/Belgrade', label: '(GMT+01:00) Belgrade' },
  { value: 'Europe/Bratislava', label: '(GMT+01:00) Bratislava' },
  { value: 'Europe/Budapest', label: '(GMT+01:00) Budapest' },
  { value: 'Europe/Ljubljana', label: '(GMT+01:00) Ljubljana' },
  { value: 'Europe/Prague', label: '(GMT+01:00) Prague' },
  { value: 'Europe/Sarajevo', label: '(GMT+01:00) Sarajevo' },
  { value: 'Europe/Skopje', label: '(GMT+01:00) Skopje' },
  { value: 'Europe/Warsaw', label: '(GMT+01:00) Warsaw' },
  { value: 'Europe/Zagreb', label: '(GMT+01:00) Zagreb' },
  { value: 'Europe/Brussels', label: '(GMT+01:00) Brussels' },
  { value: 'Europe/Copenhagen', label: '(GMT+01:00) Copenhagen' },
  { value: 'Europe/Madrid', label: '(GMT+01:00) Madrid' },
  { value: 'Europe/Paris', label: '(GMT+01:00) Paris' },
  { value: 'Europe/Amsterdam', label: '(GMT+01:00) Amsterdam' },
  { value: 'Europe/Berlin', label: '(GMT+01:00) Berlin' },
  { value: 'Europe/Zurich', label: '(GMT+01:00) Zurich' },
  { value: 'Europe/Rome', label: '(GMT+01:00) Rome' },
  { value: 'Europe/Stockholm', label: '(GMT+01:00) Stockholm' },
  { value: 'Europe/Vienna', label: '(GMT+01:00) Vienna' },
  { value: 'Africa/Algiers', label: '(GMT+01:00) West Central Africa' },
  { value: 'Europe/Bucharest', label: '(GMT+02:00) Bucharest' },
  { value: 'Africa/Cairo', label: '(GMT+02:00) Cairo' },
  { value: 'Europe/Helsinki', label: '(GMT+02:00) Helsinki' },
  { value: 'Europe/Kiev', label: '(GMT+02:00) Kyiv' },
  { value: 'Europe/Riga', label: '(GMT+02:00) Riga' },
  { value: 'Europe/Sofia', label: '(GMT+02:00) Sofia' },
  { value: 'Europe/Tallinn', label: '(GMT+02:00) Tallinn' },
  { value: 'Europe/Vilnius', label: '(GMT+02:00) Vilnius' },
  { value: 'Europe/Athens', label: '(GMT+02:00) Athens' },
  { value: 'Europe/Istanbul', label: '(GMT+03:00) Istanbul' },
  { value: 'Europe/Minsk', label: '(GMT+03:00) Minsk' },
  { value: 'Asia/Jerusalem', label: '(GMT+02:00) Jerusalem' },
  { value: 'Africa/Harare', label: '(GMT+02:00) Harare' },
  { value: 'Africa/Johannesburg', label: '(GMT+02:00) Pretoria' },
  { value: 'Europe/Kaliningrad', label: '(GMT+02:00) Kaliningrad' },
  { value: 'Europe/Moscow', label: '(GMT+03:00) Moscow' },
  { value: 'Europe/Volgograd', label: '(GMT+03:00) Volgograd' },
  { value: 'Europe/Samara', label: '(GMT+04:00) Samara' },
  { value: 'Asia/Kuwait', label: '(GMT+03:00) Kuwait' },
  { value: 'Asia/Riyadh', label: '(GMT+03:00) Riyadh' },
  { value: 'Africa/Nairobi', label: '(GMT+03:00) Nairobi' },
  { value: 'Asia/Baghdad', label: '(GMT+03:00) Baghdad' },
  { value: 'Asia/Tehran', label: '(GMT+03:30) Tehran' },
  { value: 'Asia/Muscat', label: '(GMT+04:00) Abu Dhabi' },
  { value: 'Asia/Baku', label: '(GMT+04:00) Baku' },
  { value: 'Asia/Tbilisi', label: '(GMT+04:00) Tbilisi' },
  { value: 'Asia/Yerevan', label: '(GMT+04:00) Yerevan' },
  { value: 'Asia/Kabul', label: '(GMT+04:30) Kabul' },
  { value: 'Asia/Yekaterinburg', label: '(GMT+05:00) Ekaterinburg' },
  { value: 'Asia/Karachi', label: '(GMT+05:00) Karachi' },
  { value: 'Asia/Tashkent', label: '(GMT+05:00) Tashkent' },
  { value: 'Asia/Kolkata', label: '(GMT+05:30) New Delhi' },
  { value: 'Asia/Kathmandu', label: '(GMT+05:45) Kathmandu' },
  { value: 'Asia/Dhaka', label: '(GMT+06:00) Dhaka' },
  { value: 'Asia/Colombo', label: '(GMT+05:30) Sri Jayawardenepura' },
  { value: 'Asia/Almaty', label: '(GMT+06:00) Almaty' },
  { value: 'Asia/Novosibirsk', label: '(GMT+07:00) Novosibirsk' },
  { value: 'Asia/Rangoon', label: '(GMT+06:30) Rangoon' },
  { value: 'Asia/Bangkok', label: '(GMT+07:00) Bangkok' },
  { value: 'Asia/Jakarta', label: '(GMT+07:00) Jakarta' },
  { value: 'Asia/Krasnoyarsk', label: '(GMT+07:00) Krasnoyarsk' },
  { value: 'Asia/Shanghai', label: '(GMT+08:00) Beijing' },
  { value: 'Asia/Chongqing', label: '(GMT+08:00) Chongqing' },
  { value: 'Asia/Hong_Kong', label: '(GMT+08:00) Hong Kong' },
  { value: 'Asia/Urumqi', label: '(GMT+06:00) Urumqi' },
  { value: 'Asia/Kuala_Lumpur', label: '(GMT+08:00) Kuala Lumpur' },
  { value: 'Asia/Singapore', label: '(GMT+08:00) Singapore' },
  { value: 'Asia/Taipei', label: '(GMT+08:00) Taipei' },
  { value: 'Australia/Perth', label: '(GMT+08:00) Perth' },
  { value: 'Asia/Irkutsk', label: '(GMT+08:00) Irkutsk' },
  { value: 'Asia/Ulaanbaatar', label: '(GMT+08:00) Ulaanbaatar' },
  { value: 'Asia/Seoul', label: '(GMT+09:00) Seoul' },
  { value: 'Asia/Tokyo', label: '(GMT+09:00) Tokyo' },
  { value: 'Asia/Yakutsk', label: '(GMT+09:00) Yakutsk' },
  { value: 'Australia/Darwin', label: '(GMT+09:30) Darwin' },
  { value: 'Australia/Adelaide', label: '(GMT+09:30) Adelaide' },
  { value: 'Australia/Melbourne', label: '(GMT+10:00) Melbourne' },
  { value: 'Australia/Sydney', label: '(GMT+10:00) Sydney' },
  { value: 'Australia/Brisbane', label: '(GMT+10:00) Brisbane' },
  { value: 'Australia/Hobart', label: '(GMT+10:00) Hobart' },
  { value: 'Asia/Vladivostok', label: '(GMT+10:00) Vladivostok' },
  { value: 'Pacific/Guam', label: '(GMT+10:00) Guam' },
  { value: 'Pacific/Port_Moresby', label: '(GMT+10:00) Port Moresby' },
  { value: 'Asia/Magadan', label: '(GMT+11:00) Magadan' },
  { value: 'Asia/Srednekolymsk', label: '(GMT+11:00) Srednekolymsk' },
  { value: 'Pacific/Guadalcanal', label: '(GMT+11:00) Solomon Is.' },
  { value: 'Pacific/Noumea', label: '(GMT+11:00) New Caledonia' },
  { value: 'Pacific/Fiji', label: '(GMT+12:00) Fiji' },
  { value: 'Asia/Kamchatka', label: '(GMT+12:00) Kamchatka' },
  { value: 'Pacific/Majuro', label: '(GMT+12:00) Marshall Is.' },
  { value: 'Pacific/Auckland', label: '(GMT+12:00) Auckland' },
  { value: 'Pacific/Tongatapu', label: "(GMT+13:00) Nuku'alofa" },
  { value: 'Pacific/Fakaofo', label: '(GMT+13:00) Tokelau Is.' },
  { value: 'Pacific/Chatham', label: '(GMT+12:45) Chatham Is.' },
  { value: 'Pacific/Apia', label: '(GMT+13:00) Samoa' },
];

import { useMutation } from '@apollo/client';
import {
  Card,
  Checkbox,
  ChoiceList,
  Divider,
  FormLayout,
  Select,
  Text,
  TextField,
} from '@shopify/polaris';
import { Formik, useFormikContext } from 'formik';

import { CurrentPage } from '@/components/CurrentPage';
import { EditShopMutation } from '@/gql/mutations';
import { useCurrentShop } from '@/hooks/useCurrentShop';
import { useNotification } from '@/hooks/useNotification';
import * as routes from '@/routes';
import * as schemas from '@/schemas';
import type { Shop } from '@/types';

import { TIME_ZONES } from './config';

type PreferencesFormValues = Pick<
  Shop,
  | 'notificationEmail'
  | 'lowCreditAlert'
  | 'emailSubscriptions'
  | 'timezone'
  | 'emailNotificationStartTime'
  | 'emailNotificationEndTime'
  | 'emailNotificationCustomTimeEnabled'
>;

// Content component with the form UI
function PreferencesContent() {
  const { values, errors, handleBlur, setFieldValue, handleSubmit } =
    useFormikContext<PreferencesFormValues>();

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <FormLayout>
          <TextField
            label="Notification email"
            helpText='Support multiple emails, separated by comma. eg "<EMAIL>, <EMAIL>"'
            onChange={(text) => {
              setFieldValue('notificationEmail', text);
            }}
            onBlur={handleBlur}
            value={values.notificationEmail}
            error={errors.notificationEmail}
            autoComplete="on"
          />
          <ChoiceList
            allowMultiple
            title="Email Subscriptions"
            onChange={(values) => setFieldValue('emailSubscriptions', values)}
            choices={[
              {
                label: 'New Features and Special Offers',
                value: 'newsletter',
              },
              {
                label: 'Sync Success',
                value: 'sync_success',
              },
              {
                label: 'Sync Failure',
                value: 'sync_failure',
              },
              {
                label: 'Sync File not found',
                value: 'sync_file_not_found',
              },
            ]}
            selected={values.emailSubscriptions}
          />
          <Select
            label="Timezone (apply to schedule and file name with timestamp)"
            options={TIME_ZONES}
            onChange={(values) => setFieldValue('timezone', values)}
            value={values.timezone}
          />
          <TextField
            type="number"
            label="Low credit alert"
            helpText="Send email notification when credit balance is less than the Low credit level"
            onChange={(text) => {
              setFieldValue('lowCreditAlert', text);
            }}
            onBlur={handleBlur}
            value={values.lowCreditAlert}
            error={errors.lowCreditAlert}
            autoComplete="off"
          />
        </FormLayout>
        <div style={{ margin: '20px 0px' }}>
          <Divider />
        </div>
        <div style={{ marginBottom: '20px', display: 'inline-block' }}>
          <Text variant="headingMd" as="h2">
            Do Not Disturb
          </Text>
        </div>
        <p style={{ paddingBottom: '10px' }}>
          When Do Not Disturb is turned on, syncX: Fulfill Tracking won't send
          you any notification in the period of time.
        </p>
        <FormLayout>
          <Checkbox
            checked={values.emailNotificationCustomTimeEnabled}
            label="Automatically disable notifications from:"
            onChange={(values) =>
              setFieldValue('emailNotificationCustomTimeEnabled', values)
            }
          />
        </FormLayout>
        <FormLayout>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '10px',
              paddingTop: '16px',
            }}
          >
            <TextField
              label=""
              type="time"
              onChange={(value) =>
                setFieldValue('emailNotificationStartTime', value)
              }
              value={values.emailNotificationStartTime}
              autoComplete="off"
            />
            <p>to</p>
            <TextField
              label=""
              type="time"
              onChange={(value) =>
                setFieldValue('emailNotificationEndTime', value)
              }
              value={values.emailNotificationEndTime}
              autoComplete="off"
            />
          </div>
        </FormLayout>
      </Card>
    </form>
  );
}

// Header component that uses the context
function PreferencesHeader() {
  const { handleSubmit } = useFormikContext<PreferencesFormValues>();

  return (
    <CurrentPage
      titleMetadata="Preferences"
      backAction={{
        content: 'Back to dashboard',
        url: routes.dashboard,
      }}
      primaryAction={{
        content: 'Save',
        onAction: handleSubmit,
      }}
      secondaryActions={[
        {
          content: 'Fulfillment log',
          url: routes.fulfillments,
        },
      ]}
    >
      <PreferencesContent />
    </CurrentPage>
  );
}

export function Preferences() {
  const notification = useNotification();
  const [editShop] = useMutation(EditShopMutation);
  const shop = useCurrentShop().currentShop;

  const handleSubmit = async (values: PreferencesFormValues) => {
    try {
      await editShop({
        variables: {
          shop: {
            id: shop.id,
            notificationEmail: values.notificationEmail,
            lowCreditAlert: values.lowCreditAlert,
            emailSubscriptions: values.emailSubscriptions,
            timezone: values.timezone,
            emailNotificationStartTime: values.emailNotificationStartTime,
            emailNotificationEndTime: values.emailNotificationEndTime,
            emailNotificationCustomTimeEnabled:
              values.emailNotificationCustomTimeEnabled,
          },
        },
      });

      notification.open({
        options: { variant: 'success' },
        message: 'Preferences saved successfully.',
      });
    } catch (error) {
      console.error(error);
      notification.open({ options: { variant: 'error' } });
    }
  };

  return (
    <Formik
      enableReinitialize
      initialValues={{
        notificationEmail: shop.notificationEmail || '',
        lowCreditAlert: shop.lowCreditAlert ?? '20',
        emailSubscriptions: shop.emailSubscriptions || [],
        timezone: shop.timezone || '',
        emailNotificationStartTime: shop.emailNotificationStartTime || '',
        emailNotificationEndTime: shop.emailNotificationEndTime || '',
        emailNotificationCustomTimeEnabled:
          shop.emailNotificationCustomTimeEnabled || false,
      }}
      onSubmit={handleSubmit}
      validationSchema={schemas.Shop}
    >
      <PreferencesHeader />
    </Formik>
  );
}

import { useQuery } from '@apollo/client';
import {
  <PERSON>ge,
  <PERSON>ton,
  Card,
  Checkbox,
  ChoiceList,
  Collapsible,
  Divider,
  FormLayout,
  Grid,
  InlineGrid,
  InlineStack,
  Modal,
  Popover,
  ResourceItem,
  ResourceList,
  Select,
  Text,
  TextField,
  Tooltip,
  type ButtonProps,
} from '@shopify/polaris';
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from '@shopify/polaris-icons';
import { Formik } from 'formik';
import startCase from 'lodash/startCase';
import * as React from 'react';
import Dropzone from 'react-dropzone';
import { match, P } from 'ts-pattern';
import * as Yup from 'yup';

import { CurrentPage } from '@/components/CurrentPage';
import { Link } from '@/components/Link';
import { TestColumnMappingModal } from '@/components/TestColumnMappingModal';
import { useWebSocketContext } from '@/components/WebSocketContext';
import { apolloClient } from '@/gql/apolloClient';
import { UploadFileMutation } from '@/gql/mutations';
import { SourceSyncLogsQuery } from '@/gql/queries';
import { useConfirmation } from '@/hooks/useConfirmation';
import { useCurrentShop } from '@/hooks/useCurrentShop';
import { useDetailSource } from '@/hooks/useDetailSource';
import { useExtendedDayjs } from '@/hooks/useExtendedDayjs';
import { useNotification } from '@/hooks/useNotification';
import { useUpdateSource } from '@/hooks/useUpdateSource';
import * as routes from '@/routes';
import { getScheduleSchema } from '@/schemas';

interface FormValues {
  id: string;
  updateMode: boolean;
  sourceFile: File | undefined;
}

export function SourceDetail() {
  const confirmation = useConfirmation();
  const notification = useNotification();
  const shop = useCurrentShop().currentShop;
  const { xdayjs } = useExtendedDayjs();
  const { source } = useDetailSource();

  const { latestMessage } = useWebSocketContext();
  const { updateSource, updateSourceResult } = useUpdateSource();
  const [schedulePopoverVisible, setSchedulePopoverVisible] =
    React.useState(false);
  const [renameModalVisible, setRenameModalVisible] = React.useState(false);
  const syncStatus = source.syncStatus === 'started' ? 'Disable' : 'Enable';
  const scheduleStatus =
    source.syncStatus === 'started' ? 'enabled' : 'disabled';
  const scheduledDaily = source.scheduleType === 'daily' ? true : false;
  const scheduleEnabled = source.syncStatus === 'started' ? true : false;
  const [openTestColumnMappingModal, setOpenTestColumnMappingModal] =
    React.useState(false);
  const [open, setOpen] = React.useState(true);

  const handleToggle = React.useCallback(() => setOpen((open) => !open), []);

  const sourceSyncLogsQuery = useQuery(SourceSyncLogsQuery, {
    variables: { id: source.id ?? '' },
  });

  console.log('source', source);

  React.useEffect(() => {
    if (latestMessage.payload?.sync_log && latestMessage.payload?.status) {
      sourceSyncLogsQuery.refetch();
    }
  }, [latestMessage, sourceSyncLogsQuery]);

  const syncLogs = sourceSyncLogsQuery.data?.sourceSyncLogs ?? [];

  console.log('syncLogs', syncLogs);

  const syncNow = React.useCallback(
    async ({ values }) => {
      return apolloClient
        .mutate({
          mutation: UploadFileMutation,
          variables: {
            id: values.id,
            sourceFile: values.sourceFile,
            updateMode: values.updateMode,
            cancelSyncNow: false,
          },
        })
        .then(() => {
          notification.open({
            options: { variant: 'success' },
            message: `Sync started.`,
          });
        })
        .catch((error) => {
          if (error.graphQLErrors) {
            notification.open({
              options: { variant: 'error' },
              message: error.graphQLErrors[0].message,
            });
          } else {
            console.error(error);
            notification.open({ options: { variant: 'error' } });
          }
        });
    },
    [notification]
  );

  const cancelSyncNow = React.useCallback(() => {
    confirmation.open({
      options: { variant: 'warning' },
      message: (
        <div>
          <Text variant="headingMd" as="h2">
            Cancelling process
          </Text>
          <p>
            Source still processing or queuing, please wait a while to complete.
            Are you sure to cancel this process now?
          </p>
        </div>
      ),
      onConfirm: () => {
        confirmation.close();
        apolloClient
          .mutate({
            // TODO: strong type  UploadFileMutation
            mutation: UploadFileMutation,
            variables: { id: source.id, cancelSyncNow: true },
          })
          .catch((error) => {
            if (error.graphQLErrors) {
              notification.open({
                options: { variant: 'error' },
                message: error.graphQLErrors[0]?.message ?? error?.message,
              });
            } else {
              console.error(error);
              notification.open({
                options: { variant: 'error' },
              });
            }
          });
      },
    });
  }, [confirmation, notification, source]);

  const orderKeyLabel = [
    {
      label: 'Order Number',
      value: 'order_number',
      example: '1108',
    },
    {
      label: 'Order Name = Prefix + Order Number',
      value: 'name',
      example: '#1108',
    },
    {
      label: 'Shopify Order ID',
      value: 'id',
      example: '406492184611',
    },
    {
      label: 'Order Sequence Number',
      value: 'number',
      example: '108',
    },
  ].find(({ value }) => value === source.orderKey)?.label;

  const bigcommerceOrderStatusLabel = [
    {
      label: 'Incomplete',
      value: '0',
    },
    {
      label: 'Pending',
      value: '1',
    },
    {
      label: 'Shipped',
      value: '2',
    },
    {
      label: 'Partially Shipped',
      value: '3',
    },
    {
      label: 'Refunded',
      value: '4',
    },
    {
      label: 'Cancelled',
      value: '5',
    },
    {
      label: 'Declined',
      value: '6',
    },
    {
      label: 'Awaiting Payment',
      value: '7',
    },
    {
      label: 'Awaiting Pickup',
      value: '8',
    },
    {
      label: 'Awaiting Shipment',
      value: '9',
    },
    {
      label: 'Completed',
      value: '10',
    },
    {
      label: 'Awaiting Fulfillment',
      value: '11',
    },
    {
      label: 'Manual Verification Required',
      value: '12',
    },
    {
      label: 'Disputed',
      value: '13',
    },
    {
      label: 'Partially Refunded  ',
      value: '14',
    },
  ].find(({ value }) => value === source.bigcommerceOrderStatus)?.label;

  console.log('bigcommerceOrderStatusOptions', bigcommerceOrderStatusOptions);

  return (
    <CurrentPage
      titleMetadata="Source Detail"
      backAction={{
        content: 'Back to dashboard',
        url: routes.dashboard,
      }}
    >
      <div style={{ marginBottom: '16px' }}>
        <Card>
          <InlineGrid columns={3} gap="200">
            <div style={{ display: 'flex' }}>
              <Button
                onClick={handleToggle}
                ariaExpanded={open}
                variant="plain"
                icon={open ? ChevronUpIcon : ChevronDownIcon}
              />
              <Link url={routes.getSourceSettingsRoute(source.id)}>
                {' '}
                Source type
              </Link>{' '}
            </div>
            <Link url={`${routes.getSourceSettingsRoute(source.id)}?tab=1`}>
              {' '}
              Mapping and configuration
            </Link>{' '}
            <Link url={`${routes.getSourceSettingsRoute(source.id)}?tab=2`}>
              {' '}
              Advanced settings
            </Link>{' '}
          </InlineGrid>

          <Collapsible
            open={open}
            id="basic-collapsible"
            transition={{ duration: '500ms', timingFunction: 'ease-in-out' }}
            expandOnPrint
          >
            <div style={{ marginTop: '16px' }}>
              <InlineGrid columns={3} gap="200">
                <Card>
                  <Text variant="headingSm" as="h6">
                    Source type
                  </Text>
                  <p>{startCase(source.sourceType)}</p>
                  <p>
                    {' '}
                    {source.sourceFile && source.sourceFile.name
                      ? source.sourceFile.name
                      : ''}
                  </p>
                </Card>
                <Card>
                  <Text variant="headingSm" as="h6">
                    Recognize my order by
                  </Text>
                  <p>
                    {source.platform === 'shopify' ? orderKeyLabel : 'Order ID'}
                  </p>
                  <InlineStack blockAlign="baseline">
                    <i className="fa fa-arrow-right" /> &nbsp;
                    <p>{source.orderNoMapping}</p>
                  </InlineStack>

                  {source.platform === 'shopify' && (
                    <>
                      <div style={{ marginTop: '16px' }}>
                        <Text variant="bodySm" as="p">
                          Shopify prefix/postfix
                        </Text>
                        <p>
                          {source.shopifyOrderKeyConstants
                            ? source.shopifyOrderKeyConstants
                            : '-'}
                        </p>
                        <div style={{ marginTop: '8px' }}>
                          <Text variant="bodySm" as="p">
                            Feed prefix/postfix
                          </Text>
                          <p>
                            {source.orderIdentifierConstants
                              ? source.orderIdentifierConstants
                              : '-'}
                          </p>
                        </div>
                      </div>
                      <div style={{ margin: '8px 0px' }}>
                        <Divider />
                      </div>
                      <Text variant="bodySm" as="p">
                        Location
                      </Text>
                      <p>{source.locationName ? source.locationName : '-'}</p>
                    </>
                  )}
                </Card>
                <Card>
                  <Text variant="headingSm" as="h6">
                    Order filter
                  </Text>
                  <div style={{ margin: '8px 0px' }}>
                    {source.financialStatus
                      .split(',')
                      .map((status) => startCase(status))
                      .join(' . ')}
                  </div>
                  {source.platform === 'shopify' && (
                    <>
                      {source.notifyCustomer && (
                        <div style={{ margin: '8px 0px' }}>
                          <InlineStack blockAlign="baseline">
                            <i className="fa fa-check" /> &nbsp; Notify customer
                          </InlineStack>
                        </div>
                      )}
                      {source.orderDaysAgo && (
                        <div style={{ margin: '8px 0px' }}>
                          <InlineStack blockAlign="baseline">
                            <i className="fa fa-check" /> &nbsp; Order
                            duration&nbsp;
                            <strong>{source.orderDaysAgo} days</strong>
                          </InlineStack>
                        </div>
                      )}
                      {source.tagValue && (
                        <div style={{ margin: '8px 0px' }}>
                          <InlineStack blockAlign="baseline">
                            <i className="fa fa-check" /> &nbsp; Tags to be
                            added&nbsp;
                            <strong>{source.tagValue}</strong>
                          </InlineStack>
                        </div>
                      )}
                    </>
                  )}

                  <div style={{ margin: '8px 0px' }}>
                    <Divider />
                  </div>

                  <Text variant="headingSm" as="h6">
                    Find and replace
                  </Text>
                  {source.fileReplacements.map((fr, i) => (
                    <div key={i} style={{ margin: '8px 0px 16px' }}>
                      <Text variant="bodySm" as="p">
                        {startCase(fr.column)}
                      </Text>
                      <div style={{ marginTop: '8px' }}>
                        <Text variant="bodySm" as="p">
                          {fr.replace_from}
                        </Text>
                        <InlineStack blockAlign="baseline">
                          <i className="fa fa-arrow-right" /> &nbsp;
                          <Text variant="bodySm" as="p">
                            {fr.replace_to}
                          </Text>
                        </InlineStack>
                      </div>
                    </div>
                  ))}
                </Card>
              </InlineGrid>
            </div>
          </Collapsible>
        </Card>
      </div>
      <Grid>
        <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
          <Card>
            <InlineStack blockAlign="center" align="space-between">
              <div style={{ display: 'flex' }}>
                <Text as="h2" variant="headingMd">
                  {source.name}
                </Text>{' '}
                <Button
                  variant="plain"
                  icon={<i className="fa fa-pen" />}
                  onClick={() => setRenameModalVisible(true)}
                />
                {renameModalVisible ? (
                  <Formik
                    enableReinitialize
                    initialValues={{
                      id: source.id || '',
                      name: source.name || '',
                    }}
                    validationSchema={Yup.object({
                      name: Yup.string().required('Field is required'),
                    })}
                    onSubmit={async (values) => {
                      updateSource({ variables: { source: values } })
                        .then((res) => {
                          if (!res.data) return;
                          notification.open({
                            options: { variant: 'success' },
                            message: `${res.data.source.name} successfully updated`,
                          });
                          setRenameModalVisible(false);
                        })
                        .catch(() => {
                          notification.open({ options: { variant: 'error' } });
                        });
                    }}
                  >
                    {({
                      values,
                      handleSubmit,
                      setFieldValue,
                      errors,
                      isSubmitting,
                    }) => (
                      <form onSubmit={handleSubmit}>
                        <Modal
                          open={renameModalVisible}
                          onClose={() => setRenameModalVisible(false)}
                          title="Rename source"
                          primaryAction={{
                            content: 'Submit',
                            loading: isSubmitting,
                            onAction: () => handleSubmit(),
                          }}
                          secondaryActions={[
                            {
                              content: 'Close',
                              onAction: () => setRenameModalVisible(false),
                            },
                          ]}
                        >
                          <div style={{ margin: '16px' }}>
                            <TextField
                              label=""
                              name="name"
                              autoComplete="off"
                              value={values.name}
                              error={errors.name}
                              onChange={(value) => {
                                setFieldValue('name', value);
                              }}
                            />
                          </div>
                        </Modal>
                      </form>
                    )}
                  </Formik>
                ) : (
                  <></>
                )}
              </div>
              <Link
                removeUnderline
                url={routes.getSourceSettingsRoute(source.id)}
              >
                <Button variant="plain" icon={<i className="fa fa-cog" />}>
                  Settings
                </Button>
              </Link>
            </InlineStack>
            <div style={{ marginTop: 16 }} />
            <InlineStack blockAlign="center" gap="200">
              <Text variant="bodyMd" as="p" tone="subdued">
                {startCase(source.sourceType)} . Order Duration
              </Text>
              {shop.provider === 'Shopify' && (
                <Link
                  removeUnderline
                  url={`${routes.getSourceSettingsRoute(source.id)}?tab=2`}
                >
                  {/* TODO: unify router + polaris Link */}
                  {source.orderDaysAgo} days
                </Link>
              )}
            </InlineStack>

            <div style={{ marginTop: 16 }} />
            <div>
              {match({ latestMessage, source })
                .with(
                  {
                    latestMessage: {
                      payload: { id: source.id, status: 'queuing' },
                    },
                  },
                  {
                    latestMessage: P.when((lm) => !lm.payload),
                    source: { status: 'queuing' },
                  },
                  () => (
                    <InlineStack blockAlign="center" align="space-between">
                      <Button
                        variant="plain"
                        onClick={() => setOpenTestColumnMappingModal(true)}
                      >
                        Test column mapping
                      </Button>
                      <PrimaryButton
                        iconType="spin"
                        onClick={() => cancelSyncNow()}
                      >
                        Queuing
                      </PrimaryButton>
                    </InlineStack>
                  )
                )
                .with(
                  {
                    latestMessage: {
                      payload: { id: source.id, status: 'running' },
                    },
                  },
                  ({ latestMessage }) => (
                    <InlineStack blockAlign="center" align="space-between">
                      <Button
                        variant="plain"
                        onClick={() => setOpenTestColumnMappingModal(true)}
                      >
                        Test column mapping
                      </Button>

                      <PrimaryButton
                        iconType="spin"
                        onClick={() => cancelSyncNow()}
                      >
                        Running ({`${latestMessage.payload.progress}`}%)
                      </PrimaryButton>
                    </InlineStack>
                  )
                )
                .with(
                  {
                    latestMessage: P.when((lm) => !lm.payload),
                    source: { status: 'running' },
                  },
                  ({ source }) => (
                    <InlineStack blockAlign="center" align="space-between">
                      <Button
                        variant="plain"
                        onClick={() => setOpenTestColumnMappingModal(true)}
                      >
                        Test column mapping
                      </Button>

                      <PrimaryButton
                        iconType="spin"
                        onClick={() => cancelSyncNow()}
                      >
                        Running ({`${source.progress}`}%)
                      </PrimaryButton>
                    </InlineStack>
                  )
                )
                .with({ source: { sourceType: 'email' } }, () => (
                  <InlineStack blockAlign="center" align="space-between">
                    <Button
                      variant="plain"
                      onClick={() => setOpenTestColumnMappingModal(true)}
                    >
                      Test column mapping
                    </Button>

                    <PrimaryButton iconType="tick" disabled>
                      Will auto sync when email received
                    </PrimaryButton>
                  </InlineStack>
                ))
                .with({ source: { sourceType: 'file_upload' } }, () => (
                  <Formik
                    enableReinitialize
                    initialValues={
                      {
                        id: source.id,
                        updateMode: false,
                        sourceFile: undefined,
                      } satisfies FormValues
                    }
                    onSubmit={async (values: FormValues) => {
                      if (values.sourceFile) {
                        await syncNow({ values });
                      } else {
                        notification.open({
                          options: { variant: 'error' },
                          message: 'No file uploaded',
                        });
                      }
                    }}
                  >
                    {({
                      values,
                      handleSubmit,
                      setFieldValue,
                      isSubmitting,
                    }) => (
                      <form onSubmit={handleSubmit} style={{ marginBottom: 5 }}>
                        <>
                          <Dropzone
                            accept={{
                              'text/csv': ['.csv'],
                              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                                ['.xlsx'],
                              'application/vnd.ms-excel': ['.xls'],
                              'application/xml': ['.xml'],
                              'text/xml': ['.xml'],
                            }}
                            validator={(file) => {
                              const regexMultiExtensions =
                                /(\.[a-z]+)\.[a-z]+/i; // catches multiple exts e.g. dump.php.csv
                              if (regexMultiExtensions.test(file.name)) {
                                return {
                                  code: 'file-multiple-ext',
                                  message: 'File has multiple extensions',
                                };
                              }

                              return null;
                            }}
                            onDropRejected={(files) => {
                              notification.open({
                                options: { variant: 'error' },
                                message: files[0].errors[0].message,
                              });
                            }}
                            onDrop={(files) => {
                              setFieldValue(
                                'sourceFile' satisfies keyof FormValues,
                                files[0]
                              );
                            }}
                          >
                            {({ getRootProps, getInputProps }) => (
                              <div
                                {...getRootProps()}
                                style={{
                                  border: '1px dashed',
                                  borderWidth: '1px',
                                  borderColor: values.sourceFile
                                    ? '#108043'
                                    : '#d9d9d9',
                                  borderRadius: '4px',
                                  cursor: 'pointer',
                                  textAlign: 'center',
                                  padding: '10px 12px',
                                  width: '100%',
                                  position: 'relative',
                                }}
                              >
                                <input {...getInputProps()} />
                                <i
                                  className="fa fa-upload"
                                  style={{ fontSize: '32px' }}
                                />
                                <h3
                                  style={{
                                    color: '#637381',
                                    fontSize: '15px',
                                    marginTop: '8px',
                                  }}
                                >
                                  Drop or click here to upload.
                                </h3>
                              </div>
                            )}
                          </Dropzone>
                          {values.sourceFile && (
                            <div
                              style={{ textAlign: 'center', marginTop: '4px' }}
                            >
                              <span>Current file:</span>
                              <span style={{ color: '#108043' }}>
                                {values.sourceFile.name}
                              </span>
                            </div>
                          )}
                          <div style={{ margin: '24px 0px' }}>
                            <Checkbox
                              label="Update fulfilled orders (Optional)"
                              checked={values.updateMode}
                              onChange={(value) =>
                                setFieldValue('updateMode', value)
                              }
                              helpText={
                                <>
                                  Note: Used for{' '}
                                  <strong>correcting tracking info</strong> for
                                  fulfilled orders only. Doesn’t handle partial
                                  fulfillment. Enabling this option will make
                                  the process slower.
                                </>
                              }
                            />
                          </div>
                          <InlineStack
                            blockAlign="center"
                            align="space-between"
                          >
                            <Button
                              variant="plain"
                              onClick={() =>
                                setOpenTestColumnMappingModal(true)
                              }
                            >
                              Test column mapping
                            </Button>

                            <Button
                              variant="primary"
                              submit
                              loading={isSubmitting}
                            >
                              Start process
                            </Button>
                          </InlineStack>
                        </>
                      </form>
                    )}
                  </Formik>
                ))
                .otherwise(({ source }) => (
                  <Formik
                    enableReinitialize
                    initialValues={{
                      id: source.id || '',
                      scheduleType: source.scheduleType || '',
                      scheduleTime: source.scheduleTime || '',
                      scheduleInterval: source.scheduleInterval,
                      scheduleDay: source.scheduleDay || '',
                    }}
                    validationSchema={getScheduleSchema(shop.scheduleMinHour)}
                    onSubmit={async (values) => {
                      const res = await updateSource({
                        variables: { source: values },
                      });
                      if (!res.data) return;
                      notification.open({
                        options: { variant: 'success' },
                        message: `${res.data.source.name} successfully updated`,
                      });
                      setSchedulePopoverVisible(false);
                    }}
                  >
                    {({
                      values,
                      handleSubmit,
                      setFieldValue,
                      errors,
                      isSubmitting,
                    }) => (
                      <div style={{ position: 'relative' }}>
                        <div style={{ margin: '24px 0px' }}>
                          <Card>
                            <div
                              style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                              }}
                            >
                              <div
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                }}
                              >
                                Schedule {scheduleStatus}
                                <span style={{ marginLeft: 10 }} />
                                <Popover
                                  active={schedulePopoverVisible}
                                  activator={
                                    <Tooltip
                                      content={
                                        !scheduleEnabled
                                          ? 'Enable schedule to change'
                                          : 'Click to change.'
                                      }
                                    >
                                      <Button
                                        disabled={!scheduleEnabled}
                                        onClick={() => {
                                          setSchedulePopoverVisible(true);
                                        }}
                                      >
                                        {scheduledDaily
                                          ? `at ${source.scheduleTime} daily`
                                          : `every ${source.scheduleInterval} hour(s)`}
                                      </Button>
                                    </Tooltip>
                                  }
                                  onClose={() => {
                                    setSchedulePopoverVisible(false);
                                  }}
                                  sectioned
                                >
                                  <form onSubmit={handleSubmit}>
                                    <FormLayout>
                                      <label style={{ color: '#777' }}>
                                        Automate this process?
                                      </label>
                                      <Select
                                        label="Scheduling"
                                        options={[
                                          { label: 'Hourly', value: 'hourly' },
                                          { label: 'Daily', value: 'daily' },
                                        ]}
                                        value={values.scheduleType}
                                        onChange={(value) => {
                                          setFieldValue('scheduleType', value);
                                        }}
                                      />
                                      {values.scheduleType === 'daily' ? (
                                        <TextField
                                          label="Time"
                                          type="time"
                                          autoComplete="off"
                                          helpText={
                                            <div>
                                              <Button
                                                variant="plain"
                                                url={routes.editPreferences}
                                              >
                                                {`${shop.timezone}`}
                                              </Button>
                                            </div>
                                          }
                                          value={xdayjs(
                                            values.scheduleTime,
                                            'HH:mm a'
                                          ).format('HH:mm')}
                                          onChange={(value) => {
                                            setFieldValue(
                                              'scheduleTime',
                                              xdayjs(value, 'HH:mm').format(
                                                'HH:mm a'
                                              )
                                            );
                                          }}
                                        />
                                      ) : (
                                        <TextField
                                          label="Interval (Hours)"
                                          type="number"
                                          autoComplete="off"
                                          value={values.scheduleInterval.toString()}
                                          min={0}
                                          error={errors.scheduleInterval}
                                          onChange={(value) => {
                                            setFieldValue(
                                              'scheduleInterval',
                                              parseInt(value)
                                            );
                                          }}
                                        />
                                      )}
                                      {values.scheduleType === 'daily' && (
                                        <ChoiceList
                                          titleHidden
                                          title="Company name"
                                          choices={[
                                            {
                                              label: 'Every Day',
                                              value: 'everyday',
                                            },
                                            {
                                              label: 'Weekdays only',
                                              value: 'weekday',
                                            },
                                          ]}
                                          selected={[values.scheduleDay]}
                                          onChange={(value) => {
                                            setFieldValue(
                                              'scheduleDay',
                                              value[0]
                                            );
                                          }}
                                        />
                                      )}
                                      <Button
                                        loading={isSubmitting}
                                        variant="primary"
                                        submit
                                      >
                                        Save
                                      </Button>
                                    </FormLayout>
                                  </form>
                                </Popover>
                              </div>
                              <Button
                                size="slim"
                                loading={updateSourceResult.loading}
                                onClick={() => {
                                  updateSource({
                                    variables: {
                                      source: {
                                        id: source.id,
                                        syncStatus: source.syncStatus,
                                      },
                                    },
                                  }).then((res) => {
                                    if (!res.data) return;
                                    const { syncStatus } = res.data.source;
                                    notification.open({
                                      options: { variant: 'success' },
                                      message: `Schedule successfully ${
                                        syncStatus === 'started'
                                          ? 'enabled'
                                          : 'disabled'
                                      }`,
                                    });
                                  });
                                }}
                              >
                                {syncStatus}
                              </Button>
                            </div>
                          </Card>
                        </div>
                        <InlineStack blockAlign="center" align="space-between">
                          <Button
                            variant="plain"
                            onClick={() => setOpenTestColumnMappingModal(true)}
                          >
                            Test column mapping
                          </Button>
                          <PrimaryButton
                            iconType="tick"
                            onClick={() => {
                              confirmation.open({
                                message: (
                                  <div>
                                    <Text variant="headingMd" as="h2">
                                      Sync now?
                                    </Text>
                                    <p>Please click Yes to proceed.</p>
                                  </div>
                                ),
                                onConfirm: () => {
                                  syncNow({ values: { id: source.id } });
                                  confirmation.close();
                                },
                              });
                            }}
                          >
                            Start process
                          </PrimaryButton>
                        </InlineStack>
                      </div>
                    )}
                  </Formik>
                ))}
            </div>
          </Card>
        </Grid.Cell>
        <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
          <Card background="nav-bg-surface-active">
            <InlineStack blockAlign="start" align="space-between">
              <div>
                <Text variant="headingMd" as="p">
                  Activity log
                </Text>
                <Text variant="bodyMd" as="p" tone="subdued">
                  Latest 10 only
                </Text>
              </div>

              <Link url="./export_sync_logs.csv">
                {' '}
                <i className="fa fa-download" /> Download All Activities
              </Link>
            </InlineStack>
            <div style={{ paddingBottom: 20 }} />
            {syncLogs.length > 0 ? (
              <ResourceList
                items={syncLogs}
                renderItem={(syncLog) => {
                  const numberFulfillmentUpdated = Number(
                    syncLog.numberFulfillmentUpdated
                  );
                  return (
                    <ResourceItem
                      key={syncLog.id}
                      id={source.id}
                      onClick={() => {}}
                    >
                      <div style={{ padding: '15px 0px' }}>
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                          }}
                        >
                          <strong>{syncLog.source.name}</strong>
                          {numberFulfillmentUpdated > 0 && (
                            <Badge tone="success">
                              {`${numberFulfillmentUpdated} Fulfilled`}
                            </Badge>
                          )}
                        </div>
                        <div
                          style={{
                            margin: '5px 0px',
                            lineHeight: 1.67,
                            wordWrap: 'break-word',
                            wordBreak: 'break-word',
                          }}
                        >
                          {syncLog.errorMessage}
                        </div>
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                          }}
                        >
                          <Badge tone="new">
                            {
                              {
                                user: 'User',
                                ui_all: 'User',
                                ui: 'User',
                                scheduler: 'Scheduler',
                                support: 'Support',
                              }[syncLog.caller]
                            }
                          </Badge>
                          <Text variant="bodyMd" as="span" tone="subdued">
                            {`${xdayjs(syncLog.processedAt).fromNow()}, ${xdayjs(
                              syncLog.processedAt
                            ).format('MMM D, h:mm a')}`}
                          </Text>
                        </div>
                      </div>
                    </ResourceItem>
                  );
                }}
              />
            ) : (
              <div style={{ margin: '10px 0px' }}>
                <Text variant="bodyMd" as="span" tone="subdued">
                  No activities yet
                </Text>
              </div>
            )}
          </Card>
        </Grid.Cell>
      </Grid>
      {openTestColumnMappingModal && (
        <TestColumnMappingModal
          source={source}
          onClose={() => setOpenTestColumnMappingModal(false)}
        />
      )}
    </CurrentPage>
  );
}

interface PrimaryButtonProps
  extends Omit<ButtonProps, 'primary' | 'size' | 'icon'> {
  iconType: 'spin' | 'tick';
}

function PrimaryButton({ iconType, ...props }: PrimaryButtonProps) {
  return (
    <Button
      variant="primary"
      size="slim"
      icon={match(iconType)
        .with('tick', () => CheckIcon)
        .with('spin', () => <i className="fas fa-circle-notch fa-spin" />)
        .otherwise(() => undefined)}
      {...props}
    />
  );
}

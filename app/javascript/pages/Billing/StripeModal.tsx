import { useMutation } from '@apollo/client';
import { Button, Modal, Text } from '@shopify/polaris';
import * as React from 'react';

import { CreateStripeCheckoutMutation } from '@/gql/mutations';
import { useCurrentShop } from '@/hooks/useCurrentShop';
import { useNotification } from '@/hooks/useNotification';

interface StripeModalProps {
  price: string;
  credit: number;
}

export function StripeModal({ price, credit }: StripeModalProps) {
  const currentShop = useCurrentShop().currentShop;
  const notification = useNotification();
  const [modalVisible, setModalVisible] = React.useState(false);
  const [createStripeCheckout, createStripeCheckoutResult] = useMutation(
    CreateStripeCheckoutMutation
  );

  return (
    <>
      <Button
        variant="primary"
        onClick={() => setModalVisible(true)}
        disabled={!currentShop.useCredit}
      >
        Top Up - ${price}
      </Button>
      <Modal
        open={modalVisible}
        onClose={() => setModalVisible(false)}
        title="Buy Credit"
        primaryAction={{
          content: 'Checkout',
          loading: createStripeCheckoutResult.loading,
          onAction: () => {
            createStripeCheckout({
              variables: { price, credit },
            })
              .then((res) => {
                if (!res.data) return;
                const { sessionUrl, message, success } =
                  res.data.createStripeCheckout;

                if (success) {
                  window.location.href = sessionUrl;
                } else {
                  notification.open({
                    options: { variant: 'error' },
                    message,
                  });
                }
              })
              .finally(() => {
                setModalVisible(false);
              });
          },
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            onAction: () => setModalVisible(false),
          },
        ]}
      >
        <Modal.Section>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Text as="h1" variant="headingMd">
              FulfillSync Credit ({credit} Credit)
            </Text>
            <Text variant="bodyMd" as="span" tone="subdued">
              ${price} USD one-time charged
            </Text>
          </div>
        </Modal.Section>
      </Modal>
    </>
  );
}

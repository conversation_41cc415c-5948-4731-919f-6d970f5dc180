import { LayoutCenter } from '@/components/LayoutCenter';
import * as routes from '@/routes';

export const NotFound = () => (
  <LayoutCenter>
    <div style={{ textAlign: 'center' }}>
      <header>
        <img
          src="/FS_logo.png"
          width="350px"
          height="auto"
          alt="FulfillSync logo"
        />
        <h2 style={{ fontSize: 25, margin: 21 }}>Page Not Found!</h2>
        <hr style={{ width: 50 }} />
        <p style={{ fontSize: 16, margin: 21 }}>
          The page you were looking for can't be found.
        </p>
      </header>

      {/* TODO: Link / Button component */}
      <a
        href={routes.dashboard}
        style={
          {
            '--primary-color': '#0facf3',
            display: 'inline-block',
            backgroundColor: 'var(--primary-color)',
            borderRadius: '6px',
            color: '#fff',
            padding: '10px 16px',
            border: '1px solid var(--primary-color)',
            fontSize: '15px',
            textDecoration: 'none',
          } as React.CSSProperties
        }
      >
        Back to the app
      </a>
    </div>
  </LayoutCenter>
);

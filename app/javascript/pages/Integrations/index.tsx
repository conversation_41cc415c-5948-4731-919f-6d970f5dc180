import { useMutation } from '@apollo/client';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  FormLayout,
  Text,
  TextField,
} from '@shopify/polaris';
import { Formik } from 'formik';
import { useNavigate } from 'react-router-dom';

import { CurrentPage } from '@/components/CurrentPage';
import { Link } from '@/components/Link';
import { EditShopMutation } from '@/gql/mutations';
import { useCurrentShop } from '@/hooks/useCurrentShop';
import { useNotification } from '@/hooks/useNotification';
import * as routes from '@/routes';
import * as schemas from '@/schemas';

export function Integrations() {
  const navigate = useNavigate();
  const notification = useNotification();
  const shop = useCurrentShop().currentShop;
  const [editShop] = useMutation(EditShopMutation);
  return (
    <CurrentPage
      titleMetadata="API"
      backAction={{
        content: 'Back to dashboard',
        url: routes.dashboard,
      }}
      secondaryActions={[
        {
          content: 'Fulfillment log',
          url: routes.fulfillments,
        },
        {
          content: 'Preferences',
          url: routes.editPreferences,
        },
      ]}
    >
      {!shop.connected && (
        <Banner tone="warning">
          Please fill in the api credentials in order to integrate with
          Bigcommerce.
        </Banner>
      )}
      <Card>
        <div style={{ marginBottom: '20px', display: 'inline-block' }}>
          <Text variant="headingMd" as="h2">
            Edit integration settings
          </Text>
        </div>
        <Formik
          enableReinitialize
          initialValues={{
            bigcommerceStoreHash: shop.bigcommerceStoreHash || '',
            bigcommerceClientId: shop.bigcommerceClientId || '',
            bigcommerceAccessToken: shop.bigcommerceAccessToken || '',
          }}
          validationSchema={schemas.Shop}
          onSubmit={(values) => {
            editShop({
              variables: {
                shop: {
                  id: shop.id,
                  bigcommerceStoreHash: values.bigcommerceStoreHash,
                  bigcommerceClientId: values.bigcommerceClientId,
                  bigcommerceAccessToken: values.bigcommerceAccessToken,
                },
              },
            })
              .then((res) => {
                notification.open({
                  options: { variant: 'success' },
                  message: 'Preferences saved successfully.',
                });
                if (res.data.editShop.connected) {
                  navigate(routes.dashboard);
                }
              })
              .catch((error) => {
                console.error(error);
                notification.open({ options: { variant: 'error' } });
              });
          }}
        >
          {({ handleSubmit, values, errors, handleBlur, setFieldValue }) => (
            <form onSubmit={handleSubmit}>
              <FormLayout>
                <TextField
                  label="Store Hash"
                  helpText="eg: 7tgimdckcz"
                  onChange={(text) => {
                    setFieldValue('bigcommerceStoreHash', text);
                  }}
                  onBlur={handleBlur}
                  value={values.bigcommerceStoreHash}
                  error={errors.bigcommerceStoreHash}
                  readOnly
                  autoComplete="off"
                />
                <TextField
                  label="Client ID"
                  helpText="eg: 20uodrrrzysnisizpmvcu53x08mydxp"
                  onChange={(text) => {
                    setFieldValue('bigcommerceClientId', text);
                  }}
                  onBlur={handleBlur}
                  value={values.bigcommerceClientId}
                  error={errors.bigcommerceClientId}
                  readOnly
                  autoComplete="off"
                />
                <TextField
                  label="Access Token"
                  helpText="eg: hr0cqq0y0gay633suwqn5nehh323yqy"
                  onChange={(text) => {
                    setFieldValue('bigcommerceAccessToken', text);
                  }}
                  onBlur={handleBlur}
                  value={values.bigcommerceAccessToken}
                  error={errors.bigcommerceAccessToken}
                  readOnly
                  autoComplete="off"
                />
                <Button variant="primary" submit>
                  Update
                </Button>
                <p>
                  Click{' '}
                  <Link
                    url="https://fulfilltracking.freshdesk.com/support/solutions/articles/44002602700-set-up-source-type"
                    target="_blank"
                  >
                    here
                  </Link>{' '}
                  if you are not sure where to get the api credentials.
                </p>
              </FormLayout>
            </form>
          )}
        </Formik>
      </Card>
    </CurrentPage>
  );
}

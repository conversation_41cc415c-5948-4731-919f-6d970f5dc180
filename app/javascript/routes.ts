import type { Package } from '@/types';

export const dashboard = '/';
export const sourceSettings = '/sources/:id/settings';
export const sourceDetail = '/sources/:id';
export const newSource = '/sources/new';
export const editPreferences = '/edit_preferences';
export const welcome = '/welcome';
export const editIntegrations = '/edit_integrations';
export const fulfillments = '/fulfillments';
export const paypal = '/paypal';
export const billing = '/billing';
export const downloadSampleCsv = '/sample.csv';
export const getSubcribeToPlan = ({ plan }: { plan: Package }) =>
  `/subscribe?package=${plan}`;
export const getSourceSettingsRoute = (id) => `/sources/${id}/settings`;
export const getSourceDetail = (id) => `/sources/${id}`;
export const getCreditSystemRoute = (credit) =>
  `/credit_charge?credit=${credit}`;
export const getCreditChargeCallbackRoute = (charge_id, credit) =>
  `/credit_charge_callback?charge_id=${charge_id}&credit=${credit}`;

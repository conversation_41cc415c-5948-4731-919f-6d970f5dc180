# = require active_admin/base
# = require highcharts
# = require highcharts/highcharts-more

$ ->
  $(".load-order-num").click (e) ->
    e.preventDefault()
    profile_id = window.location.pathname.split("/").pop()
    $.ajax
      type: 'GET'
      url: "/admin/sources/#{profile_id}/pre_debug_order"
      dataType: 'json'
      success: (data) ->
        comfirmation = confirm("Warning, we're loading #{data} of order number, do you want to proceed?");
        if comfirmation == true 
          window.open("/admin/sources/#{profile_id}/debug_orders", '_blank');

   
  $('#header ul#utility_nav').prepend(
    '<li style="display: inline-flex;height:30px">
      <form style="display: inherit;" method="get" action="/admin/shops">
      <input style="width: 180px" id="search-store" type="text" placeholder="Store URL" name="q[shopify_domain_contains]"></input>
      <button style="margin-left:5px;padding: 6px 10px">
        <img src="https://img.icons8.com/material-outlined/16/ffffff/search--v1.png"/>
      </button>
      </form>
    </li>');       


  copyText = (element) ->
    $temp = $("<input>")
    $("body").append($temp)
    $temp.val($(element).text()).select()
    document.execCommand("copy")
    $temp.remove();

  $("#source-host, #source-url, #source-login, #source-password").click (e) ->
    e.preventDefault()
    if $(this).attr("id") == "source-host"
      copyText('#copy-source-host')
    else if $(this).attr("id") == "source-url"
      copyText('#copy-source-url')
    else if $(this).attr("id") == "source-login"
      copyText('#copy-source-login');  
    else
      copyText('#copy-source-password');

  $('head').append("<link href='/fs_favicon.svg' rel='shortcut icon'>");
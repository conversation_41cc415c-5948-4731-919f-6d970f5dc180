$ ->
  handle_schedule = () ->
    job_time = $("#schedule-time")
    job_interval = $("#schedule-interval")
    if $("#source_schedule_type").val() == "hourly"
      job_interval.show()
      job_time.hide()
    else if $("#source_schedule_type").val() == "daily"
      job_time.show()
      job_interval.hide()
    else
      job_interval.hide()
      job_time.hide()

  handle_source_rename_field = () ->
    source_rename = $("#source-rename-field")
    if $("#source_source_process").val() == "rename"
      source_rename.show()
    else
      source_rename.hide()

  toggle_source_type_input = () ->
    source_type = $("#source_source_type").val()
    $(".source-type-settings, .settings-panel").each (index, elm) ->
      elm = $(elm)
      hide_type = $(elm).attr("data-hide-from-source-type")
      if hide_type.indexOf(source_type) > -1
        elm.hide()
      else
        elm.show()
    handle_source_rename_field()
    handle_schedule()

  toggle_source_type_input()

  handle_schedule()

  $(".source_is_active").bootstrapSwitch
    onText: "Active",
    offText: "Inactive",
    onColor: "success",
    offColor: "danger",
    size: "small"

  $(".test-column-mapping-btn").on "click", (e) ->
    e.preventDefault()
    elm = $(e.currentTarget)
    url = elm.data('url')
    ShopifyApp.Modal.open {
      src: url
      title: "Column Mapping"
      height: 600
      buttons:
        primary:
          label: 'OK'
          message: 'modal_ok'
          callback: (message) ->
            ShopifyApp.Modal.close()
            return
    }

  $("#source_source_process").on "change", (e) ->
    handle_source_rename_field()

  $("#source_source_type").on "change", (e) ->
    toggle_source_type_input()

  $("#source_schedule_type").on "change", (e) ->
    handle_schedule()

  $("#source_schedule_type").change

  $('.source_is_active').on 'switchChange.bootstrapSwitch', (event, state) ->
    url = $(this).data('url')
    $.ajax
      type: "POST"
      url: url
      dataType: "json"
      data: { is_active: state }
      success: (data) =>
        if data.status == false
          # $('#source_is_active').bootstrapSwitch('toggleState', true)
          $('.source_is_active').bootstrapSwitch('state', false)
          ShopifyApp.flashError data.error
      error: (jqXHR, textStatus, errorThrown) =>
        $('.source_is_active').bootstrapSwitch('toggleState', true)
        ShopifyApp.flashError errorThrown
        
  options = {
    useEasing : true, 
    useGrouping : true, 
    separator : ',', 
    decimal : '.', 
    prefix : '', 
    suffix : '' 
  }
  if $("#fulfilled-orders").length > 0
    exportedCounts = $("#fulfilled-orders").attr('data-orders')
    counts = new CountUp("fulfilled-orders", 0, exportedCounts, 0, 2.5, options)
    counts.start()
$ ->
  $.validator.setDefaults
    errorElement: 'span'
    errorClass: 'help-block'
    highlight: (element, errorClass, validClass) ->
      $(element).closest('.form-group').addClass 'has-error'
      return
    unhighlight: (element, errorClass, validClass) ->
      $(element).closest('.form-group').removeClass 'has-error'
      return
    errorPlacement: (error, element) ->
      if element.parent('.input-group').length or element.prop('type') == 'checkbox' or element.prop('type') == 'radio'
        error.insertAfter element.parent()
      else
        error.insertAfter element
      return

  $(".edit-source-form").validate
    rules:
      "source[name]":
        required: true
      "source[source_url]":
        required: (element) ->
          $("#source_source_type").val() == "url";
      "source[source_host]":
        required: (element) ->
          $("#source_source_type").val() != "url";
      "source[path_to_file]":
        required: true
      "source[source_process]":
        required: true
      "source[source_rename]":
        required: (element) ->
          $("#source_source_process").val() == "rename";
      "source[source_login]":
        required: true
      "source[source_password]":
        required: (element) ->
          $("#source_source_type").val() == "ftp";
      "source[schedule_type]":
        required: true
      "source[schedule_interval]":
        required: (element) ->
          $("#source_schedule_type").val() == "hourly";
        digits: true
      "source[order_no_mapping]":
        required: true
        digits: true
      "source[sku_mapping]":
        digits: true
      "source[quantity_mapping]":
        digits: true
      "source[tracking_no_mapping]":
        digits: true
      "source[tracking_company_mapping]":
        digits: true
      "source[tracking_url_mapping]":
        digits: true
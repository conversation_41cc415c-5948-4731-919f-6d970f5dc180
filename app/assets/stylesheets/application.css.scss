@import 'font-awesome-sprockets';
@import 'font-awesome';
@import 'bootstrap-sprockets';
@import 'bootstrap';
@import 'bootstrap-datetimepicker';
@import 'bootstrap3-switch';

.main-container {
  padding: 20px;
}

.pagination > li > a,
.pagination > li > span {
  position: relative;
  float: left;
  padding: 5px 10px;
  line-height: 1.5;
  text-decoration: none;
  color: #337ab7;
  background-color: #fff;
  border: 1px solid #ddd;
  margin-left: -1px;
}

// .form-group.required .control-label:before {
//   content:"*";
// }

.form-group .control-label:after {
  content: ' (optional)';
  font-weight: 400;
}
.form-group.ignore-optional .control-label:after,
.form-group.required .control-label:after {
  content: '';
}

.sample-order-mapping th {
  text-align: center;
}

.option .btn {
  margin-bottom: 5px;
}

.processed-at {
  font-size: 10px;
}

[tooltip]:before {
  /* needed - do not touch */
  content: attr(tooltip);
  position: absolute;
  opacity: 0;

  /* customizable */
  transition: all 0.15s ease;
  padding: 10px;
  color: #333;
  border-radius: 10px;
  box-shadow: 2px 2px 1px silver;
}

[tooltip]:hover:before {
  /* needed - do not touch */
  opacity: 1;

  /* customizable */
  background: white;
  margin-top: -50px;
  margin-left: 20px;
}

[tooltip]:not([tooltip-persistent]):before {
  pointer-events: none;
}

#btn-help {
  text-center: center;
  position: fixed;
  bottom: 40px;
  left: 40px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  background: #fff;
  z-index: 1000;
}
#btn-help span {
  font-size: 50px;
  color: #0078bd;
}
.row-line {
  display: inline-block;
}

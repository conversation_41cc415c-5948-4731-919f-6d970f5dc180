class UpdateFulfillmentsJob < ApplicationJob
  queue_as :update_fulfillments

  after_enqueue do |job|
    Delayed::Job.where(id: job.provider_job_id).update_all(source_id: job.arguments.first)
  end

  def perform(*args)
    puts inspect
    source_id = args[0]
    source = Source.find(source_id)
    caller = (source.source_type == "email") ? :email : :scheduler
    result = source.update_fulfillments(Time.zone.now, caller)
    GC.start
    ## email not sending in :inline mode
    ## please change queue adapter to delayed job in development.rb if it's needed
    result
  end

  def after(job)
    # https://github.com/collectiveidea/delayed_job/wiki/How-to-release-memory-after-jobs-completed-%3F
    if `ps -o rss= -p #{Process.pid}`.to_i > Settings.memory_limit
      `kill -15 #{Process.pid}`
    end
  end

  rescue_from(StandardError) do |exception|
    Airbrake.notify(exception)
    retry_job(wait: 10.minutes)
  end
end

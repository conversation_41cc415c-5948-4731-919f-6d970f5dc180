class AdminFulfillmentsJob < ApplicationJob
  queue_as :update_fulfillments

  def perform(*args)
    source_id = args[0]
    source = Source.find(source_id)
    caller = args[1]
    update_all = args[2]
    result = source.sync_fulfillments(caller.to_sym, update_all)
    GC.start
    ## email not sending in :inline mode
    ## please change queue adapter to delayed job in development.rb if it's needed
    result
  end

  rescue_from(StandardError) do |exception|
    Airbrake.notify(exception)
    retry_job(wait: 10.minutes)
  end
end

class GraphqlController < ApplicationController
  skip_before_action :verify_authenticity_token, only: [:execute]
  before_action :authenticate_by_api_token!

  def execute
    if params[:operations].present?
      operations = ensure_hash(params[:operations]).symbolize_keys
      variables = operations[:variables].merge({"sourceFile" => params["0"]})
      query = operations[:query]
      operation_name = operations[:operationName]
    else
      variables = ensure_hash(params[:variables])
      query = params[:query]
      operation_name = params[:operationName]
    end

    context = {
      current_shop: current_shop,
      current_user: current_user
    }
    result = EmbededAppSchema.execute(query, variables: variables, context: context, operation_name: operation_name)
    render json: result
  end

  private

  # Handle form data, JSON body, or a blank value
  def ensure_hash(ambiguous_param)
    case ambiguous_param
    when String
      if ambiguous_param.present?
        ensure_hash(JSON.parse(ambiguous_param))
      else
        {}
      end
    when Hash, ActionController::Parameters
      ambiguous_param
    when nil
      {}
    else
      raise ArgumentError, "Unexpected parameter: #{ambiguous_param}"
    end
  end
end

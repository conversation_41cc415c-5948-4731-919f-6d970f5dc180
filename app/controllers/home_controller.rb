class HomeController < ShopifyApp::AuthenticatedController
  around_action :shopify_session, except: %i[uninstall export_fulfillment_logs export_sync_logs]
  before_action :authenticate_user!, only: %i[export_fulfillment_logs export_sync_logs]
  protect_from_forgery except: [:uninstall]

  def index
    @shop = current_shop
    @sources = current_shop.sources.order("created_at desc")
    @fulfilled_orders_count = 5_088_028 + 7_714_459
    @fulfillment_logs = current_shop.fulfillment_logs.order("id DESC").page(params[:page] || 1).per(25)
    @sync_logs = current_shop.sync_logs.order("id DESC").page(params[:page] || 1).per(10)
  end

  def preferences
    @shop = current_shop
  end

  def update_preferences
    @shop = Shop.find(params[:shop][:id])
    if @shop.update(shop_params)
      flash[:notice] = "Preferences Updated Successfully"
      redirect_to :root
    else
      flash[:error] = @shop.errors.full_messages.to_sentence.html_safe
      render "preferences"
    end
  end

  def faq
  end

  def upgrade
    charge = nil
    package = Package.new[params[:package]]
    if package.present?
      charge = ShopifyAPI::RecurringApplicationCharge.new
      charge.name = package.title
      charge.price = package.cost
      charge.trial_days = 0
      charge.return_url = callback_charge_url(package: params[:package])
      charge.test = Settings.test_stores.include?(current_shop.shopify_domain) || Rails.env.development?
      charge.save!
    end
    @redirect_url = if charge.try(:status) == "pending"
      charge.confirmation_url
    else
      root_url
    end
    render "/common/iframe_redirect", format: [:html], layout: false
  end

  def callback_charge
    if params[:charge_id]
      charge = ShopifyAPI::RecurringApplicationCharge.find(id: params[:charge_id])
      charge.activate if charge.status == "accepted"

      if ["active"].include?(charge.status) && current_shop.present?
        current_shop.charge_id = params[:charge_id]
        current_shop.charged_at = DateTime.now
        package = Package.new[params[:package]]
        if package.present?
          current_shop.package = params[:package]
          current_shop.source_limit = package.limit
        end
      elsif current_shop.present?
        current_shop.charge_id = nil
      end
      current_shop&.save(validate: false)
    end
    redirect_to root_url
  end

  def uninstall
    shop = Shop.where(shopify_domain: params[:myshopify_domain]).first

    if shop.present?
      shop.deactivate_shop
      shop.update(charge_id: nil, charged_at: nil, package: "trial", uninstalled_at: Time.now, use_credit: true)
      UserMailer.uninstall_email(shop).deliver_now

    end
    head :ok
  end

  private

  def shop_params
    params.require(:shop).permit(:notification_email)
  end
end

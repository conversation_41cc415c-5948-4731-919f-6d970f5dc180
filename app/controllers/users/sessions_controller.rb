class Users::SessionsController < Devise::SessionsController
  # POST /resource/sign_in
  def create
    resource = User.find_by_email(params[:user][:email])

    if resource
      if resource.valid_password?(params[:user][:password])
        shop = resource.shops.find_by_id(params[:shop_id])

        if shop
          session[:shop_id] = shop.id
          sign_in_and_redirect resource
        else
          flash[:error] = "The shop is not belong to this user. Please sign in using the correct user."
          redirect_to new_user_session_path
        end
      else
        flash[:error] = "Invalid password"
        redirect_to sign_in_integrations_path shop_id: params[:shop_id]
      end
    else
      flash[:error] = "The email address does not exist."
      redirect_to sign_in_integrations_path shop_id: params[:shop_id]
    end
  end
end

class Users::RegistrationsController < Devise::RegistrationsController
  # POST /resource
  def create
    super do |resource|
      shop = Shop.find(params[:shop_id])
      shop.update(user_id: resource.id)
      session[:shop_id] = shop.id
    end
  end

  # If you have extra params to permit, append them to the sanitizer.
  def configure_sign_up_params
    devise_parameter_sanitizer.permit(:sign_up, keys: [:shop_id])
  end
end

class Users::OmniauthCallbacksController < Devise::OmniauthCallbacksController
  include PayPal::SDK::OpenIDConnect
  require "action_view"

  include ActionView::Helpers::SanitizeHelper
  protect_from_forgery except: %i[shopify ecwid bigcommerce bigcommerce_app]
  def shopify
    @platform = "Shopify"
    @shop = Shopify.from_omniauth(auth_hash)
    login_user
  end

  def shopify_redirect_post
    params.permit!
    hash_params = params.to_h

    if hash_params.key?("shop") && hash_params["shop"].include?(".myshopify.com") && sanitize(hash_params["shop"]) == hash_params["shop"]
      redirect_post("/auth/shopify", params: hash_params, options: {authenticity_token: :auto})
    else
      redirect_to root_path
    end
  end

  def ecwid
    @platform = "Ecwid"
    response = Ecwid.auth.access_token(params[:code])
    @shop = Ecwid.from_omniauth(response)
    login_user
  end

  def ecwid_redirect_post
    params.permit!
    redirect_post("/auth/ecwid", params: params.to_h, options: {authenticity_token: :auto})
  end

  def bigcommerce
    @shop = BigCommerce.from_omniauth(params)
    sign_out(current_user)

    if @shop.user
      redirect_to sign_in_integrations_path(shop_id: @shop.id)
    else
      redirect_to new_user_registration_path(shop_id: @shop.id)
    end
  end

  def bigcommerce_redirect_post
    params.permit!
    redirect_post("/auth/bigcommerce", params: params.to_h, options: {authenticity_token: :auto})
  end

  def bigcommerce_app
    url = "https://login.bigcommerce.com/oauth2/token"
    payload = {
      client_id: ENV["BIGCOMMERCE_CLIENT_ID"],
      client_secret: ENV["BIGCOMMERCE_CLIENT_SECRET"],
      code: params[:code],
      scope: params[:scope],
      grant_type: "authorization_code",
      redirect_uri: auth_bigcommerce_app_callback_url,
      context: params[:context]
    }
    resp = Faraday.post(url, payload.to_json, "Content-Type" => "application/json")

    if resp.status == 200
      result = JSON.parse(resp.body, object_class: OpenStruct)
      puts result
      @shop = BigCommerce.from_callback(result)

      login_user
    else
      sign_out(current_user)
      redirect_to :root
    end
  end

  def bigcommerce_app_redirect_post
    params.permit!
    redirect_post("/auth/bigcommerce_app", params: params.to_h, options: {authenticity_token: :auto})
  end

  def paypal
    if params[:code]
      url = "https://api-m.sandbox.paypal.com/v1/identity/openidconnect/tokenservice"
      auth = "#{ENV["PAYPAL_CLIENT_ID"]}:#{ENV["PAYPAL_CLIENT_SECRET"]}"
      encoded_text = Base64.strict_encode64(auth)
      body = {
        grant_type: "authorization_code",
        code: params[:code]
      }
      headers = {
        Authorization: "Basic #{encoded_text}"
      }

      response = Faraday.post(url) do |req|
        req.headers = headers
        req.headers["Content-Type"] = "application/x-www-form-urlencoded"
        req.body = URI.encode_www_form(body)
      end

      result = JSON.parse(response.body)

      access_token = result["access_token"]
      refresh_token = result["refresh_token"]

      current_shop.paypal_access_token = access_token
      current_shop.paypal_refresh_token = refresh_token
      current_shop.save(validate: false)
    end
    redirect_to paypal_path
  end

  def paypal_redirect_post
    params.permit!
    redirect_post("/auth/paypal", params: params.to_h, options: {authenticity_token: :auto})
  end

  private

  def login_user
    if @shop.persisted?
      if @platform == "Shopify"
        login_shop
        install_webhooks
        install_scripttags
        perform_after_authenticate_job
      end

      @user = @shop.user
      session[:shop_id] = @shop.id
      session[:true_shop_id] = @shop.id
      cookies[:uptracker_current_shop] = @shop.id

      sign_in_and_redirect @user, event: :authentication
      if is_navigational_format?
        set_flash_message(:notice, :success, kind: @platform)
      end
    else
      session["devise.omniauth_data"] = auth_hash
      puts "@shop error"
      p @shop.errors
      Rails.logger.info @shop.errors
      redirect_to :root
    end
  end

  def login_shop
    @session ||= ShopifyAPI::Auth::Session.new(shop: shop_name, access_token: token)
    ShopifyAPI::Context.activate_session(@session)

    request.session_options[:renew] = true
    session.delete(:_csrf_token)

    session[:shopify] = @shop.id
    session[:shopify_domain] = shop_name
    session[:shopify_user] = associated_user if associated_user.present?
  end

  def auth_hash
    request.env["omniauth.auth"]
  end

  def shop_name
    auth_hash.uid
  end

  def token
    auth_hash["credentials"]["token"]
  end

  def associated_user
    return unless auth_hash["extra"].present?

    auth_hash["extra"]["associated_user"]
  end

  def install_webhooks
    return unless ShopifyApp.configuration.has_webhooks?

    ShopifyApp::WebhooksManager.queue(
      shop_name,
      token
    )
  end

  def install_scripttags
    return unless ShopifyApp.configuration.has_scripttags?

    ShopifyApp::ScripttagsManager.queue(
      shop_name,
      token,
      ShopifyApp.configuration.scripttags
    )
  end

  def perform_after_authenticate_job
    config = ShopifyApp.configuration.after_authenticate_job

    return unless config && config[:job].present?

    if config[:inline] == true
      config[:job].perform_now(shop_domain: session[:shopify_domain])
    else
      config[:job].perform_later(shop_domain: session[:shopify_domain])
    end
  end

  def return_address
    session.delete(:return_to) || ShopifyApp.configuration.root_url
  end
end

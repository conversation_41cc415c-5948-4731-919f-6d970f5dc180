require "base64"
require "openssl"

class BigcommerceController < ApplicationController
  include Devise::Controllers::Helpers

  class InvalidPayload < StandardError; end

  def load
    @shop, user_info = find_shop
    @user = User.where(email: user_info["email"], shop_id: @shop.id).first_or_initialize do |user|
      user.password = Devise.friendly_token[0, 20]
      user.save
    end
    session[:shop_id] = @shop.id

    sign_in_and_redirect @user, event: :authentication
  end

  def uninstall
    @shop, _user_info = find_shop

    Stripe::Subscription.delete(@shop.stripe_subscription_id) if @shop.stripe_subscription_id.present?
    @shop.deactivate_shop
    @shop.reset_payment

    head :ok
  end

  def remove_user
    Rails.logger.info params.inspect
    @shop, user_info = find_shop
    @user = User.where(email: user_info["email"], shop_id: @shop.id).first
    @user.delete
    respond_with do |format|
      format.xml { render text: "ok" }
    end
  end

  private

  def find_shop
    payload = decode_signed_payload(params[:signed_payload])
    raise InvalidPayload unless payload

    [Shop.find_by(bigcommerce_store_hash: payload["store_hash"]), payload["user"]]
  end

  # Copied from https://developer.bigcommerce.com/api-docs/getting-started/building-apps-bigcommerce/building-apps
  def decode_signed_payload(signed_payload)
    client_secret = ENV["BIGCOMMERCE_CLIENT_SECRET"]
    message_parts = signed_payload.split(".")

    encoded_json_payload = message_parts[0]
    encoded_hmac_signature = message_parts[1]

    payload_object = Base64.strict_decode64(encoded_json_payload)
    provided_signature = Base64.strict_decode64(encoded_hmac_signature)

    expected_signature = OpenSSL::HMAC.hexdigest("sha256", client_secret, payload_object)

    return false unless secure_compare(expected_signature, provided_signature)

    JSON.parse(payload_object)
  end

  def secure_compare(a, b)
    return false if a.blank? || b.blank? || a.bytesize != b.bytesize

    l = a.unpack "C#{a.bytesize}"

    res = 0
    b.each_byte { |byte| res |= byte ^ l.shift }
    res == 0
  end
end

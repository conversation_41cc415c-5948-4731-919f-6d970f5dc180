module ShopifyApp
  class AuthenticatedController < ApplicationController
    include ShopifyApp::LoginProtection
    require "action_view"

    include ActionView::Helpers::SanitizeHelper

    before_action :login_again_if_different_user_or_shop, except: [:uninstall]
    before_action :check_shop_permission
    before_action :check_scope

    def check_shop_permission
      Rails.logger.info "Check shop permission: #{current_shop.inspect}"
      Rails.logger.info "user_signed_in?: #{user_signed_in?}"
      if user_signed_in?
        reset_sess = !current_shop.uninstalled_at.nil?

        reset_sess ||= (current_shop.provider == "Shopify" and params[:shop] and current_shop.shopify_domain != params[:shop])
        if reset_sess
          sign_out(current_user)
          session[:shopify] = nil
          sanitized_shop_params = sanitize(params[:shop])
          if params[:shop].present? && sanitized_shop_params == params[:shop] && params[:shop].include?(".myshopify.com")
            redirect_to "/auth/shopify?shop=#{params[:shop]}"
            nil
          else
            redirect_to :root and return
          end
        end
      end
    end

    def check_scope
      if user_signed_in? && current_shop.provider == "Shopify"
        shop_remote_scope = current_shop.get_shop_scopes.map(&:handle).sort
        app_scope = ShopifyApp.configuration.scope.split(",").sort
        if shop_remote_scope != app_scope
          redirect_to("/auth/shopify?shop=#{current_shop.shopify_domain}")
        end
      end
    end
  end
end

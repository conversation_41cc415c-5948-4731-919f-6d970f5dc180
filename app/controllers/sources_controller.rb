class SourcesController < ShopifyApp::AuthenticatedController
  layout "embedded_app"
  around_action :shopify_session
  before_action :validate_active_user, only: %i[toggle_sync_status sync_fulfillments upload_file_and_sync]
  before_action :find_source, except: %i[new create]

  def new
    if current_shop.source_limit_reach?
      redirect_to :root, notice: "Your plan has reached the maximum number of sources allow"
    else
      @source = current_shop.sources.create(name: "Source #{current_shop.sources.count + 1}", source_host: "localhost", schedule_type: "hourly", schedule_interval: 6, order_no_mapping: "0", schedule_day: "everyday")
      @source.email = @source.assign_source_email
      @source.save

    end
  end

  def create
    @source = current_shop.sources.new(source_params)
    if @source.save
      redirect_to :root
      flash[:notice] = "Source #{@source.name} Created Successfully"
    else
      flash[:error] = @source.errors.full_messages.to_sentence.html_safe
      render "new"
    end
  end

  def edit
  end

  def settings
    render layout: "dashboard"
  end

  def update
    if @source.update(source_params)
      flash[:notice] = "Source #{@source.name} Updated Successfully"
      redirect_to :root
    else
      flash[:error] = @source.errors.full_messages.to_sentence.html_safe
      render "new"
    end
  end

  def destroy
    @source = @source.destroy
    flash[:notice] = "Source #{@source.name} Deleted Successfully"
    redirect_to :root
  end

  def test_column_mapping
    @result = @source.test_column_mapping
  end

  def toggle_sync_status
    respond_to do |format|
      is_active = params[:is_active] == "true"
      if @source.update_sync_status(is_active)
        format.json { render json: {status: true} }
      else
        format.json { render json: {status: false, error: "Failed to update status. Please try again later."} }
      end
    end
  end

  def upload_file_and_sync
    if @source.update(source_params)
      sync_fulfillments
    else
      flash[:error] = @source.errors.full_messages.to_sentence.html_safe
      redirect_to :root
    end
  end

  # use delayed_job
  def sync_fulfillments
    @source.set_queuing
    if params[:commit] == "Upload & Update any orders"
      @source.delay(queue: "update_fulfillments").sync_fulfillments(:ui_all, true)
    else
      @source.delay(queue: "update_fulfillments").sync_fulfillments(:ui)
    end
    flash[:notice] = "Your source is now being updated."
    redirect_to :root
  end

  private

  def find_source
    @source = current_shop.sources.find(params[:id])
  end

  def source_params
    params.require(:source).permit(:name, :source_host, :source_login, :source_password,
      :path_to_file, :schedule_type, :schedule_time,
      :order_no_mapping, :sku_mapping, :quantity_mapping, :tracking_no_mapping,
      :tracking_company_mapping, :source_process, :source_rename, :has_header,
      :schedule_interval, :order_key, :source_type, :tracking_url_mapping, :ssh_key,
      :ignore_key, :ignore_value, :ignore_empty_sku, :source_url, :sync_status, :column_separator,
      :source_file, :notify_customer, :order_identifier_constants, :shopify_order_key_constants, :email,
      :allow_blank_tracking_no, :tracking_company_default, :auto_detect_tracking, :schedule_day, :line_item_identifier, :location_mapping)
  end
end

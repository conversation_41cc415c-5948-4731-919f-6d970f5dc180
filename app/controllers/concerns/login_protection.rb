module LoginProtection
  extend ActiveSupport::Concern
  include Devise::Controllers::Helpers

  def redirect_authentication
    return if params[:shop].nil?
    return if user_signed_in? && current_shop.shopify_domain == params[:shop]

    puts "redirecting #{params[:shop]}"
    redirect_to login_url(shop: params[:shop])
  end

  def relogin_if_different_store
    return false unless params[:shop]

    shop = Shop.where(shopify_domain: params[:shop]).first
    return false unless shop

    if current_shop && (current_shop.shopify_domain != params[:shop])
      sign_out(current_shop)
      sign_in(shop.user)
    end
  end

  def redirect_if_signed_in
    redirect_to root_path if user_signed_in?
  end
end

class ApplicationController < ActionController::Base
  protect_from_forgery with: :exception
  after_action :set_header_for_iframe
  impersonates :user

  expose(:current_shop) do
    curr_shop = nil
    #    Rails.logger.info "curent user: #{current_user.inspect}"
    if current_user
      #      Rails.logger.info "Find session #{session[:shop_id]} / true shop: #{session[:true_shop_id]} / param: #{params[:shop]}"

      curr_shop = if session[:shop_id]
        if @shop && @shop.id == session[:shop_id]
          curr_shop = @shop
        elsif current_user.shop_id == session[:shop_id]
          Shop.find(session[:shop_id])
        else
          begin
            current_user.shops.find(session[:shop_id])
          rescue ActiveRecord::RecordNotFound => _
            current_user.shop
          end
        end
      else
        current_user.shops.where(shopify_domain: params[:shop]).take
      end

      curr_shop.update_details
    end

    curr_shop
  end

  expose(:true_shop) do
    curr_shop = if session[:true_shop_id]
      Shop.find(session[:true_shop_id])
    else
      current_shop
    end
    curr_shop
  end

  def ecwid_authorize
    oauth = Ecwid.auth

    redirect_to oauth.oauth_url
  end

  def routing_error
    respond_to do |format|
      format.html { render file: "#{Rails.root}/public/404", layout: false, status: :not_found }
      format.xml { head :not_found }
      format.any { head :not_found }
    end
  end

  protected

  def validate_active_user
    unless current_shop.active?
      current_shop.deactivate_shop
      error_msg = "Your trial period has ended. Please upgrade to use this feature."
      flash[:error] = error_msg
      respond_to do |format|
        format.html { redirect_to :root }
        format.json { render json: {status: false, error: error_msg} }
      end
    end
  end

  def authenticate_by_api_token!
    authenticate_or_request_with_http_token do |token, _|
      @shop ||= Shop.find_by_api_token(token)
      @shop
    end
  end

  def impersonate_shop(shop)
    user = shop.user
    impersonate_user(user)
    session[:true_shop_id] = session[:shop_id]
    session[:shop_id] = shop.id
  end

  def stop_impersonate_shop
    stop_impersonating_user
    session[:shop_id] = session[:true_shop_id]
    session[:true_shop_id] = nil
  end

  private

  def set_header_for_iframe
    response.headers.delete "X-Frame-Options"
  end
end

class IntegrationsController < ApplicationController
  include LoginProtection
  include PayPal::SDK::OpenIDConnect

  MISSING_SHOP_PARAMS = "You must enter your shop domain"
  INVALID_STORE_URL = "The store URL is not valid."

  def index
  end

  def sign_in
  end

  def shopify
  end

  def shopify_authorize
    if params[:shop].present?
      if params[:shop].include?(".myshopify.com")
        redirect_to "/auth/shopify?shop=#{params[:shop]}"
      else
        flash[:error] = INVALID_STORE_URL
        redirect_back fallback_location: root_path
        nil
      end
    else
      flash[:error] = MISSING_SHOP_PARAMS
      redirect_back fallback_location: root_path
    end
  end

  def bigcommerce
  end

  def bigcommerce_authorize
    if params.nil? || params[:shop].blank?
      flash[:error] = "You must enter your shop domain"
      redirect_back fallback_location: root_path
    else
      redirect_to auth_bigcommerce_callback_path(params.permit(:shop))
    end
  end

  private

  def resource_name
    :user
  end
  helper_method :resource_name

  def resource
    @resource ||= User.new
  end
  helper_method :resource

  def devise_mapping
    @devise_mapping ||= Devise.mappings[:user]
  end
  helper_method :devise_mapping

  def resource_class
    User
  end
  helper_method :resource_class
end

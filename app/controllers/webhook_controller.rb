class WebhookController < ApplicationController
  skip_before_action :verify_authenticity_token

  def stripe
    event = verified_stripe_event
    head 400 unless event.present?

    if event.present? && event["type"] == "checkout.session.completed"
      checkout_session = event["data"]["object"]
      return false unless checkout_session["payment_status"] == "paid"

      package_name = checkout_session["metadata"]["package"].to_s.downcase
      total_credits = checkout_session["metadata"]["total_credits"].to_i
      store_api_token = checkout_session["metadata"]["api_token"]
      shop = Shop.find_by(api_token: store_api_token)

      if shop
        shop.stripe_customer_id = checkout_session["customer"] if shop.stripe_customer_id.blank?
        shop.stripe_subscription_id = checkout_session["subscription"] if checkout_session["subscription"].present?

        if package_name.present?
          package = Package.new(package_name)
          package.activate(shop, event["id"])

          shop.billings.create(
            charge_id: params[:charge_id],
            shop_name: (shop.provider == "BigCommerce") ? shop.bigcommerce_domain : shop.ecwid_store_url,
            plan_name: package.title,
            total_charge: package.cost,
            status: "success"
          )
        else
          shop.charge_id = event["id"]
          shop.charged_at = DateTime.now
          shop.increment(:credit, total_credits)
          shop.save
        end
      end
    end

    head :ok
  end

  def shop_redact
    begin
      user = Shop.find_by!(shopify_domain: params[:shop_domain])

      info_attrs = {
        email: nil,
        notification_email: nil,
        email_subscriptions: []
      }

      user.update(info_attrs)
      user.save(validate: false)
    rescue ActiveRecord::RecordNotFound
    end
    head :ok and return
  end

  def customer_redact
    params.permit!
    pp params
    head :ok and return
  end

  def customer_data_request
    params.permit!
    pp params
    head :ok and return
  end

  private

  def verified_stripe_event
    endpoint_secret = ENV["STRIPE_SIGNING_SECRET"]
    sig_header = request.env["HTTP_STRIPE_SIGNATURE"]
    payload = request.body.read
    Stripe::Webhook.construct_event(payload, sig_header, endpoint_secret)
  rescue Stripe::SignatureVerificationError, JSON::ParserError
    nil
  end
end

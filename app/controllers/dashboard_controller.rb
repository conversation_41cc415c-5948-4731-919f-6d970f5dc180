class DashboardController < ShopifyApp::AuthenticatedController
  require "action_view"

  include ActionView::Helpers::SanitizeHelper

  before_action :create_shopify_session
  layout "dashboard"

  def index
  end

  def edit_preferences
  end

  def edit_integrations
  end

  def new_source
  end

  def fulfillments
  end

  def paypal
  end

  def settings
    @source = current_shop.sources.find_by_id(params[:id])
  end

  def credit_charge
    credit = params[:credit].to_i
    charge_price = (credit < 10_000) ? credit * Settings.flexi.cost : (0.0039 * credit)
    charge_name = "FulfillSync Credit (#{credit} Credit)"

    callback_params = {
      charge_price: charge_price,
      charge_name: charge_name,
      credit: credit
    }

    charge = ShopifyAPI::ApplicationCharge.new
    charge.name = charge_name
    charge.price = charge_price
    charge.return_url = credit_charge_callback_url(callback_params)
    charge.test = Settings.test_stores.include?(current_shop.shopify_domain) || Rails.env.development?
    charge.save!

    Rails.logger.info("charge credit")
    Rails.logger.info(charge)

    Ga4AnalyticsService.track_credit_purchase(
      user: current_shop,
      transaction_id: charge.id,
      credits: credit,
      price: charge_price
    )

    @redirect_url = if !credit.between?(200, 20_000)
      root_url
    elsif charge.try(:status) == "pending"
      charge.confirmation_url
    else
      root_url
    end
    render "/common/iframe_redirect", format: [:html], layout: false
  end

  def subscribe
    if params[:package] == "trial"
      unsubscribe
      redirect_to :root
      Rails.logger.info("trial")
    else
      charge = nil
      package = Settings.packages[params[:package]]
      if package.present?
        if current_shop.provider == "Shopify"
          charge = ShopifyAPI::RecurringApplicationCharge.new
          charge.name = package[:title]
          charge.price = package[:cost]
          charge.return_url = offsite_payment_callback_url(package: params[:package])
          charge.test = Settings.test_stores.include?(current_shop.shopify_domain) || Rails.env.development?
          charge.save!

          Ga4AnalyticsService.track_plan_purchase(
            user: current_shop,
            transaction_id: charge.id,
            plan: package[:title],
            price: package[:cost],
            event_type: "upgrade"
          )
        else
          charge = Stripe::Checkout::Session.create(
            success_url: "#{Settings.app_domain}?payment_success=true",
            cancel_url: "#{Settings.app_domain}?payment_success=false",
            payment_method_types: ["card"],
            mode: "subscription",
            metadata: {
              platform: current_shop.provider,
              store_id: current_shop.id,
              package: package[:title],
              api_token: current_shop.api_token
            },
            line_items: [{
              price: Settings.stripe.subscription_id,
              quantity: 1
            }]
          )
        end

        Rails.logger.info("charge")
        Rails.logger.info(charge)
      end
      @redirect_url = if ["pending", "open"].include?(charge.try(:status))
        charge.instance_of?(Stripe::Checkout::Session) ? charge.url : charge.confirmation_url
      else
        root_url
      end
      redirect_to @redirect_url
    end
  end

  def subscribe_callback
    if params[:charge_id]
      charge = ShopifyAPI::RecurringApplicationCharge.find(id: params[:charge_id])
      charge.activate if charge.status == "accepted"

      if ["active"].include?(charge.status) && current_shop.present?
        current_shop.charge_id = params[:charge_id]
        current_shop.charged_at = DateTime.now
        package = Settings.packages[params[:package]]
        if package.present?
          current_shop.package = params[:package]
          current_shop.source_limit = package[:limit]
        end
      elsif current_shop.present?
        current_shop.charge_id = nil
      end
      current_shop&.save(validate: false)
    end
    redirect_to root_url
  end

  def offsite_payment_callback
    if params[:charge_id]
      current_shop.use_credit = false
      @notification = OffsitePayments
        .integration(:shopify_payment)
        .notification(request.params,
          shopify_token: current_shop.shopify_token,
          shopify_domain: current_shop.shopify_domain,
          charge_type: "recurring")
      status = @notification.retrieve_payment_status
      package = Package.new(params[:package])

      billing = current_shop.billings.new(
        charge_id: params[:charge_id],
        shop_name: current_shop.shopify_domain,
        plan_name: package.title,
        total_charge: package.cost,
        status: status
      )

      if status == "active"
        package.activate(current_shop, params[:charge_id])
      elsif ["declined", "expired"].include?(status)
        package.deactivate(current_shop)
      end

      billing.save
    end

    redirect_to root_url
  end

  def credit_charge_callback
    if params[:charge_id]
      payment = current_shop.klass_for_payment.new(current_shop)
      payment.process_callback(params)
    end

    redirect_to root_url
  end

  def export_fulfillment_logs
    if current_shop.active?
      month = params["month"]

      month = 6 if month.blank?
      month = month.to_i
      logs = current_shop.fulfillment_logs.where("created_at > ?", month.months.ago).to_csv(platform: current_shop.platform)
      flash[:error] = "Export Successfully."
      respond_to do |format|
        format.csv { send_data logs, filename: "fulfillment_logs.csv" }
      end
    else
      current_shop.deactivate_shop
      error_msg = "Your trial period has ended. Please upgrade to use this feature."
      flash[:error] = error_msg
      respond_to do |format|
        format.csv { send_data "", filename: "fulfillment_logs.csv" }
      end
    end
  end

  def export_sync_logs
    if current_shop.active?
      logs = current_shop.sync_logs.to_csv
      flash[:error] = "Export Successfully."
      respond_to do |format|
        format.csv { send_data logs, filename: "sync_logs.csv" }
      end
    else
      current_shop.deactivate_shop
      error_msg = "Your trial period has ended. Please upgrade to use this feature."
      flash[:error] = error_msg
      respond_to do |format|
        format.csv { send_data "", filename: "sync_logs.csv" }
      end
    end
  end

  private

  def unsubscribe
    shop = current_shop
    shop.use_credit = true
    return false unless shop && shop.charge_id.present?

    if shop.provider == "Shopify"
      charge = shop.get_billing(shop.charge_id)
      charge.destroy if charge.try(:status).present?
    elsif shop.stripe_subscription_id.present?
      Stripe::Subscription.delete(shop.stripe_subscription_id)
    end
    shop.reset_payment
  rescue ActiveResource::ResourceNotFound => e
    Rails.logger.error("ShopID: #{shop.id} #{e.message}")
    Rails.logger.error(e.backtrace.join("\n"))
    Airbrake.notify(e)
  end

  def create_shopify_session
    unless current_shop
      sanitized_shop_params = sanitize(params["shop"])
      if params["shop"].present? && sanitized_shop_params == params["shop"] && params["shop"].include?(".myshopify.com")
        redirect_to "/auth/shopify?shop=#{params["shop"]}"
      else
        redirect_to signin_url
      end
    end
    if user_signed_in?
      if current_shop.provider == "Shopify"
        current_shop.create_session
        info = current_shop.get_shop_information
        current_shop.primary_location_id = info.try(:primary_location_id)
        current_shop.save
      end
      cookies[:uptracker_current_shop] = current_shop.id
    end
  rescue ActiveResource::UnauthorizedAccess
    shopify_domain = current_shop.shopify_domain
    if shopify_domain && shop_signed_in?
      sign_out(current_shop)
      redirect_to "/auth/shopify?shop=#{shopify_domain}"
    else
      redirect_to signin_url
    end
  end
end

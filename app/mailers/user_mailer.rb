class UserMailer < ApplicationMailer
  layout "mailer"

  def notify_finish_process(email_receiver, result)
    @result = result
    mail to: tokenized_emails(email_receiver)
  end

  def notify_sync_success(email_receiver, result, source)
    @result = result
    @receiver = email_receiver
    @source = source
    mail to: tokenized_emails(email_receiver)
  end

  def notify_sync_failure(email_receiver, result, source)
    @result = result
    @receiver = email_receiver
    @source = source
    mail to: tokenized_emails(email_receiver)
  end

  def billing_revoke(store)
    @store = store
    mail to: tokenized_emails(store)
  end

  def notify_new_uptracker(email)
    mail to: email, subject: "A great way to fulfill orders with FulfillSync"
  end

  def notify_out_of_credit(email_receiver, source)
    @receiver = email_receiver
    source.sync_status = "pause"
    source.save
    mail to: tokenized_emails(email_receiver)
  end

  def notify_low_credit(email_receiver)
    @receiver = email_receiver
    mail to: tokenized_emails(email_receiver), subject: "Your FulfillSync Credit is running low"
  end

  def notify_api_change(email_receiver)
    @receiver = email_receiver
    mail to: tokenized_emails(email_receiver), subject: "Changes to FulfillSync Access"
  end

  def uninstall_email(email_receiver)
    @store_name = email_receiver.shopify_domain
    @receiver = email_receiver
    mail to: tokenized_emails(email_receiver), subject: "FulfillSync - We're sorry to see you go"
  end

  def welcome_email(email_receiver, store_name, store_provider)
    @store_name = store_name
    @store_provider = store_provider
    mail to: email_receiver, subject: "Welcome to FulfillSync"
  end
end

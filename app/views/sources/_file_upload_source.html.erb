<tr id="<%= source.shop_id %>_<%= source.id %>">
  <td>
    <%= source.name %> <br>
    <span class="processed-at"><%= last_processed_time(source.last_processing_time) %></span>
  </td>
  <td>N/A</td>
  <td>N/A</td>
  <td class="option">
    <%= link_to '<i class="fa fa-pencil-square-o"></i> Settings'.html_safe, edit_source_url(source), class: "btn btn-primary btn-sm" %>
    <% if source.status == "queuing" %>
      <%= button_tag class: "btn btn-sm btn-danger sync-now-button status-queuing disabled", data: { toggle: "modal", target:".file-upload-execution-modal-#{source.id}" } do %>
        <i class="fa fa-refresh fa-spin"></i> Queuing
      <% end %>
    <% elsif source.status == "running" %>
      <%= button_tag class: "btn btn-sm btn-warning sync-now-button status-running disabled", data: { toggle: "modal", target:".file-upload-execution-modal-#{source.id}" } do %>
        <i class="fa fa-refresh fa-spin"></i> Running
      <% end %>
    <% else %>
      <%= button_tag class: "btn btn-sm btn-danger sync-now-button", data: { toggle: "modal", target:".file-upload-execution-modal-#{source.id}" } do %>
        <i class="fa fa-play"></i> Sync Now
      <% end %>
    <% end %>
    <% if source.source_file.present? %>
      <button class="btn btn-sm btn-info test-column-mapping-btn" data-url="<%= test_column_mapping_source_url(source) %>"><i class="fa fa-plug"></i> Test Column Mapping</button>
    <% end %>
  </td>
</tr>

<div class="modal execution-modal file-upload-execution-modal-<%= source.id %>">
  <div class="modal-dialog">
    <%= form_for source, url: upload_file_and_sync_source_url(source), method: :post do |f| %>
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
          <h4 class="modal-title">Upload File to Sync <%= source.name %></h4>
        </div>
        <div class="modal-body">
          <p>Upload your file and sync now! This process may take some time to finish.</p>
          <div class="form-group">
            <%= f.file_field :source_file, class: "form-control" %>
          </div>
        </div>
        <div class="modal-footer">
          <div class="pull-left text-left" style="width: 40%;">
          <%= f.submit "Upload & Update any orders", :onclick => "return confirm('This process will take longer time to finish. Are you sure to continue?')", class: "btn pull-left" %>
            <div><small>Note: "Upload & Update any orders" doesn't support partial fulfillments. Please use it for correcting fulfilled orders only.</small></div>
          </div>
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          <%= f.submit "Update unfulfilled orders only", class: "btn btn-primary", data: { disable_with: "Please Wait..".html_safe } %>
        </div>
      </div>
    <% end %>
  </div>
</div>
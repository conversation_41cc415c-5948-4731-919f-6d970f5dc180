<% if fulfillment_logs.present? %>
  <h4>
    Fulfillment Logs
    <%= link_to("Export to CSV", export_fulfillment_logs_url(format: :csv) , class: "btn btn-primary pull-right") %>
  </h4>
  <table class="table table-hover">
    <thead>
      <th>Shopify Order</th>
      <th>Item SKU</th>
      <th>Quantity</th>
      <th>Tracking Number</th>
      <th>Tracking Company</th>
      <th>Successfully Fulfilled</th>
      <th>Remarks</th>
      <th>Fulfilled At</th>
    </thead>
    <tbody>
      <% fulfillment_logs.each do |log| %>
        <tr>
          <td><%= log.order_number %></td>
          <td><%= log.sku %></td>
          <td><%= log.quantity %></td>
          <td><%= log.tracking_no %></td>
          <td><%= truncate(log.tracking_company, length: 20) %></td>
          <td><%= log.fulfillment_status %></td>
          <td><%= log.error_message %></td>
          <td><%= time_ago_in_words(log.created_at) %> ago</td>
        </tr>
      <% end %>
    </tbody>
  </table>
  <%= paginate fulfillment_logs %>
<% end %>
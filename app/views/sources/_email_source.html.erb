<tr id="<%= source.shop_id %>_<%= source.id %>">
  <td>
    <%= source.name %> <br>
    <span class="processed-at"><%= last_processed_time(source.last_processing_time) %></span>    
  </td>
  <td>N/A</td>
  <td>
    N/A
  </td>
  <td class="option">
    <%= link_to '<i class="fa fa-pencil-square-o"></i> Settings'.html_safe,  edit_source_url(source), class: "btn btn-primary btn-sm" %>
    <% if source.status == "queuing" %>
      <%= link_to '<i class="fa fa-refresh fa-spin"></i> Queuing'.html_safe,  sync_fulfillments_source_url(source), class: "btn btn-sm btn-danger sync-now-button status-queuing disabled", data: {confirm: "Confirm to sync now?"} %>
    <% elsif source.status == "running" %>
      <%= link_to '<i class="fa fa-refresh fa-spin"></i> Running'.html_safe,  sync_fulfillments_source_url(source), class: "btn btn-sm btn-warning sync-now-button status-running disabled", data: {confirm: "Confirm to sync now?"} %>
    <% else %>
      <%= link_to '<i class="fa fa-play"></i> Sync Now'.html_safe,  sync_fulfillments_source_url(source), class: "btn btn-sm btn-danger sync-now-button", data: {confirm: "Confirm to sync now?"} %>
    <% end %>
    <button class="btn btn-sm btn-info test-column-mapping-btn" data-url="<%= test_column_mapping_source_url(source) %>"><i class="fa fa-plug"></i> Test Column Mapping</button>
  </td>
</tr>
<% unless @result[:data].present? %>
	<div class="text-center">
		<p><i class="fa fa-exclamation-triangle fa-5x"></i></p>
		<p>Unable to map columns. Please check your source settings.</p>
		<div class="alert alert-warning" role="alert">
		  Error: <%= @result[:remark] %>
		</div>
	</div>
<% else %>
	<div class="text-center">
		<div class="alert alert-info"> 
			If column names are not matching, please check your column mapping settings. The columns have to be matched so that FulfillSync can process your file properly. :)
		</div>
	</div>

	<table class="table table-bordered">
		<thead>
			<th>Column</th>
			<th>Mapped Column Name/Value</th>
		</thead>
		<tbody>
			<% @result[:data].each do |key, value| %>
			<tr>
				<td>
					<%= key %>
				</td>
				<td>
					<%= value %>
				</td>
			</tr>
			<% end %>
		</tbody>
	</table>

	<div class="text-center">
		<div class="alert alert-info"> 
			This table shows the format of your order column from your file and the <strong>latest Shopify order</strong> from your shop. Please adjust your "Recognize my order by" field if the <strong>format of the highlighted area</strong> is not matching.
		</div>
	</div>

	<table class="sample-order-mapping table table-bordered text-center">
		<thead>
			<th>Source</th>
			<th colspan=2>Latest Shopify Order</th>
		</thead>		
		<thead>
			<th>Order #</th>
			<th>Order Number</th>
			<th>Order Name</th>
		</thead>
		<tbody>
			<tr>
				<td class="info"><%= @result[:data]["Order #"] %></td>
				<td class="<%= active_selected_order_key('order_number', @source.order_key) %>"><%= @result[:sample_shopify_order][:order_number] %></td>
				<td class="<%= active_selected_order_key('name', @source.order_key) %>"><%= @result[:sample_shopify_order][:order_name] %></td>
			</tr>
		</tbody>
	</table>
	Note: The table above shows the <strong>latest Shopify order</strong>, which may not be the same as the first order in your file. Please check the <strong>format rather than the value</strong>.
<% end %>
<% if @source.new_record? %>
  <div class="alert alert-info">
    Please get the source connection settings info from your Vendor, Supplier or Dropshipper.
  </div>
<% end %>
<%= form_for(@source, html: {class: "form-horizontal edit-source-form", :autocomplete => "off"}) do |f| %>

<div class="row">

  <!-- General Information and Scheduler -->
  <div class="col-md-offset-2 col-md-8">
    <div class="form-group required">
      <%= f.label :name, class: "col-sm-4 control-label" %>
      <div class="col-sm-8">
        <%= f.text_field :name, class: "form-control" %>
      </div>
    </div>

    <div class="panel panel-default">
      <div class="panel-heading">
        How are the orders being mapped?
      </div>

      <div class="panel-body">
        <%= render :partial => "sources/form/order_mapping_settings", locals: {f: f} %>
      </div>
    </div>

    <div class="panel panel-default">
      <div class="panel-heading">
        Source Connection Settings <%= link_to("(Click here to download a sample CSV file!)", "/sample.csv") %>
      </div>

      <div class="panel-body">
        <%= render :partial => "sources/form/connection_settings", locals: {f: f} %>
      </div>
    </div>

    <div class="panel panel-default settings-panel" data-hide-from-source-type="file_upload, email">
      <div class="panel-heading">
        How frequent should we update fulfillments from your source?
      </div>
      <div class="panel-body">
        <%= render :partial => "sources/form/schedule_settings", locals: {f: f} %>
      </div>
    </div>

    <div class="panel panel-default">
      <div class="panel-heading">
        Mapping Source File Columns / Index
      </div>

      <div class="panel-body">
        <%= render :partial => "sources/form/source_mapping_settings", locals: {f: f} %>
      </div>
    </div>

    <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
      <div class="panel panel-default">
        <div class="panel-heading">
          <a class="collapsed" role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
            4. Advanced Settings (optional)
          </a>
        </div>

        <div id="collapseThree" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingThree">
          <div class="panel-body">
            <%= render :partial => "sources/form/other_settings", locals: {f: f} %>
          </div>
        </div>
      </div>

      
    </div>

    <div class="form-group">
      <div class="col-sm-12">
        <div class="pull-right">
          <%= f.submit class: "btn btn-primary" %>
          <%= link_to "Cancel", root_url, class: "btn btn-default" %>
        </div>
      </div>
    </div>

  </div>
  <!-- End Schedule and Column Mapping Section -->

</div>
<% end %>

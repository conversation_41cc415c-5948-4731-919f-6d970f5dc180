<% if sync_logs.present? %>
  <h4>
    Sync Logs
    <%= link_to("Export to CSV", export_sync_logs_url(format: :csv) , class: "btn btn-primary pull-right") %>
  </h4>
  <table class="table table-hover">
    <thead>
      <th>Status</th>
      <th style="width: 130px;">Processed At</th>
      <th>Remark</th>
    </thead>
    <tbody>
      <% sync_logs.each do |log| %>
        <tr>
          <td><%= log.status.humanize %></td>
          <td><%= time_ago_in_words(log.processed_at) %></td>
          <td>
            <% if log.error_message and log.error_message.length > 60 %>
              <div tooltip="<%= log.error_message %>"><%= truncate(log.error_message, length: 60) %></div>
            <% else %>
              <%= log.error_message %>
            <% end %>

          </td>
        </tr>
      <% end %>
    </tbody>
  </table>
  <%= paginate sync_logs %>
<% end %>

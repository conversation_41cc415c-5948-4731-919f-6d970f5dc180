<div class="form-group ignore-optional">
  <%= f.label :order_identifier_constants, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :order_identifier_constants, class: "form-control", placeholder: "PRE-" %>
    <p class="help-block">The prefix or suffix of the order number from feed that needs to be ignored while finding your Shopify orders.</p>
  </div>
</div>

<div class="form-group ignore-optional">
  <%= f.label :shopify_order_key_constants, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :shopify_order_key_constants, class: "form-control", placeholder: "" %>
    <p class="help-block">The prefix or suffix of the order number on Shopify store that needs to be ignored while matching orders.</p>
  </div>
</div>


<div class="form-group ignore-optional">
  <%= f.label :ignore_key, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :ignore_key, class: "form-control", placeholder: 6 %>
  </div>
</div>

<div class="form-group ignore-optional">
  <%= f.label :ignore_value, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :ignore_value, class: "form-control", placeholder: "not_dispatched" %>
    <p class="help-block">The filtered rows will be ignored from the source file while updating fulfillment. Leave these two fields blank to sync all rows in your file. Only one value can be filtered for now.</p>
  </div>
</div>

<div class="form-group ignore-optional">
  <%= f.label :ignore_empty_sku, class: "col-sm-4 control-label" %>
  <div class="col-sm-8" style="padding-top: 7px;">
    <%= f.check_box :ignore_empty_sku %>
    <p class="help-block">If it's left unchecked and SKU is empty or not mapped, all order items will be fulfilled.</p>
  </div>
</div>

<div class="form-group ignore-optional">
  <%= f.label :allow_blank_tracking_no, class: "col-sm-4 control-label" %>
  <div class="col-sm-8" style="padding-top: 7px;">
    <%= f.check_box :allow_blank_tracking_no %>
    <p class="help-block">If checked, it will fulfill orders even the tracking number is empty.</p>
  </div>
</div>

<div class="form-group ignore-optional">
  <%= f.label :column_separator, class: "col-sm-4 control-label" %>
  <div class="col-sm-8" style="padding-top: 7px;">
    <%= f.select :column_separator, Settings.column_separators.map{|k,v| [v,k]}, {selected: f.object.column_separator || ","}, class: "form-control" %>
  </div>
</div>

<div class="form-group ignore-optional">
  <%= f.label :tracking_company_default, class: "col-sm-4 control-label" %>
  <div class="col-sm-8" style="padding-top: 7px;">
    <%= f.text_field :tracking_company_default, class: "form-control" %>
  </div>
</div>


<div class="form-group ignore-optional">
  <%= f.label :notify_customer, class: "col-sm-4 control-label" %>
  <div class="col-sm-8" style="padding-top: 7px;">
    <%= f.check_box :notify_customer %>
    <p class="help-block">Send shipping confirmation email to customers upon each fulfillment.</p>
  </div>
</div>

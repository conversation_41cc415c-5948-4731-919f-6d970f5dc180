<div class="alert alert-info alert-dismissible" role="alert">
  <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
  First column starts from 0.
</div>
<div class="form-group required">
  <%= f.label :order_no_mapping, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :order_no_mapping, class: "form-control", placeholder: 0 %>
  </div>
</div>

<div class="form-group">
  <%= f.label :sku_mapping, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :sku_mapping, class: "form-control", placeholder: 1 %>
  </div>
</div>

<div class="form-group">
  <%= f.label :quantity_mapping, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :quantity_mapping, class: "form-control", placeholder: 2 %>
  </div>
</div>

<div class="form-group">
  <%= f.label :tracking_no_mapping, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :tracking_no_mapping, class: "form-control", placeholder: 3 %>
  </div>
</div>

<div class="form-group">
  <%= f.label :tracking_company_mapping, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :tracking_company_mapping, class: "form-control", placeholder: 4 %>
  </div>
</div>

<div class="form-group">
  <%= f.label :tracking_url_mapping, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :tracking_url_mapping, class: "form-control", placeholder: 5 %>
  </div>
</div>
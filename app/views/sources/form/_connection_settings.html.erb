<div class="form-group ignore-optional">
  <%= f.label :source_type, class: "col-sm-4 control-label source_type" %>
  <div class="col-sm-8">
    <%= f.select :source_type, Settings.source_types.map{|k,v| [v,k]}, {}, class: "form-control" %>
  </div>
</div>

<div class="form-group source-type-settings required" data-hide-from-source-type="ftp, sftp, ftps, file_upload, email">
  <%= f.label :source_url, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :source_url, class: "form-control", placeholder: "http://example.com/your_path/tracking.csv" %>
  </div>
</div>

<div class="form-group source-type-settings required" data-hide-from-source-type="url, file_upload, email">
  <%= f.label :source_host, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :source_host, class: "form-control" %>
  </div>
</div>

<!-- <div class="form-group source-type-settings" data-hide-from-source-type="url, file_upload, email">
  <%= f.label :source_parent_path, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :source_parent_path, class: "form-control" %>
    <p class="help-block">Leave it blank if it's in the root path.</p>
  </div>
</div> -->

<div class="form-group source-type-settings required" data-hide-from-source-type="url, file_upload, email">
  <%= f.label :path_to_file, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :path_to_file, class: "form-control", placeholder: "yourfilename.csv", autocomplete: "off" %>
    <p class="help-block">For file name with timestamp, please click <a href="#" data-toggle="modal" data-target="#file_name">here</a> for more info.</p>    
  </div>
</div>

<div id="file_name" class="modal fade" role="dialog">
  <div class="modal-dialog">

    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">File name with timestamp</h4>
      </div>
      <div class="modal-body">
        <p>Looking to match this file name /orders/fulfillment_2017_05_09_14_30.csv <br>
          eg. /orders/fulfillment_%Y_%m_%d_*.csv </p>
        <p>
          <table class="table table-condensed">
            <tr>
              <th>Symbol</th>
              <th>Description</th>
              <th>Example</th>
            </tr>
            <tr>
              <th colspan=3>Today Timestamp</th>
            </tr>            
            <tr>
              <td>%b</td>
              <td>The abbreviated month name</td>
              <td>Jan</td>
            </tr>
            <tr>
              <td>%^b</td>
              <td>The abbreviated month name uppercased</td>
              <td>JAN</td>
            </tr>            
            <tr>
              <td>%d</td>
              <td>Day of the month, zero-padded</td>
              <td>01..31</td>
            </tr>
            <tr>
              <td>%Y</td>
              <td>Year with century</td>
              <td>1995, 2009, 2017</td>
            </tr>
            <tr>
              <td>%y</td>
              <td>Year last 2 digits</td>
              <td>00..99</td>
            </tr>
            <tr>
              <td>%m</td>
              <td>Month of the year, zero-padded</td>
              <td>01..12</td>
            </tr>
            <tr>
              <th colspan=3>Yesterday Timestamp</th>
            </tr>
            <tr>
              <td>%{b-}</td>
              <td>The abbreviated month name</td>
              <td>Jan</td>
            </tr>
            <tr>
              <td>%{^b-}</td>
              <td>The abbreviated month name uppercased</td>
              <td>JAN</td>
            </tr>
            <tr>
              <td>%{d-}</td>
              <td>Day of the month, zero-padded</td>
              <td>01..31</td>
            </tr>
            <tr>
              <td>%{Y-}</td>
              <td>Year with century</td>
              <td>1995, 2009, 2017</td>
            </tr>
            <tr>
              <td>%{y-}</td>
              <td>Year last 2 digits</td>
              <td>00..99</td>
            </tr>
            <tr>
              <td>%{m-}</td>
              <td>Month of the year, zero-padded</td>
              <td>01..12</td>
            </tr>            
          </table>
        </p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>

  </div>
</div>

<div class="form-group source-type-settings ignore-optional" id="source-email-field" data-hide-from-source-type="ftp, ftps, url, sftp,file_upload">
  <%= f.label :source_email, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :email, class: "form-control", readonly: true %>
    <p class="help-block">Please send an email with attachment of your orders, the fullfilment process will run everytime you send an email to the above address.</p>    
  </div>
</div>

<!-- 
<div class="form-group">
  <div class="col-sm-8 col-sm-offset-4">
    <%= f.check_box :has_header %> The file above has a header row
    <p class="help-block">Please ensure your file is available if you wish to test the column mapping after save.</p>
  </div>
</div> -->

<div class="form-group source-type-settings required" data-hide-from-source-type="url, file_upload, email">
  <%= f.label :source_login, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :source_login, class: "form-control" %>
  </div>
</div>

<div class="form-group source-type-settings required" data-hide-from-source-type="url, file_upload, , email">
  <%= f.label :source_password, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.password_field :source_password, value: f.object.source_password, class: "form-control" %>
    <p class="help-block">Optional if it's SFTP.</p>
  </div>
</div>

<div class="form-group source-type-settings source-ssh-key" data-hide-from-source-type="ftp,url,ftps, file_upload, email">
  <%= f.label :ssh_key, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_area :ssh_key, class: "form-control" %>
  </div>
</div>

<div class="form-group source-type-settings ignore-optional" data-hide-from-source-type="url, file_upload, email" >
  <%= f.label :source_process, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.select :source_process, Settings.source_process.map{|k,v| [v,k]}, {}, class: "form-control" %>
  </div>
</div>

<div class="form-group source-type-settings" id="source-rename-field" data-hide-from-source-type="url, file_upload, email">
  <%= f.label :source_rename, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :source_rename, class: "form-control" %>
  </div>
</div>



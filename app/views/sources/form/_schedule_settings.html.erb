<div class="form-group ignore-optional">
  <%= f.label :schedule_type, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.select :schedule_type, Settings.job_type.map{|k,v| [v,k]}, {}, class: "form-control" %>
  </div>
</div>

<div class="form-group required" id="schedule-time" <% if @source.schedule_type == "none" %>style="display:none;"<% end %>>
  <%= f.label :schedule_time, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :schedule_time, class: "form-control timepicker", placeholder: 6 %>
  </div>
</div>

<div class="form-group required" id="schedule-interval" <% if @source.schedule_type == "none" %>style="display:none;"<% end %>>
  <%= f.label :schedule_interval, class: "col-sm-4 control-label" %>
  <div class="col-sm-8">
    <%= f.text_field :schedule_interval, class: "form-control", placeholder: 6 %>
  </div>
</div>
<script type="text/javascript">
  window.mainPageTitle = "<%= title %>";
  ShopifyApp.ready(function(){
    ShopifyApp.Bar.initialize({
      title: window.mainPageTitle,
      icon: '/fs_favicon.svg',
      // buttons: {
      //     primary: 
      //     {
      //       label: "FAQ",
      //       href: "<%= faq_url %>",
      //       target: "new"
      //     },
      //     secondary: [
      //     {
      //       <% if current_shop.should_upgrade? %>
      //         label: "Upgrade",
      //         type: "dropdown",
      //         links: [
      //            { label: "Basic - $5 per month", href: "/upgrade?package=basic", target: "app" }
      //         ]
      //       <% else %>
      //         label: "Upgraded",
      //         style: 'disabled',
      //         callback: function(e){e.preventDefault(); }
      //       <% end %>
      //     },
      //     {
      //       label: "Edit Preferences",
      //       href: "<%= preferences_url %>"
      //     }
      //     ], 
      //}
    });

    <%if flash[:notice] %>
      ShopifyApp.flashNotice("<%== j flash[:notice] %>")
    <% end %>

    <% if flash[:error] %>
      ShopifyApp.flashError("<%== j flash[:error] %>")
    <% end %>
    
  });
</script>
<div id="btn-help">
  <a href="mailto:<EMAIL>" target="_top"><span class="glyphicon glyphicon-question-sign"></span></a>
</div>
<nav class="navbar navbar-default">
  <div class="container-fluid">
    <div class="navbar-header">
      <a class="navbar-brand" href="<%= root_url %>">Dashboard</a>
    </div>    
    <ul class="nav   navbar-nav navbar-right">
      <li><a href="<%= faq_url %>">FAQ</a></li>
      <li><a href="<%= preferences_url %>">Edit Preferences</a></li>
      <li class="dropdown">
        <% if current_shop.should_upgrade? %>
          <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Upgrade <span class="caret"></span></a>
          <ul class="dropdown-menu">
            <li><a href="/upgrade?package=basic" target="app">Basic - $5 per month</a></li>
            <li><a href="/upgrade?package=pro" target="app">Pro - $10 per month<br><small>Allow max 3 sources</small></a></li>
          </ul>
        <% else %>
          <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Upgraded<span class="caret"></span></a>
          <ul class="dropdown-menu">
            <% if current_shop.package == "basic" %>
              <li><a href="#" >Basic - (Your plan)</a></li>
            <% else %>
              <li><a href="/upgrade?package=basic" target="app">Basic - $5 per month</a></li>
            <% end %>
            <% if current_shop.package == "pro" %>
              <li><a href="#">Pro - (Your plan)</a></li>
            <% else %>
              <li><a href="/upgrade?package=pro" target="app">Pro - $10 per month<br><small>Allow max 3 sources</small></a></li>
            <% end %>
          </ul>

        <% end %>          

      </li>
    </ul>    
  </div>
</nav>


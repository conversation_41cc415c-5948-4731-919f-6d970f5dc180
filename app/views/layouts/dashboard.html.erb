<!DOCTYPE html>
<html lang="en">
  <head>
    <% application_name = ShopifyApp.configuration.application_name %>
    <title><%= application_name %></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <%= render partial:"common/fav_icon"%>
    <%= stylesheet_link_tag 'dashboard' %>

    <script src="https://djqizrxa6f10j.cloudfront.net/ecwid-sdk/js/1.2.4/ecwid-app.js"></script>
    <%= csrf_meta_tags %>

    <% if Rails.env.production? %>
      <!-- Google Tag Manager -->
      <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-MKMVHHM');
      </script>
      <!-- End Google Tag Manager -->
    <% end %>
  </head>

  <body>
    <% if Rails.env.production? %>
      <!-- Google Tag Manager (noscript) -->
      <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MKMVHHM"
      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
      <!-- End Google Tag Manager (noscript) -->
    <% end %>

    <script>
      window.fwSettings={
        'widget_id':44000003474
      };
      !function(){if("function"!=typeof window.FreshworksWidget){var n=function(){n.q.push(arguments)};n.q=[],window.FreshworksWidget=n}}()
    </script>
    <script type='text/javascript' src='https://widget.freshworks.com/widgets/44000003474.js' async defer></script>

    <script>
      FreshworksWidget('prefill', 'ticketForm', {
        subject: '<%= current_shop.shopify_domain %>',
        email: '<%= current_shop.email %>',
      });
    </script>

    <%= yield %>

    <script>
      EcwidApp.init({
      app_id: 'stock-sync-dev', // use your application namespace
      autoloadedflag: true,
      autoheight: true
      });
    </script>

    <script>
      var shop_id = "<%= current_shop.id %>"
    </script>

  </body>
</html>

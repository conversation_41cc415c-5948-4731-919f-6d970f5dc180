<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
  <head>
    <title></title>
    <!--[if !mso]><!-- -->  
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!--<![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style type="text/css">  #outlook a { padding: 0; }  .ReadMsgBody { width: 100%; }  .ExternalClass { width: 100%; }  .ExternalClass * { line-height:100%; }  body { margin: 0; padding: 0; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }  table, td { border-collapse:collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; }  img { border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; }  p { display: block; margin: 13px 0; }</style>
    <!--[if !mso]><!-->
    <style type="text/css">  @media only screen and (max-width:480px) {    @-ms-viewport { width:320px; }    @viewport { width:320px; }  }</style>
    <!--<![endif]--><!--[if mso]>
    <xml>
      <o:OfficeDocumentSettings>
        <o:AllowPNG/>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
    <![endif]--><!--[if lte mso 11]>
    <style type="text/css">  .outlook-group-fix {    width:100% !important;  }</style>
    <![endif]--><!--[if !mso]><!-->    
    <link href="https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700" rel="stylesheet" type="text/css">
    <style type="text/css">        @import url(https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700);    </style>
    <!--<![endif]-->
    <style type="text/css">  @media only screen and (min-width:480px) {    .mj-column-per-100 { width:100%!important; }  }.get-started-button {background: linear-gradient(180deg,#6371c7,#5563c1); color: white; padding: 15px 32px; text-align: center; text-decoration: none; display: inline-block; font-size: 16px; margin: 4px 2px; outline: none;} .get-started-button:hover{color: #fff;
    background-: linear-gradient(180deg,#5c6ac4,#4959bd);
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(.64,0,.35,1);}</style>

  </head>
  <body style="background: #FFFFFF; font-family:Open Sans, sans-serif">
    <div class="mj-container" style="background-color:#FFFFFF;">
      <!--[if mso | IE]>      
      <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="600" align="center" style="width:600px;">
        <tr>
          <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
            <![endif]-->
            <div style="margin:0px auto;max-width:600px;">
              <table role="presentation" cellpadding="0" cellspacing="0" style="font-size:0px;width:100%;" align="center" border="0">
                <tbody>
                  <tr>
                    <td style="text-align:center;vertical-align:top;direction:ltr;font-size:0px;padding:9px 0px 9px 0px;">
                      <!--[if mso | IE]>      
                      <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td style="vertical-align:top;width:600px;">
                            <![endif]-->
                            <div class="mj-column-per-100 outlook-group-fix" style="vertical-align:top;display:inline-block;direction:ltr;font-size:16px;text-align:left;width:100%;">
                              <table role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0">
                                <tbody>
                                  <tr>
                                    <td style="word-wrap:break-word;font-size:0px;padding:0px 0px 0px 0px;" align="center">
                                      <table role="presentation" cellpadding="0" cellspacing="0" style="border-collapse:collapse;border-spacing:0px;" align="center" border="0">
                                        <tbody>
                                          <tr>
                                            <td style="width:250px;">
                                              <img src="https://app.fulfillsync.com/FS_logo.png" width="250" height="auto"  style="border:none;border-radius:0px;display:block;font-size:16px;outline:none;text-decoration:none;width:100%;height:auto;"></td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td style="word-wrap:break-word;font-size:0px;padding:0px 20px 0px 20px;" align="left">
                                      <div style="cursor:auto;color:#000000;font-size:16px;line-height:22px;text-align:left;">
                                        <%= yield %>
                                      </div>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td style="word-wrap:break-word;font-size:0px;padding:0px 20px 0px 20px;" align="left">
                                      <div style="cursor:auto;color:#000000;font-size:16px;line-height:22px;text-align:left;">
                                        <p>Happy<b> </b>Fulfilling!<br></p>
                                        The FulfillSync Team</p>
                                      </div>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td style="word-wrap:break-word;font-size:0px;padding:10px 25px;padding-top:10px;padding-right:10px;">
                                      <p style="font-size:1px;margin:0px auto;border-top:1px solid #000;width:100%;"></p>
                                      <!--[if mso | IE]>
                                      <table role="presentation" align="center" border="0" cellpadding="0" cellspacing="0" style="font-size:1px;margin:0px auto;border-top:1px solid #000;width:100%;" width="600">
                                        <tr>
                                          <td style="height:0;line-height:0;"> </td>
                                        </tr>
                                      </table>
                                      <![endif]-->
                                    </td>
                                  </tr>
                                  <tr>
                                    <td style="word-wrap:break-word;font-size:0px;padding:0px 20px 0px 20px;" align="center">
                                      <div style="cursor:auto;color:#000000;font-size:13px;line-height:22px;text-align:center;">
                                        <p><span style="font-size:12px;">Copyright &#xA9;&#xA0;<%= DateTime.now.year %> FulfillSync. All rights reserved.</span></p>
                                      </div>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                            <!--[if mso | IE]>      
                          </td>
                        </tr>
                      </table>
                      <![endif]-->
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <!--[if mso | IE]>      
          </td>
        </tr>
      </table>
      <![endif]-->
    </div>
  </body>
</html>
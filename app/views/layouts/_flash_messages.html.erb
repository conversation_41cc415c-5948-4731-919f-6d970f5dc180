<% content_for :javascript do %>
<script type="text/javascript">
  var eventName = typeof(Turbolinks) !== 'undefined' ? 'turbolinks:load' : 'DOMContentLoaded';

  document.addEventListener(eventName, function flash() {
    <% if flash[:notice] %>
      ShopifyApp.flashNotice("<%= j flash[:notice].html_safe %>");
    <% end %>

    <% if flash[:error] %>
      ShopifyApp.flashError("<%= j flash[:error].html_safe %>");
    <% end %>
                             
    document.removeEventListener(eventName, flash)
  });
</script>
<% end %>

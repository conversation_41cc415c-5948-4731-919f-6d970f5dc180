<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <% application_name = ShopifyApp.configuration.application_name %>
    <title><%= application_name %></title>
    <%= stylesheet_link_tag 'application' %>
    <%= javascript_include_tag 'application', "data-turbolinks-track" => true %>
    <%= csrf_meta_tags %>

    <script src="https://cdn.shopify.com/s/assets/external/app.js?<%= Time.now.strftime('%Y%m%d%H') %>"></script>

    <script type="text/javascript">
      ShopifyApp.init({
        apiKey: "<%= ShopifyApp.configuration.api_key %>",
        shopOrigin: "<%= "https://#{ @shop_session.domain }" if @shop_session %>",
        debug: <%= Rails.env.development? ? 'true' : 'false' %>,
        forceRedirect: true
      });
    </script>
  </head>

  <body>
    <div class="app-wrapper">
      <div class="app-content main-container">
        <main role="main">
          <%= yield %>
        </main>
      </div>
    </div>

    <%= render 'layouts/flash_messages' %>

    <% if content_for?(:javascript) %>
      <div id="ContentForJavascript" data-turbolinks-temporary>
        <%= yield :javascript %>
      </div>
    <% end %>
  </body>
</html>
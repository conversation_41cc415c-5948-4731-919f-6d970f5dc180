<!DOCTYPE html>
<html>
  <head>
    <title>Login</title>
    <link href='//fonts.googleapis.com/css?family=Dosis:' rel='stylesheet' type='text/css'>
    <%= render partial:"common/fav_icon"%>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <style>
    .container { text-align:center;padding:20px; }
    .navbar .brand { float:none;}
    .marketing-button{ width: 50%;background-color: #1fb8ed;cursor: pointer;color: white;font-size: 12px;padding: 9px 34px;font-weight: 600;line-height: inherit;letter-spacing: 1.7px;text-transform: uppercase;border-radius: 2px;border-color: #ebebeb;outline: none; transition: 0.15s linear;}
    body{font-family: "Open Sans",sans-serif;color: #535353;font-weight: 300;font-size: 0.9375rem;line-height: 1.9;background-color: #ffffff;}
    h3{font-family:"Dosis","Open Sans",sans-serif;color: #37404d;letter-spacing: 1px;line-height: 1.6;font-size: 28px;font-weight: 600;margin-bottom: 8px;}
    .section-header hr{width: 50px;margin-bottom: 1.5rem;border-color: rgba(83,83,83,0.07);}
    small{color: #b5b9bf;}
    .lead{font-size: 15px;margin-bottom: 16px;}
    img{max-width: 300px;width: 100%}
    .marketing-input{line-height: 32px;font-size: 14px;padding: 7px 20px;width: 50%;    border-color: #70cef8;color: #535353;background-clip: padding-box;border: 1px solid rgba(0,0,0,.15); }
    .marketing-input:focus{background-color: #fff;border-color: #5cb3fd;outline: 0;}
    @media only screen and (max-width: 500px) {
      .marketing-input {
          width: 100%;
        }
        .marketing-button {
            width: 100%;
        }
    }
    .logo {
      width: 150px;
    }
    </style>
  </head>

  <body>
    <div class="container">
      <header class="section-header">
      <%= render partial:"common/fs_logo"%>
        <h3>More than <strong>30 million orders</strong> has been fulfilled</h3>
        <img src="<%= image_path 'shopify_logo.png' %>" alt="" class="logo">
        <p class="lead">Enter your shop domain to log in or install this app.</p>
      </header>

      <%= form_tag shopify_authorize_integrations_path, method: :post do %>
        <% if flash[:error] %>
          <div class=error><%= flash[:error] %></div>
        <% end %>
        <div>
          <input id="shop" name="shop" type="text" autofocus="autofocus" placeholder="example.myshopify.com" class="marketing-input" oninput="return autoLowerCase(this)">
        </div>
        <div>
          <button type="submit" class="marketing-button">Install</button>
        </div>
      <% end %>
    </div>
  </body>

  <script>
    function autoLowerCase(input) {
      input.value=input.value.toLowerCase().trim();
    }
  </script>
</html>

<!DOCTYPE html>
<html>
  <head>
    <title>Login</title>
    <link href='//fonts.googleapis.com/css?family=Dosis:' rel='stylesheet' type='text/css'>
    <%= render partial:"common/fav_icon"%>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

<style>
    /* Main container */
    .index {
      min-height: 100vh;
      width: 100%;
      display: flex;
      align-items: stretch;
      justify-content: stretch;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
     .container {
      display: flex;
      width: 100%;
      min-height: 100vh;
    }
    /* Left Column */
    .leftColumn {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 1rem;
      padding: 2rem;
      max-width: 950px;
      margin: 0 auto;
      justify-content: center;
      background-color: #F3F7FE;
    }
    .fsLogo {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom:2rem;
    }

    .go-back {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #6b7280;
      text-decoration: none;
      font-size: 14px;
      margin-bottom: 40px;
      width: fit-content;
    }

    .go-back:hover {
      color: #374151;
    }

    .form-container {
      background: white;
      border-radius: 12px;
      padding: 40px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      max-width: 400px;
      width: 100%;
      margin: 0 auto;
    }

    .logo {
      margin: 0 auto 30px;
      height: 40px;
    }

    .form-group {
      margin-bottom: 24px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 500;
      color: #374151;
    }

    .marketing-input {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.2s;
      box-sizing: border-box;
    }

    .marketing-input:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .marketing-input::placeholder {
      color: #9ca3af;
    }

    .marketing-button {
      width: 100%;
      background: #374151;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .marketing-button:hover {
      background: #1f2937;
    }
    /* Right Column */
    .rightColumn {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 2rem;
      padding: 2rem 4rem;
      justify-content: center;
      min-height: 100vh;
      max-width:650px;
    }
    /* Hero Section - Two Row Layout */
    .heroSection {
      display: flex;
      flex-direction: column;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    /* Row 1: Two Column Layout */
    .heroTopRow {
      display: flex;
    }
    /* Row 2: Full Width Features */
    .heroBottomRow {
      padding: 2rem;
      background: #F0FAFF;
    }
    /* Left Column - Light Background with Text and Features */
    .heroTextColumn {
      background: #2b3c81;
      padding: 3rem 2rem;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 2rem;
    }
    .heroText {
      color: #ffffff;
      font-size: 24px;
      font-weight: 700;
      margin: 0;
      line-height: 1.3;
      text-align: left;
    }
    /* Hero Features inside Text Column */
    .heroFeaturesList {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    .heroFeatureItem {
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
      font-size: 1rem;
      line-height: 1.5;
      color: #374151;
    }
    .heroCheckmark {
      color: #10b981;
      font-weight: 700;
      font-size: 1.2rem;
      flex-shrink: 0;
      margin-top: 0.1rem;
    }
    /* Right Column - White Background with Image */
    .heroImageColumn {
      background: white;
      padding: 3rem 2rem;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .integrationImage {
      max-width: 200px;
      height: auto;
      border-radius: 8px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .container {
        flex-direction: column;
      }
      .leftColumn,
      .rightColumn {
         flex: none;
        width: 100%;
        padding: 1rem;
      }

      .heroText {
        font-size: 1.5rem;
      }

      .heroSection {
        flex-direction: column;
      }

      .heroTextColumn {
        padding: 2rem 1.5rem;
        gap: 1.5rem;
      }

      .heroImageColumn {
        padding: 2rem 1.5rem;
      }

      .heroFeatureItem {
        font-size: 0.9rem;
      }

      .integrationImage {
        max-width: 150px;
      }
    }

    @media (max-width: 480px) {
      .heroText {
        font-size: 1.25rem;
      }

      .heroSection {
        flex-direction: column;
      }

      .heroTextColumn {
        padding: 1.5rem;
        gap: 1rem;
      }

      .heroImageColumn {
        padding: 1.5rem;
      }

      .heroFeatureItem {
        font-size: 0.85rem;
      }

      .heroCheckmark {
        font-size: 1rem;
      }

      .integrationImage {
        max-width: 120px;
      }
    }
  </style>
</head>
<body>
  <div class="index">
    <div class="container">
      <!-- Left Column -->
      <div class="leftColumn">
        <div class="form-container">
          <div class="fsLogo">
            <%= render partial:"common/fs_logo"%>
          </div>
          <a href="/signin" class="go-back">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M10 12L6 8L10 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Go back
          </a>
          <img src="<%= image_path 'shopify_logo.png' %>" alt="" class="logo">
    
            <%= form_tag shopify_authorize_integrations_path, method: :post do %>
              <% if flash[:error] %>
                <div class="error"><%= flash[:error] %></div>
              <% end %>
              
              <div class="form-group">
                <label for="shop">Enter your store name</label>
                <input id="shop" 
                      name="shop" 
                      type="text" 
                      autofocus="autofocus" 
                      placeholder="yourstorename.myshopify.com" 
                      class="marketing-input" 
                      oninput="return autoLowerCase(this)">
              </div>
              
              <button type="submit" class="marketing-button">Login</button>
            <% end %>
          </div>
      </div>
      <!-- Right Column -->
      <div class="rightColumn">
        <!-- Hero Section - 2 Row Layout -->
        <div class="heroSection">
          <!-- Row 1: Two Column Layout -->
          <div class="heroTopRow">
            <!-- Left Column - Text -->
            <div class="heroTextColumn">
              <h2 class="heroText">Automatically match order & update order tracking code</h2>
            </div>
            
            <!-- Right Column - Image -->
            <div class="heroImageColumn">
              <img src="<%= image_path 'login_right_image.png' %>"
                class="integrationImage"
              />
            </div>
          </div>
          
          <!-- Row 2: Full Width Features -->
          <div class="heroBottomRow">
            <ul class="heroFeaturesList">
              <li class="heroFeatureItem">
                <span class="heroCheckmark">✓</span>
                <span>30M+ Orders Tracked</span>
              </li>
              <li class="heroFeatureItem">
                <span class="heroCheckmark">✓</span>
                <span>Fits any 3PL courier</span>
              </li>
              <li class="heroFeatureItem">
                <span class="heroCheckmark">✓</span>
                <span>Fetch order files from any source</span>
              </li>
              <li class="heroFeatureItem">
                <span class="heroCheckmark">✓</span>
                <span>Add multiple tracking info to orders</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

  <script>
    function autoLowerCase(input) {
      input.value=input.value.toLowerCase().trim();
    }
  </script>
</html>

<!DOCTYPE html>
<html>
  <head>
    <title>Sign Up</title>
    <link href='//fonts.googleapis.com/css?family=Dosis:' rel='stylesheet' type='text/css'>
    <%= render partial:"common/fav_icon"%>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <style>
    .container { text-align:center;padding:20px; }
    .navbar .brand { float:none;}
    .marketing-button{ width: 50%;background-color: #1fb8ed;color: white;font-size: 12px;padding: 9px 34px;font-weight: 600;line-height: inherit;letter-spacing: 1.7px;text-transform: uppercase;border-radius: 2px;border-color: #ebebeb;outline: none; transition: 0.15s linear;}
    body{font-family: "Open Sans",sans-serif;color: #535353;font-weight: 300;font-size: 0.9375rem;line-height: 1.9;background-color: #ffffff;}
    h3{font-family:"Dosis","Open Sans",sans-serif;color: #37404d;letter-spacing: 1px;line-height: 1.6;font-size: 28px;font-weight: 600;margin-bottom: 8px;}
    .section-header hr{width: 50px;margin-bottom: 1.5rem;border-color: rgba(83,83,83,0.07);}
    small{color: #b5b9bf;}
    .lead{font-size: 15px;margin-bottom: 16px;}
    img{max-width: 300px;width: 100%}
    .marketing-input{line-height: 32px;font-size: 14px;padding: 7px 20px;width: 50%;    border-color: #70cef8;color: #535353;background-clip: padding-box;border: 1px solid rgba(0,0,0,.15); }
    .marketing-input:focus{background-color: #fff;border-color: #5cb3fd;outline: 0;}
    @media only screen and (max-width: 500px) {
      .marketing-input {
          width: 100%;
        }
        .marketing-button {
            width: 100%;
        }
    }
    .logo {
      width: 150px;
    }
    .error {
      font-size: 13px;
      color: red;
    }
    .trouble {
      font-size: 13px;
    }
    </style>
  </head>

  <body>
    <div class="container">
      <header class="section-header">
        <%= render partial:"common/fs_logo"%>
        <h3>More than <strong>30 million orders</strong> has been fulfilled</h3>
        <p class="lead">Sign in to your account.</p>
      </header>

      <%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>
        <% if flash[:error] %>
          <div class=error><%= flash[:error] %></div>
        <% end %>
        <div>
          <%= f.email_field :email, autofocus: true, autocomplete: "email", placeholder: "Email", class: "marketing-input" %>
        </div>

        <div>
          <%= f.password_field :password, autocomplete: "off", placeholder: "Password", class: "marketing-input" %>
        </div>

        <%= hidden_field_tag :shop_id, params[:shop_id] %>

        <div>
          <%= f.submit "Sign In", class: "marketing-button" %>
        </div>
      <% end %>
    </div>
  </body>
</html>
<p>Hi <%= @receiver.store_domain %>,</p>
<p>
  FulfillSync has finished process your scheduled update of the fulfillments: <br>
  Name: <%= @source.name %><br>
  Status: <%= Settings.fulfillment_statuses[@result[:status]] %>
  <% if @result[:status] == "success" %>
    <br>
    Fulfillments created:
    <table border="1" style="border-collapse: collapse;" cellpadding="5" cellspacing="5">
      <thead>
        <th>Order Number</th>
        <th>Tracking Number</th>
      </thead>
      <tbody>
        <% @result[:fulfillments].each do |fulfillment| %>
          <tr>
            <td> <%= link_to(fulfillment[:order_number], "https://#{@receiver.shopify_domain}/admin/orders/#{fulfillment[:order_id]}", target: "_blank") %></td>
            <td><%= fulfillment[:tracking_number] %></td>
          </tr>
        <% end %>
      </tbody>
    </table>
  <% end %>

  <br>

  Detailed information on each fulfillment is available at <%= link_to("FulfillSync dashboard", "https://#{@receiver.shopify_domain}/admin/apps/fulfillsync/", target: "_blank") %>.
</p>

<p>
  This is an automated email sent by FulfillSync.
</p>

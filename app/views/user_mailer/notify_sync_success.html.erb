<p>Hi <%= @receiver.store_domain %>,</p>
<p>
  FulfillSync has finished process your scheduled update of the fulfillments: <br><br>
  Name: <%= @source.name %><br>
  Status: <%= Settings.fulfillment_statuses[@result[:status]] %>
  <br>
  Date of fulfillment: <%= @result[:sync_log][:processed_at].to_formatted_s(:rfc822) %>
  <br>
  Total fulfillments: <%= @result[:sync_log][:number_fulfillment_updated] %>
  <br>
  Fulfillments created:
  <table border="1" style="border-collapse: collapse;" cellpadding="5" cellspacing="5">
    <thead>
      <th>Order Number</th>
      <th>Tracking Number</th>
    </thead>
    <tbody>
      <% @result[:fulfillments].each do |fulfillment| %>
        <tr>
          <td> <%= link_to(fulfillment[:order_number], "https://#{@receiver.shopify_domain}/admin/orders/#{fulfillment[:order_id]}", target: "_blank") %></td>
          <td><%= fulfillment[:tracking_number] %></td>
        </tr>
      <% end %>
    </tbody>
  </table>

  <br>
  File name: <%= @result[:sync_log].source[:source_file] %>

  <br>
  Detailed information on each fulfillment is available at <%= link_to("FulfillSync dashboard", "https://#{@receiver.shopify_domain}/admin/apps/fulfillsync/", target: "_blank") %>.
</p>

<p>
  This is an automated email sent by FulfillSync.
</p>

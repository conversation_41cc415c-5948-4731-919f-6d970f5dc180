
<table>
  <tr>
    <th>Product ID</th>
    <th>SKU</th>
    <th>Order Qty</th>
    <th>Price</th>
    <th>Level</th>
  </tr>
  <% @shopify_order.each do | order | %>
    <% order.line_items.each do | li | %>

      <tr>
        <td><%= order.id %> </td>
        <td><%= li["sku"] %></td>
        <td><%= li["quantity"] %> </td>
        <td><%= li["price"] %> </td>
        <%
          if v = @variants[li["variant_id"]]
        %>
          <% v[:inventory_levels].each do | il | %>
            <td><%= "Mng: #{v[:inventory_management]} Location: #{il[:location_id] } Qty: #{il[:available]}" %></td>
          <% end %>
        <% else %>
          <td>not found variant and inventory level</td>
        <% end %>
      </tr>
    <% end %>

  <% end %>
</table>
<%
attrs = @shopify_order.first.instance_variables %>
<% attrs.each do | att | %>
  <% att = att.to_s.gsub("@", "") %>
  <%= att %>: 
  <% begin %>
    <% value = @shopify_order.first.send(att) %>
    <% if value.is_a? Array %>
      <ol>
      <% value.each do | v | %>
        <li><%= v.pretty_inspect %></li>
      <% end %>
      </ol>
    <% else %>
      <%= value.pretty_inspect %><br>
    <% end %>
  <% rescue TypeError => e %>
    -error-
  <% end %>
<% end %>


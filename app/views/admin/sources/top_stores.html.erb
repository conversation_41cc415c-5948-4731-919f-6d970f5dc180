<h2>Type (last 30 days)</h2>
  <table>
    <tr>
      <th>Type</th>
      <th>Count</th>
    </tr>
  <% @type_counts.each do | row | %>
    <tr>
      <td><%= row.source_type %></td>
      <td><%= number_with_delimiter row.count %></td>
    </tr>
  <% end %>
</table>

<hr>

<h2>All time Top <%= @data.length %> Spender Stores within one month</h2>
  <table>
    <tr>
      <th>Store Name</th>
      <th>Month</th>th>
      <th>Fulfilled</th>
    </tr>
  <% @data.each do | row | %>
    <tr>
      <td><%= link_to row.shopify_domain, admin_shop_url(row.shop_id) %></td>
      <td><%= row.month.month %>/<%= row.year.year %></td>
      <td><%= number_with_delimiter row.fulfilled_count %></td>
    </tr>
  <% end %>
</table>

<hr>

<h2>Top <%= @one_month_data.length %> Spender Stores (last one month)</h2>
  <table>
    <tr>
      <th>Store Name</th>
      <th>Fulfilled</th>
    </tr>
  <% @one_month_data.each do | row | %>
    <tr>
      <td><%= link_to row.shopify_domain, admin_shop_url(row.shop_id) %></td>
      <td><%= number_with_delimiter row.fulfilled_count %></td>
    </tr>
  <% end %>
</table>

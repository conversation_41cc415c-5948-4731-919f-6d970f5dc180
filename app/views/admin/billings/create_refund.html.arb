form action: process_refund_admin_billing_path, method: :post do |f|
  fieldset class: "inputs" do
    legend do
      span do
        "Create Refund"
      end
    end
    f.input name: "authenticity_token", type: :hidden, value: form_authenticity_token.to_s
    ol do
      li class: "input" do
        f.label "Refund Value", class: "label"
        f.input "refund_value", value: billing.total_charge, type: :number, name: "refund_value", max: billing.total_charge
      end
    end
  end
  f.input "Save", type: :submit, value: "Save"
end

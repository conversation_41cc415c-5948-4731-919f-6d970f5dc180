<div id = "container">
 <%= content_tag :div, class: "background_process", data: {temp: params[:background_process]} do %>
  <% end %>
</div>

<script type= "text/javascript">
  var background_process = $('.background_process').data('temp');
  // testing whether same values are overlapping 
  $(function () {
    $('#container').highcharts({
        title: {
            text: 'System Information',
            x: -20 //center
        },
        subtitle: {
            text: 'Source: Uptracker',
            x: -20
        },
        xAxis: {
            categories: background_process["timestamp"]
        },
        yAxis: {
            title: {
                text: 'values'
            },
            plotLines: [{
                value: 0,
                width: 1,
                color: '#808080'
            }]
        },
        tooltip: {
          
        },
        legend: {
            layout: 'vertical',
            align: 'right',
            verticalAlign: 'middle',
            borderWidth: 0
        },
        series: [{
            name: 'Running Source',
            data: background_process['running_source']
        }, {
            name: 'Running Job',
            data: background_process['running_job']
        }
        ]
    });
});

</script>
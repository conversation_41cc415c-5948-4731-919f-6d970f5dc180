<h3>Order/Fulfillments</h3>
<% if @fulfillments.is_a? ShopifyAPI::Order %>
  <table>
    <tr>
      <th>SKU</th>
      <th>Order Qty</th>
      <th>Fulfilled Qty</th>
      <th>Level</th>
    </tr>
  <% sku_qty = {}
    @fulfillments.fulfillments.each do | f |
      f.line_items.each do | line |
        fulfilled_qty = line["quantity"] - line["fulfillable_quantity"]
        if sku_qty[line["sku"]]
          sku_qty[line["sku"]] += fulfilled_qty
        else
          sku_qty[line["sku"]] = fulfilled_qty
        end
      end
    end
  %>
  <% @fulfillments.line_items.each do | li | %>

    <tr>
      <td><%= li["sku"] %></td>
      <td><%= li["quantity"] %> </td>
      <td><%= sku_qty[li["sku"]] %></td>
      <td><%= %></td>
      <%
        v = ShopifyAPI::LightGraphQL.query(GraphqlHelper.query_product_variant(variant_id: "gid://shopify/ProductVariant/#{li["variant_id"]}"))
      %>
      <% (v.dig("inventoryItem","inventoryLevels","nodes") || []).each do |level| %>
        <td><%= "Mng: #{v["inventoryItem"]["tracked"]} Policy: #{v["inventoryPolicy"]} Location: #{level["location"]["id"] } Qty: #{level["quantities"][0]["quantity"]}" %></td>
      <% end %>
    </tr>
  <% end %>
  </table>
<% elsif @fulfillments.is_a? Array %>
  <% @fulfillments.each do | f | %>
    <%= f.pretty_inspect.gsub("\n", "<br>").html_safe %>
  <% end %>
<% end %>


<%
attrs = @fulfillments.instance_variables %>
<% attrs.each do | att | %>
  <% att = att.to_s.gsub("@", "") %>
  <%= att %>: 
  <% begin %>
    <%= @fulfillments.send(att).pretty_inspect %><br>
  <% rescue TypeError => e %>
    -error-
  <% end %>
<% end %>
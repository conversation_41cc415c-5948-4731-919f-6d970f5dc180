<%= render "/common/top_bar", title: "Dashboard" %>

<% unless @sources.present? %>
  <div class="jumbotron">
    <div class="container">
      <h1>Automate your fulfillment tracking update</h1>
      <h2>More than <span id="fulfilled-orders" data-orders="<%= @fulfilled_orders_count %>"></span> of fulfillment records!</h2>
      <p><small>This app will perform a remote connection to your vendor, supplier, dropshipper or warehouse to grab the latest fulfillment status, like Tracking Code and Tracking Company and update your store. Now we support manual file upload too! </small></p>

      <p>This will <strong>automatically</strong> keep your shoppers <strong>happy</strong> by getting notified when their order has been fulfilled.</p>
      <p><a class="btn btn-primary btn-lg" href="<%= new_source_url() %>" role="button">Click here to get started »</a></p>
    </div>
  </div>

<% else %>  
  <%= render partial: "sources/sync_status" , locals: { source: @sources.first } %>
  <h4 class="row-line">My Source</h4>
  <%= link_to "New Source", new_source_url(), class: "btn btn-info row-line  pull-right" if @shop.allow_new_source? %>
  <table class="table">
    <thead>
    	<th>Name</th>
      <th>File</th>
      <th>Scheduler</th>
      <th>Options</th>
    </thead>
    <tbody>
      <% @sources.each do |source| %>
        <%= content_tag :div, nil, id: "source_id_data", data: {source_id: source.id} %>
        <% if source.source_type == "email" %>
          <%= render partial: "sources/email_source", locals: { source: source } %>
        <% elsif source.source_type != "file_upload" %>
          <%= render partial: "sources/source", locals: { source: source } %>
        <% else %>
          <%= render partial: "sources/file_upload_source", locals: { source: source } %>
        <% end %>
    	<% end %>
    </tbody>
  </table>

  <%= render partial: "sources/sync_logs", locals: { sync_logs: @sync_logs } %>
  <div class="clearfix"></div>
  <%= render partial: "sources/fulfillment_logs", locals: { fulfillment_logs: @fulfillment_logs } %>

<% end %>

<script>
  App.cable.subscriptions.create({ channel: "StatusChannel", shop_id: "<%= current_shop.id %>" }, {
    received(data) {
      console.log("action cable data: ", data)
      if(data.status == "running"){
        $(`#<%= current_shop.id %>_${data.source_id} .sync-now-button`).html(`<i class="fa fa-refresh fa-spin"></i> Running`);
        $(`#<%= current_shop.id %>_${data.source_id} .sync-now-button`).addClass("disabled btn-warning").removeClass("btn-danger")
      }else if(data.status == "queuing"){
        $(`#<%= current_shop.id %>_${data.source_id} .sync-now-button`).html(`<i class="fa fa-refresh fa-spin"></i> Queuing`);
        $(`#<%= current_shop.id %>_${data.source_id} .sync-now-button`).addClass("disabled btn-danger").removeClass("btn-warning");
      }else{
        $(`#<%= current_shop.id %>_${data.source_id} .sync-now-button`).html(`<i class="fa fa-play"></i> Sync Now`);
        $(`#<%= current_shop.id %>_${data.source_id} .sync-now-button`).removeClass("disabled btn-warning").addClass("btn-danger");
      }
    }
  });
</script>

<!-- Start Exit Mist -->
<!-- End Exit Mist -->

<script type="text/javascript">
  window.mainPageTitle = 'Buttons Page';

  ShopifyApp.ready(function(){
    ShopifyApp.Bar.initialize({
      title: window.mainPageTitle,
      buttons: {
        primary: {label: "Primary", loading: false, callback: function(){ alert('Primary clicked'); } },
        secondary: [
          { label: "Help", callback: function(){ alert('help'); } },
          { label: "More",
            style: "Danger",
            type: "dropdown",
            links: [
                     { label: "Update", href: "/update", target: "app" },
                     { label: "Delete", callback: function(){ alert("destroy") } }
                   ]
          },
          { label: "Preview", href: "http://my-app.com/preview_url", target: "new" }
        ]
      }
    });
  });
</script>

<p>
  Buttons can be styled as "danger", "disabled", "primary", or null/undefined as default.
</p>

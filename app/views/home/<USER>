<script type="text/javascript">
   window.mainPageTitle = 'Form page';

  ShopifyApp.ready(function(){
    ShopifyApp.Bar.initialize({
      title: window.mainPageTitle,
      icon: '/fs_favicon.svg',
      buttons: {
        primary: {
          label: "Create Unicorn",
          message: 'save-unicorn-form-message'
        },
        secondary: {
          label: "Back",
          href: "/",
          target: 'app'
        }
      }
    });

  <% if flash[:notice] %>
    ShopifyApp.flashNotice("<%= flash[:notice] %>");
  <% end %>

  <% if flash[:error] %>
    ShopifyApp.flashError("<%= flash[:error] %>");
  <% end %>

  });
</script>

<h1>Create new Unicorn</h1>

<p>A normal form submitted by a data tag and a button in Shopify. Embedded apps can mostly work exactly like a normal app, except with Shopify admin UI integration.</p>

<form method="POST" action="form_page" data-shopify-app-submit="save-unicorn-form-message">
  <input name="authenticity_token" value="<%= form_authenticity_token %>" type="hidden">
  <p>
    <label>Name:</label>
    <input type="text" name="name"/>
  </p>

  <p>
    <label>Colour:</label>
    <select name="colour">
      <option>Pink</option>
      <option>Rainbow</option>
      <option>Purple</option>
    </select>
  </p>

</form>

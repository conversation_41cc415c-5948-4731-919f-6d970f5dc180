<script type="text/javascript">
  ShopifyApp.ready(function(){
    ShopifyApp.Bar.initialize({
      title: 'Breadcrumbs',
      breadcrumb: {
        label: "Regular Page",
        href: "/regular_app_page",
        target: 'app'
      },
      icon: '/fs_favicon.svg',
      buttons: {
        secondary: {
          label: "Back",
          href: "/",
          target: 'app'
        }
      }
    });
  });
</script>

<h1>Breadcrumbs</h1>

<p>In addition to the page title, a single middle level of breadcrumbs can be inserted between the root of the app and the current page. It is defined the same way as a button with label, href, target, loading, etc..</p>

<p>If there is no title set the breadcrumb will not appear.</p>

<script type="text/javascript">
   window.mainPageTitle = 'Pagination (<%= @page %> of <%= @total_pages %>)';

  ShopifyApp.ready(function(){
    ShopifyApp.Bar.initialize({
      title: window.mainPageTitle,
      icon: '/fs_favicon.svg',
      buttons: {
        secondary: {
          label: "Back",
          href: "/",
          target: 'app'
        }
      },
      pagination: {
        previous: <%= (@previous_page.present? ? {href: @previous_page} : nil).to_json.html_safe %>,
        next: <%= (@next_page.present? ? {href: @next_page} : nil).to_json.html_safe %>
      }
    });
  });
</script>

<h1>Pagination</h1>

<p>Now viewing page <%= @page %> of <%= @total_pages %>.</p>

<p>Pagination links at the top page can load a passed in URL or a Javascript callback.</p>

module Graphql<PERSON>el<PERSON>
  def self.get_orders_id(total, names)
    query_str = ""
    names.each_with_index do |value, index|
      next if value.blank?

      query_str += "name:" + value.to_s + ((index < names.length - 1) ? " OR " : "")
    end
    {"query" => clean_query(
      <<-GRAPHQL
        query {
          orders(first: #{total}, query:"#{query_str}" ) {
            edges {
              node {
                id
              }
            }
          }
        }
    GRAPHQL
    )}
  end

  def self.add_orders_tag(ids, tag)
    {"query" => clean_query(
      <<-GRAPHQL
      mutation {#{
        ids.map.with_index do |id, idx|
          "Order#{idx}: tagsAdd(id: \"#{id}\", tags: [\"#{tag}\"]) {
            userErrors {
              field
              message
            }
          }"
        end.join
      }}
    GRAPHQL
    )}
  end

  def self.fulfillment_create(payload)
    {
      "operationName" => "fulfillmentCreate",
      "query" => clean_query(<<~GRAPHQL
        mutation fulfillmentCreate($fulfillment: FulfillmentInput!) {
          fulfillmentCreate(fulfillment: $fulfillment) {
            fulfillment {
              id
              status
            }
            userErrors {
              field
              message
            }
          }
        }
      GRAPHQL
                            ),
      "variables" => payload
    }
  end

  def self.fulfillment_event_create(payload)
    {
      "operationName" => "fulfillmentEventCreate",
      "query" => clean_query(<<~GRAPHQL
        mutation fulfillmentEventCreate($fulfillmentEvent: FulfillmentEventInput!) {
          fulfillmentEventCreate(fulfillmentEvent: $fulfillmentEvent) {
            fulfillmentEvent {
              id
              status
              message
            }
            userErrors {
              field
              message
            }
          }
        }
      GRAPHQL
                            ),
      "variables" => payload
    }
  end

  def self.mark_order_as_paid(order_id)
    {
      "query" => clean_query(<<~GRAPHQL
        mutation orderMarkAsPaid($input: OrderMarkAsPaidInput!) {
          orderMarkAsPaid(input: $input) {
            order {
              id
            }
            userErrors {
              field
              message
            }
          }
        }
      GRAPHQL
                            ),
      "variables" => {"input" => {"id" => "gid://shopify/Order/#{order_id}"}}
    }
  end

  def self.query_product_variant(opts = {})
    {"query" => clean_query(
      <<-GRAPHQL
      query{
        productVariant(id: "#{opts[:variant_id]}") {
          id
          inventoryPolicy
          inventoryItem {
            tracked
            inventoryLevels(first: 250) {
              nodes {
                location {
                  id
                  name
                }
                quantities(names: ["available"]) {
                  name
                  quantity
                }
              }
            }
          }
        }
      }
    GRAPHQL
    )}
  end

  def self.clean_query(schema)
    schema.delete!("\n").squeeze!(" ")
  end
end

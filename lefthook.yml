pre-commit:
  parallel: true
  commands:
    backend:standard:
      glob: "*.rb"
      run: bundle exec standardrb --fix {staged_files}
      stage_fixed: true
    frontend:prettier:
      glob: "app/javascript/**/*"
      run: pnpm prettier --write --cache --ignore-unknown {staged_files}
      stage_fixed: true
    frontend:eslint:
      glob: "app/javascript/**/*.{js,jsx,ts,tsx}"
      run: pnpm eslint --cache --fix {staged_files}
      stage_fixed: true

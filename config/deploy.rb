set :application, "fulfillment-sync"
set :repo_url, "**************:syncx-org/fulfillsync.git"

set :pty, true

set :linked_dirs, %w[log tmp vendor/bundle public/system]
set :linked_files, %w[config/master.key]

set :keep_releases, 3
set :keep_assets, 6
set :rvm_ruby_version, "3.3.6"

set :slack<PERSON><PERSON>, {
  username: "Fulfill Sync Deployment",
  klass: Slackistrano::CustomMessaging,
  channel: "#work",
  webhook: "*****************************************************************************"
}

namespace :logs do
  desc "tail rails logs"
  task :tail do
    on roles(:app) do
      execute "tail -f #{shared_path}/log/#{fetch(:rails_env)}.log"
    end
  end
end

namespace :logs do
  desc "tail dj logs"
  task :dj do
    on roles(:app) do
      execute "tail -f #{shared_path}/log/delayed_job.log"
    end
  end
end

namespace :rails do
  desc "Invoke a rake command on the remote server"
  task :invoke, [:command] => "shop:update_shop_domain" do |_task, args|
    on primary(:app) do
      within current_path do
        with rails_env: fetch(:rails_env) do
          rake args[:command]
        end
      end
    end
  end
end

# after 'deploy:published', 'delayed_job:restart' do
#   invoke 'delayed_job:restart'
#   on primary(:app) do
#     within release_path do
#       with rails_env: fetch(:rails_env) do
#         execute :rake, 'source:restart_jobs', "RAILS_ENV=production"
#       end
#     end
#   end
# end

# every '*/15 * * * *' do
#   command "cd /home/<USER>/apps/fulfillment-sync/current && RAILS_ENV=production bundle exec rake source:sync_fulfillments"
# end

# every 1.day, :at => '6:00 am' do
#   command "cd /home/<USER>/apps/fulfillment-sync/current && RAILS_ENV=production bundle exec rake backup:database"
# end

# Restart delayed job in job server
hourly_interval = 24
every hourly_interval.hours, at: "2:00 am" do
  # Restart quickscan DJ
  command "cd /home/<USER>/apps/barcode-reader/current && RAILS_ENV=production bundle exec bin/delayed_job -i 'quickscan' restart"
  # Restart uptracker DJ
  command "cd /home/<USER>/apps/fulfillment-sync/current && RAILS_ENV=production bundle exec rake source:restart_jobs"
  # Restart neon DJ
  command "cd /home/<USER>/apps/neon/current && RAILS_ENV=production bundle exec bin/delayed_job -i 'neon.0' restart"
  # Restart stocksync job DJ
  # command "cd /home/<USER>/apps/stocksync-job/current && RAILS_ENV=job bundle exec rake job:restart_delayed_job"
end

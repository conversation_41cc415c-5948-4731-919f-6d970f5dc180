role :app, %w[root@45.32.229.187]
role :web, %w[root@45.32.229.187]
role :db, %w[root@45.32.229.187]

set :deploy_to, "/var/www/uptracker-staging"
set :user, "root"
set :branch, "staging"
set :rvm_ruby_version, "3.3.6"
set :nvm_node, "v20.19.0"
# Must match `ViteRuby.config.public_output_dir`, which by default is 'vite'
# https://vite-ruby.netlify.app/guide/deployment.html#using-capistrano
set :assets_prefix, "vite"

set :ssh_options, {
  forward_agent: false,
  user: fetch(:user),
  auth_methods: ["publickey"],
  keys: ["cloudcoder.pem"]
}

server "45.32.229.187", user: "root", roles: %w[app]

namespace :deploy do
  desc "Restart nginx"
  task :restart do
    on roles(:app), in: :sequence do
      sudo "service nginx restart"
    end
  end

  after "deploy:assets:backup_manifest", "maintenance:enable"
  after :publishing, :restart
  after "deploy:restart", "maintenance:disable"
end

role :app, %w[<EMAIL>]
role :web, %w[<EMAIL>]

set :ssh_options, {
  forward_agent: true,
  user: fetch(:user)
}

server "private-web3.stock-sync.com", user: "ubuntu", roles: %w[web]
server "private-web3.stock-sync.com", user: "ubuntu", roles: %w[app], primary: true

set :branch, proc { `git rev-parse --abbrev-ref staging`.chomp }

set :rvm_ruby_version, "3.3.6"
set :nvm_node, "v20.19.0"
set :nvm_map_bins, %w[rake]

set :deploy_to, "/home/<USER>/apps/staging-fulfillment-sync"
set :rails_env, "staging"
# Must match `ViteRuby.config.public_output_dir`, which by default is 'vite'
# https://vite-ruby.netlify.app/guide/deployment.html#using-capistrano
set :assets_prefix, "vite"
set :assets_role, [:web]

namespace :deploy do
  desc "Restart application"
  task :restart do
    on roles(:web), in: :sequence do
      sudo "passenger-config restart-app /home/<USER>/apps/staging-fulfillment-sync --ignore-app-not-running"
    end
  end

  Rake::Task["deploy:assets:precompile"].clear_actions
  desc "Precompile assets locally and then rsync to web servers"
  namespace :assets do
    task :precompile do
      p "Monkey patch manifest cleanup"
      run_locally do
        execute :rm, "-f", "public/vite/manifest.json"
        execute :rm, "-f", "public/vite/manifest-assets.json"
      end

      p "JENKINS - PRECOMPILING LOCALLY NOW!!!"
      run_locally do
        with rails_env: :production do
          execute :bundle, "exec rake assets:precompile"
        end
      end
      on roles(:web), in: :parallel do |r|
        run_locally do
          execute :rsync,
            # "-av --delete ./public/packs/ #{r.user}@#{r.hostname}:#{release_path}/public/packs/"
            "-av --delete ./public/vite/ #{r.user}@#{r.hostname}:#{release_path}/public/vite/"
          execute :rsync,
            # "-av --delete ./public/assets/ #{r.user}@#{r.hostname}:#{release_path}/public/assets/"
            "-av --delete ./public/assets/ #{r.user}@#{r.hostname}:#{release_path}/public/assets/"
          execute :rsync,
            "-av ./public/vite/.vite/ #{r.user}@#{r.hostname}:#{release_path}/public/vite/"
        end
      end
      run_locally do
        execute :find, "public/assets", "-type", "f", "-mtime", "+5", "-exec", "rm", "{}", '\\;'
        execute :find, "public/vite", "-type", "f", "-mtime", "+5", "-exec", "rm", "{}", '\\;'
      end
    end
  end
  after "deploy:assets:backup_manifest", "maintenance:enable"
  after :publishing, :restart
  after "deploy:restart", "maintenance:disable"
end

role :app, %w[<EMAIL>]
role :web, %w[<EMAIL>]
role :db, %w[<EMAIL>]
server "web1.stock-sync.com", user: "ubuntu", roles: %w[web]
server "job.stock-sync.com", user: "ubuntu", roles: %w[app], primary: true

# Must match `ViteRuby.config.public_output_dir`, which by default is 'vite'
# https://vite-ruby.netlify.app/guide/deployment.html#using-capistrano
set :assets_prefix, "vite"
set :nvm_node, "v20.19.0"
set :nvm_map_bins, %w[rake]

set :deploy_to, "/home/<USER>/apps/fulfillment-sync"
set :user, "ubuntu"
set :branch, ENV.fetch("BRANCH", "master")
set :assets_role, [:web]
set :filter, hosts: %w[web1.stock-sync.com job.stock-sync.com]
set :rvm_ruby_version, "3.3.6"

set :ssh_options, {
  forward_agent: true,
  user: fetch(:user),
  auth_methods: ["publickey"],
  keys: ["cloudcoder.pem"]
}

set :maintenance_template_path, File.expand_path("../../public/maintenance.html", __dir__)

namespace :deploy do
  desc "Restart application"
  task :restart do
    on roles(:web), in: :sequence do
      # Restart passenger manually
      sudo "passenger-config restart-app /home/<USER>/apps/fulfillment-sync --ignore-app-not-running"
    end

    on primary(:app) do
      within release_path do
        with rails_env: fetch(:rails_env) do
          execute :rake, "source:restart_jobs", "RAILS_ENV=production"
        end
      end
    end
  end

  # Rake::Task["deploy:assets:precompile"].clear_actions
  # desc "Precompile assets locally and then rsync to web servers"
  # namespace :assets do
  #   task :precompile do
  #     p "PRECOMPILING LOCALLY NOW!!!"
  #     run_locally do
  #       with rails_env: :production do
  #         execute :bundle, "exec rake assets:precompile"
  #       end
  #     end
  #     on roles(:web), in: :parallel do |r|
  #       run_locally do
  #         execute :rsync,
  #           "-av --delete -e \"ssh -i ./cloudcoder.pem\" ./public/packs/ #{r.user}@#{r.hostname}:#{release_path}/public/packs/"
  #         execute :rsync,
  #           "-av --delete -e \"ssh -i ./cloudcoder.pem\" ./public/assets/ #{r.user}@#{r.hostname}:#{release_path}/public/assets/"
  #       end
  #     end
  #     run_locally do
  #       execute :rm, "-rf public/assets"
  #       execute :rm, "-rf public/packs"
  #     end
  #   end
  # end

  after "deploy:assets:backup_manifest", "maintenance:enable"
  after :publishing, :restart
  after "deploy:restart", "maintenance:disable"
end

## Cap task to restart delayed jobs
namespace :delayed_job do
  desc "Restart delayed job"
  task :restart do
    on roles(:app) do
      within release_path do
        with rails_env: fetch(:rails_env) do
          execute :rake, "source:restart_jobs", "RAILS_ENV=production"
        end
      end
    end
  end
end

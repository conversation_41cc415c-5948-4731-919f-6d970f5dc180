# Code below can be removed once we upgrade to a newer version of shopify_api gem
# module ShopifyAPI
#   module AdminVersions
#     SUPPORTED_ADMIN_VERSIONS = T.let([
#       "2025-01"
#     ], T::Array[String])

#     LATEST_SUPPORTED_ADMIN_VERSION = T.let("2025-01", String)
#   end

#   SUPPORTED_ADMIN_VERSIONS = ShopifyAPI::AdminVersions::SUPPORTED_ADMIN_VERSIONS
#   LATEST_SUPPORTED_ADMIN_VERSION = ShopifyAPI::AdminVersions::LATEST_SUPPORTED_ADMIN_VERSION
# end

# Used to call Shopify API (shopify_api gem)

ShopifyAPI::Context.setup(
  api_key: ENV["SHOPIFY_KEY"],
  api_secret_key: ENV["SHOPIFY_SECRET"],
  host: Settings.app_domain,
  scope: "read_all_orders, write_orders, read_inventory, read_products, write_merchant_managed_fulfillment_orders, write_fulfillments, write_assigned_fulfillment_orders, write_third_party_fulfillment_orders, read_customers",
  is_embedded: false, # Set to true if you are building an embedded app
  api_version: Settings.api_version, # The version of the API you would like to use
  is_private: false # Set to true if you have an existing private app
)

# Code below can be removed once we upgrade to a newer version of shopify_api gem
# Reason for this code block is that the shopify_api gem does not support all the resources in the latest version.
ShopifyAPI::Context.load_rest_resources(api_version: "2023-07")

### IMPORTANT - DO NOT REMOVE. shopify_app gem will need to pick up the registry (initiated here) to install the webhooks
ShopifyAPI::Webhooks::Registry.add_registration(topic: "app/uninstalled", delivery_method: :http, path: File.join(Settings.webhook_host, "uninstall"), handler: WebhookManagerJob)

# Be sure to restart your server when you modify this file.

# Define an application-wide content security policy
# For further information see the following documentation
# https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy

Rails.application.config.content_security_policy do |policy|
  policy.default_src :self, :https
  policy.font_src :self, :https, :data
  policy.img_src :self, :https, :data
  policy.object_src :none
  policy.script_src :self, :https, :unsafe_inline, "*.cloudfront.net"
  policy.style_src :self, :https, :unsafe_inline
  policy.connect_src :self, :https, :wss
  policy.frame_ancestors :https, "admin.shopify.com"

  if Rails.env.development?
    # Allow @vite/client to hot reload javascript changes in development
    policy.script_src(*policy.script_src, :unsafe_eval, "http://#{ViteRuby.config.host_with_port}")
    # Allow @vite/client to hot reload style changes in development
    policy.style_src(*policy.style_src, :unsafe_inline)
    policy.connect_src(*policy.connect_src, "ws://#{ViteRuby.config.host_with_port}")
    # for vite ruby "skipProxy": true, directly loads images from vite dev server
    policy.img_src(*policy.img_src, :http)
  end

  # You may need to enable this in production as well depending on your setup.
  if Rails.env.test?
    policy.script_src(*policy.script_src, :blob)
  end
end

# If you are using UJS then enable automatic nonce generation
# Rails.application.config.content_security_policy_nonce_generator = -> request { SecureRandom.base64(16) }

# Report CSP violations to a specified URI
# For further information see the following documentation:
# https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy-Report-Only
# Rails.application.config.content_security_policy_report_only = true

Delayed::Worker.max_run_time = 8.hours
Delayed::Worker.logger = Logger.new(File.join(Rails.root, "log", "delayed_job.log"))
Delayed::Worker.queue_attributes = {
  update_fulfillments: {priority: 10}
}
class Delayed::Backend::ActiveRecord::Job < ::ActiveRecord::Base
  def self.ransackable_associations(auth_object = nil)
    @ransackable_associations ||= reflect_on_all_associations.map { |a| a.name.to_s }
  end

  def self.ransackable_attributes(auth_object = nil)
    @ransackable_attributes ||= column_names + _ransackers.keys + _ransack_aliases.keys + attribute_aliases.keys
  end
end

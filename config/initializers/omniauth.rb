class TokenVerifier
  include ActiveSupport::Configurable
  include ActionController::RequestForgeryProtection

  def call(env)
    @request = ActionDispatch::Request.new(env.dup)
    raise OmniAuth::AuthenticityError unless verified_request?
  end

  private

  attr_reader :request
  delegate :params, :session, to: :request
end
# in an initializer
OmniAuth.config.request_validation_phase = TokenVerifier.new

Rails.application.config.middleware.use OmniAuth::Builder do
  provider :shopify,
    ShopifyApp.configuration.api_key,
    ShopifyApp.configuration.secret,
    scope: ShopifyApp.configuration.scope,
    setup: lambda { |env|
             strategy = env["omniauth.strategy"]

             # get params if login page present
             request = Rack::Request.new(env)
             shop_domain = request.POST["shop"] || request.GET["shop"]
             # for default Shopify App usage
             if shop_domain.blank?
               shopify_auth_params = strategy.session["shopify.omniauth_params"]&.with_indifferent_access
               if shopify_auth_params.present?
                 shop_domain = shopify_auth_params[:shop]
                 puts shop_domain
               end
             end

             shop = shop_domain.present? ? "https://#{shop_domain}" : ""
             strategy.options[:client_options][:site] = shop
           },
    callback_path: "/auth/shopify/callback"
end

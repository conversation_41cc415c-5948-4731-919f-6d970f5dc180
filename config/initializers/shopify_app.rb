# Used to handle OAuth (shopify_app gem)
ShopifyApp.configure do |config|
  config.application_name = "syncX: Fulfill Tracking"
  config.api_key = ENV["SHOPIFY_KEY"]
  config.secret = ENV["SHOPIFY_SECRET"]
  scope_arr = [
    "read_all_orders", "read_inventory", "read_products", "read_locations", "read_customers",

    "read_orders", "write_orders",
    "read_fulfillments", "write_fulfillments",
    "read_merchant_managed_fulfillment_orders", "write_merchant_managed_fulfillment_orders",
    "read_assigned_fulfillment_orders", "write_assigned_fulfillment_orders",
    "read_third_party_fulfillment_orders", "write_third_party_fulfillment_orders"
  ]
  config.scope = scope_arr.join(",")
  config.embedded_app = false
  config.after_authenticate_job = false
  # config.session_repository = Shopify
  config.api_version = Settings.api_version
  # config.enable_same_site_none = Rails.env.production?
  config.webhooks = [
    {topic: "app/uninstalled", address: File.join(Settings.webhook_host, "uninstall")}
  ]
  config.webhooks_manager_queue_name = "mailers"
end

# ShopifyAPI::Base.api_version = '2021-07'

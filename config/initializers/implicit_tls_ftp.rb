require "net/ftp"
class ImplicitTlsFTP < Net::FTP
  FTP_PORT = 990

  def connect(host, port = FTP_PORT)
    synchronize do
      @host = host
      @bare_sock = open_socket(host, port)
      begin
        ssl_sock = start_tls_session(Socket.tcp(host, port))
        @sock = BufferedSSLSocket.new(ssl_sock, read_timeout: @read_timeout)
        voidresp
        if @private_data_connection
          voidcmd("PBSZ 0")
          voidcmd("PROT P")
        end
      rescue OpenSSL::SSL::SSLError, Net::OpenTimeout
        @sock.close
        raise
      end
    end
  end
end

module Net
  class FTP
    def makepasv # :nodoc:
      # for profile https://admin.stock-sync.com/admin/user_profiles/201539
      if Settings.ftp_special_host.include? @bare_sock.remote_address.ip_address
        host, port = parse229(sendcmd("EPSV"))
      elsif @bare_sock.remote_address.ipv4?
        host, port = parse227(sendcmd("PASV"))
      else
        host, port = parse229(sendcmd("EPSV"))
        #     host, port = parse228(sendcmd("LPSV"))
      end
      [host, port]
    end
  end
end

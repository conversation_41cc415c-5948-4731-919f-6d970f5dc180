module ShopifyAPI
  class LightGraphQLError < StandardError; end

  class LightGraphQL
    @@token = nil
    @@domain = nil

    def self.set(user)
      @@token = user.shopify_token
      @@domain = user.shopify_domain
    end

    def self.query(schema, type = :graphql)
      tries = 3
      tries_throttled = 300
      res = nil
      begin
        res = if @@token.present? && @@domain.present?
          response = Faraday.post(
            "https://#{@@domain}/admin/api/#{Settings.shopify_gql_api_version}/graphql.json",
            (type == :raw_json) ? schema : JSON.dump(schema),
            {"X-Shopify-Access-Token" => @@token, "Content-Type" => "application/json"}
          )
          JSON.parse(response.body)
        else
          {"errors" => [{"message" => "Shop not set"}]}
        end

        if res["errors"].present?
          if res["errors"].is_a?(Array) && res.dig("errors", 0, "message") == "Throttled"
            raise LightGraphQLError.new res["errors"]
          else
            raise StandardError.new "ShopifyAPI GraphQL: #{res["errors"].collect { |o| o.dig("extensions", "code") }.join(",")}" if res["errors"].is_a?(Array)
            raise StandardError.new "ShopifyAPI GraphQL: #{res["errors"]}"
          end
        end
        res
      rescue LightGraphQLError => e
        tries_throttled -= 1
        if tries_throttled > 0
          if res["extensions"]["cost"]["throttleStatus"]["currentlyAvailable"] >= res["extensions"]["cost"]["requestedQueryCost"]
            return res
          end
          time_taken = ((res["extensions"]["cost"]["requestedQueryCost"] - res["extensions"]["cost"]["throttleStatus"]["currentlyAvailable"]) / res["extensions"]["cost"]["throttleStatus"]["restoreRate"]).ceil
          sleep time_taken.second
          retry
        else
          Airbrake.notify(e, {details: "can't process shopify graphql response", data: (res.present? ? res : ""), schema: schema, domain: @@domain})
          res.present? ? res : {"errors" => [{"message" => "server error"}]}
        end
      rescue => e
        tries -= 1
        if tries > 0
          retry
        else
          Airbrake.notify(e, {details: "can't process shopify graphql response", data: (res.present? ? res : ""), schema: schema, domain: @@domain})
          res.present? ? res : {"errors" => [{"message" => "server error"}]}
        end
      end
    end
  end
end

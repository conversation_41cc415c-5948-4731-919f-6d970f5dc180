web_server_username: "ubuntu"
web_server_host: "web2.stock-sync.com"
support_email: "<EMAIL>"
api_version: "2025-01"
shopify_gql_api_version: "2025-01"
job_type: { hourly: "Hourly", daily: "Daily" }
fulfillment_statuses:
  {
    "success": "Success",
    "retrieve_failed": "No file has been retrieved",
    "no_data": "No information retrieved from file",
  }
source_types:
  {
    ftp: "FTP",
    ftps: "FTP with TLS",
    sftp: "SFTP",
    implicit_ftp: "Implicit FTP",
    url: "Direct Link(URL)",
    file_upload: "File Upload",
    email: "Email",
    google_sheet: "Google Sheet",
    smiffys_file: "Smiffys File",
    wholecell_file: "Wholecell",
    mstgolf: "MSTGolf File",
    bigcaring: "Bigcaring",
  }
email_subscription_options:
  newsletter: "newsletter"
  sync_success: "sync_success"
source_process:
  {
    none: "Do Nothing To My File",
    rename: "Rename My File",
    append_date: "Append Date To My File (<datetime>_filename)",
    delete: "Delete My File",
    move: "move to new folder",
  }
order_keys:
  {
    id: "Shopify Order ID",
    order_number: "Order Number",
    name: "Order Name",
    number: "Order Sequence Number",
  }
packages:
  {
    trial: { title: "Trial", cost: 0 },
    basic: { title: "Basic", cost: 5, limit: 1 },
    pro: { title: "Pro", cost: 10, limit: 3 },
    unlimited: { title: "Unlimited", cost: 24, limit: 10 },
  }
flexi: { cost: 0.01 }
free_trial_days: 10
accepted_mime:
  [
    "text/plain",
    "text/csv",
    "application/csv",
    "text/x-csv",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.template",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/octet-stream",
    "application/zip",
    "application/json",
  ]
column_separators: { "	": "<tab>", " ": "<space>", ",": ",", ";": ";", "|": "|" }
row_separators:
  rs0:
    match: "\r\n"
    name: "\\r\\n"
  rs1:
    match: "\r\r\n"
    name: "\\r\\r\\n"
  rs2:
    match: ""
    name: "none"
faq:
  - question: "What does FulfillSync do?"
    answer: "FulfillSync helps shop owners to update multiple tracking informations on multiple orders at once in bulk, without the hassle of update the tracking number manually in each order page."
  - question: "How does FulfillSync updates the fulfillments of orders?"
    answer: "FulfillSync will read from a file to identify the orders to fulfill. Tracking number and company will be updated to each product based on the SKU."
  - question: "What are the supported formats for FulfillSync to fulfill my orders?"
    answer: "The supported formats are CSV and Excel 2007(XLSX) for now."
  - question: "So, for multiple items in one order, it will be one row per item in my spreadsheet file?"
    answer: "Yes, as there might be multiple tracking information for multiple items from the same order."
  - question: "What if my products does not have SKU when the order is created?"
    answer: "If an SKU is not available, we will fulfill all products from the same order with the same tracking information. In other words, partial fulfillment does not work under this circumstance at the moment."
  - question: "How to configure Mapping Source File Columns / Index ?"
    answer: 'We start counting column by 0, thus the value of each field is the number of column that matches the field and minus by 1. For example, the second column of the file is the order number, thus the value of "*Order #" will be counted by 2 - 1 which is 1. '
  - question: "How do I know whether my source is valid and ready to sync?"
    answer: 'Please use the "Test Column Mapping" feature as shown in the Dashboard. If the file is uploaded to your FTP and the mapping is configured in the settings, the sample mapping will be displayed on a table in the popup. Otherwise, please make sure the FTP is configured properly, the file is uploaded and column mappings are configured.'
  - question: "For tracking company, is there any specific format to make it work?"
    answer: "There's no specific format of the tracking company. However, if the company name matched the company as listed in Shopify, the tracking number will be displayed properly in the order page of your admin dashboard."
  - question: "I've uploaded the file and configured but my orders are not fulfilled."
    answer: "One of the most common problem is that we couldn't find your Shopify orders based on the order numbers/name given. By default, FulfillSync use order numbers(e.g 1001, 1002) to identify your orders. If the provided format of your order is for example #1001, #1002, please change your 'Recognize my order by' to 'Order Name' instead of 'Order Number'. Verify your format by 'Test Column Mapping' and perform the sync again."
  - question: "Shipping Confirmation email does not include the tracking number or tracking company, how can I add it?"
    answer: "Adding tracking number to the email is beyond the control of FulfillSync. To do this, you’ll need to change the settings in your Shopify admin(Settings > Notifications > Shipping Confirmation) to include the attributes. The available attributes can be found here: https://help.shopify.com/manual/sell-online/notifications/email-variables#fulfillment-properties"
replacement_columns:
  - order_no_mapping
  - sku_mapping
  - quantity_mapping
  - tracking_company_mapping
  - tracking_url_mapping
profile_email_domain: "in.fulfillsync.com"

#aws settings
aws:
  websocket_endpoint: "https://86f6zwnfz8.execute-api.us-east-2.amazonaws.com/dev"
  redis_host: "stocksync-valkey-001.ihgmvr.0001.use1.cache.amazonaws.com"

enable_broadcast_websocket: true
memory_limit: 2_000_000
test_stores: ["stocksync123.myshopify.com", "munhoh.myshopify.com"]
ftp_special_host: []
bigcaring: { secret_key: "App2aWlS28bo" }

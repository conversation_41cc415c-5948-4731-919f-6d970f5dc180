test: &default

  # Credentials for REST APIs
  client_id: AVvxShTALzPPVNvqh_7zg87WzTmbYMm2T6ugenEPcFlB0wlDoIG2ydebhItyymMIDxQL0zrkDBq2NTLB
  client_secret: EPNDo8vT2Dn4TSOnuhPgCW0r8tEnqe55b4TUx9dhl_urbDwya_-9t5PmVA5SlZSJQvgP1q3XIplXLhrV

  # Mode can be 'live' or 'sandbox'
  mode: live

  # Credentials for Classic APIs
  app_id: APP-80W284485P519543T
  username: jb-us-seller_api1.paypal.com
  password: WX4WTU3S8MY44S7F
  signature: AFcWxV21C7fd0v3bYYYRCpSSRl31A7yDhhsPUU2XhtMoZXsWHFxu-RWy
  # # With Certificate
  # cert_path: "config/cert_key.pem"
  sandbox_email_address: <EMAIL>

  # # IP Address
  # ip_address: 127.0.0.1
  # # HTTP Proxy
  # http_proxy: http://proxy-ipaddress:3129/

  # verbose_logging: true

development:
  <<: *default

production:
  <<: *default
  mode: live

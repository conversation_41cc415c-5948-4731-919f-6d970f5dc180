require "aws-sdk-secretsmanager"
require "json"

def load_secrets
  unless ENV["RAILS_ENV"] == "development"
    p "Loading secrets..."
    get_secret_value_response = Aws::SecretsManager::Client.new(region: "us-east-1").get_secret_value(secret_id: "prod/fulfillsync")
    secrets = get_secret_value_response.secret_string
    JSON.parse(secrets).each do |key, value|
      ENV[key] = value
    end

    if ENV["RAILS_ENV"] == "staging"
      ENV["SHOPIFY_KEY"] = ENV["STAGING_SHOPIFY_KEY"]
      ENV["SHOPIFY_SECRET"] = ENV["STAGING_SHOPIFY_SECRET"]
    end
  end
end

load_secrets

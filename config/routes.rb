Rails.application.routes.draw do
  if Rails.env.development?
    mount GraphiQL::Rails::Engine, at: "/graphiql", graphql_path: "/graphql"
  end
  post "/graphql", to: "graphql#execute"

  get "/ecwid/authorize" => "application#ecwid_authorize"

  devise_for :admin_users, ActiveAdmin::Devise.config
  devise_for :users, controllers: {
    registrations: "users/registrations",
    sessions: "users/sessions"
  }
  devise_for :shops
  devise_scope :shop do
    get "/auth/shopify/callback" => "users/omniauth_callbacks#shopify"
    get "/auth/ecwid/callback" => "users/omniauth_callbacks#ecwid"
    get "/auth/bigcommerce/callback" => "users/omniauth_callbacks#bigcommerce"
    get "/auth/bigcommerce_app/callback" => "users/omniauth_callbacks#bigcommerce_app"
    get "/auth/paypal/callback" => "users/omniauth_callbacks#paypal"
    get "/auth/shopify" => "users/omniauth_callbacks#shopify_redirect_post"
    get "/auth/ecwid" => "users/omniauth_callbacks#ecwid_redirect_post"
    get "/auth/bigcommerce" => "users/omniauth_callbacks#bigcommerce_redirect_post"
    get "/auth/bigcommerce_app" => "users/omniauth_callbacks#bigcommerce_app_redirect_post"
    get "/auth/paypal" => "users/omniauth_callbacks#paypal_redirect_post"
  end
  ActiveAdmin.routes(self)

  mount ShopifyApp::Engine, at: "/"

  # controller :sessions do
  #   get "login" => :new, :as => :login
  #   post "login" => :create, :as => :authenticate
  #   # get 'auth/shopify/callback' => :callback
  #   get "logout" => :destroy, :as => :logout
  # end

  resources :integrations, only: [:index] do
    collection do
      get :sign_in
      get :bigcommerce_authorize
      post :shopify_authorize
    end
  end

  get "/signin" => "integrations#index"
  get "/signin/shopify" => "integrations#shopify", :as => "shopify_integrations"
  get "/signin/bigcommerce" => "integrations#bigcommerce", :as => "bigcommerce_integrations"

  # griddler
  mount_griddler

  root to: "dashboard#index"

  controller :dashboard do
    get "dashboard" => :index
    get :edit_preferences
    get :edit_integrations
    get :fulfillments
    get :subscribe
    get :subscribe_callback
    get "sources/:id/settings" => :settings
    get "sources/new" => :new_source
    get :offsite_payment_callback
    get :credit_charge
    get :credit_charge_callback
    get :export_fulfillment_logs
    get :export_sync_logs
    get :paypal
  end

  controller :home do
    post "uninstall"
    get "upgrade"
    get "callback_charge"
    get "faq"
    get :preferences
    patch :update_preferences
  end

  controller :webhook do
    post "stripe"
    post "shop_redact", to: "shop_redact"
    post "customer_data_request", to: "customer_data_request"
    post "customer_redact", to: "customer_redact"
  end

  resources :sources, except: [:index, :show] do
    member do
      get :test_column_mapping
      get :sync_fulfillments
      post :toggle_sync_status
      post :upload_file_and_sync
    end
  end

  resource :bigcommerce, only: [] do
    collection do
      get :load
      get :uninstall
      get :remove_user
    end
  end

  mount ActionCable.server => "/cable"

  match "*path", to: "dashboard#index", via: :all
  match "*unmatched_route", to: "application#routing_error", via: %i[get post]
end

default: &default
  adapter: postgresql
  encoding: unicode
  pool: 5

development:
  <<: *default
  database: fulfillment_sync_development
  host: localhost
  username: postgres
  password: cloudcoder

test:
  <<: *default
  database: fulfillment_sync_test
  username: postgres
  password: cloudcoder

production:
  <<: *default
  database: fulfillment_sync_production
  host: <%= ENV["DATABASE_HOST"] %>
  pool: 12
  username: <%= ENV["DATABASE_USERNAME"] %>
  password: <%= ENV["DATABASE_PASSWORD"] %>
  port: 5432

staging:
  <<: *default
  database: fulfillment_sync_production
  host: <%= ENV["DATABASE_HOST"] %>
  pool: 12
  username: <%= ENV["DATABASE_USERNAME"] %>
  password: <%= ENV["DATABASE_PASSWORD"] %>
  port: 5432

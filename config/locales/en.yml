# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# To learn more, please read the Rails Internationalization guide
# available at http://guides.rubyonrails.org/i18n.html.

en:
  activerecord:
    attributes:
      source:
        name: 'Source Name'
        source_type: 'Source Type'
        ssh_key: 'SSH Key'
        source_url: 'Direct URL'
        source_host: 'Host'
        source_parent_path: 'File Path'
        source_login: 'Username'
        source_password: 'Password'
        path_to_file: 'Path and file name'
        source_file_name: 'File'
        source_process: 'After Process'
        source_rename: 'Rename My File To'
        schedule_type: 'Schedule Type'
        schedule_time: 'Schedule Time'
        schedule_interval: 'Schedule Interval(Hours)'
        order_no_mapping: 'Order #'
        sku_mapping: 'Item SKU'
        quantity_mapping: 'Item Quantity'
        tracking_no_mapping: 'Tracking Number'
        tracking_company_mapping: 'Tracking Company'
        tracking_url_mapping: 'Tracking URL'
        order_key: 'Recognize my order by'
        ignore_key: 'Filter by column index'
        ignore_value: 'when value is'
        column_separator: 'Column Separator'
        ignore_empty_sku: "Don't fulfill orders without SKU"
  user_mailer:
    notify_finish_process:
      subject: 'FulfillSync Scheduled Process Finished'

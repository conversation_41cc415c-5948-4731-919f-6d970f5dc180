import typescript from '@rollup/plugin-typescript';
import react from '@vitejs/plugin-react-swc';
import { visualizer } from 'rollup-plugin-visualizer';
import { defineConfig } from 'vite';
import rails from 'vite-plugin-rails';

export default defineConfig(({ command, mode }) => {
  return {
    build: { sourcemap: mode === 'development' },
    plugins: [
      react(),
      rails({ compress: true }),
      // prevent build & deploy until recent commit with error is fixed
      command === 'build' &&
        typescript({
          tsconfig: 'tsconfig.json',
          noForceEmit: true,
          noEmitOnError: true, // fail build on type check error
        }),
      mode === 'visualizer' && visualizer({ open: true }),
    ].filter(Boolean),
  };
});

name: CI

env:
  # 7 GiB by default on GitHub, setting to 6 GiB
  # https://docs.github.com/en/actions/using-github-hosted-runners/about-github-hosted-runners#supported-runners-and-hardware-resources
  NODE_OPTIONS: --max-old-space-size=6144

# Remove default permissions of GITHUB_TOKEN for security
# https://docs.github.com/en/actions/using-jobs/assigning-permissions-to-jobs
permissions:
  contents: read # git fetch repo

on:
  # we don't allow direct pushes, but just in case...
  push:
    branches:
      - production
      - workflow/* # test workflow changes - fail fast, fix fast before PR merge
  # https://docs.github.com/en/billing/managing-billing-for-github-actions/about-billing-for-github-actions
  # due to billable time cost for workflow runners, only trigger on PRs for branches below:
  pull_request:
    branches:
      - production
      - master
      - chore/*
      - feat/*
      - fix/*
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.sha }}
  cancel-in-progress: true

jobs:
  lint:
    name: "Lint & Typecheck"
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest]
        node_version: [20]
      fail-fast: true
    timeout-minutes: 10
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 50 # Assume PRs are less than 50 commits

      # https://github.com/tj-actions/changed-files
      - name: Get changed frontend files
        id: changed-frontend-files
        # issue: release tag compromise https://github.com/tj-actions/changed-files/issues/2463#issuecomment-2727015784
        # fix: pin to specific commit SHA instead of release tag like vite https://github.com/vitejs/vite/blob/v6.2.2/.github/workflows/ci.yml#L51
        uses: tj-actions/changed-files@dcc7a0cba800f454d79fff4b993e8c3555bcc0a8 # v45.0.7
        with:
          files: |
            app/javascript/**
            vite.config.ts
            tsconfig.json
            pnpm-lock.yaml

      - name: Install pnpm
        if: steps.changed-frontend-files.outputs.any_changed == 'true'
        uses: pnpm/action-setup@v4

      - name: Setup node ${{ matrix.node_version }}
        if: steps.changed-frontend-files.outputs.any_changed == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node_version }}
          cache: "pnpm"

      - name: Install dependencies
        if: steps.changed-frontend-files.outputs.any_changed == 'true'
        run: pnpm install

      - name: Lint
        if: steps.changed-frontend-files.outputs.any_changed == 'true'
        run: pnpm lint

      - name: Typecheck
        if: steps.changed-frontend-files.outputs.any_changed == 'true'
        run: pnpm typecheck

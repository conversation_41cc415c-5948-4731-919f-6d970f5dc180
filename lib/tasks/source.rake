namespace :source do
  task missing_workers: :environment do |t, args|
    jobs = `ps aux | grep job`
    running_jobs = jobs.split("\n").reject { |a| a.index("delayed_job.uptracker_process").nil? }
    total_job = 2
    allocated_jobs = (0..total_job).collect { |a| "uptracker_process.#{a}" }

    running_jobs.each do |job_list|
      idx = job_list.split(".").slice(-2, 2).join(".")
      allocated_jobs.delete(idx)
    end
    allocated_jobs.each do |missing_idx|
      `RAILS_ENV=production bin/delayed_job --queues=update_fulfillments -i '#{missing_idx}' start`
    end
    if !jobs.include?("uptracker_mailer")
      `RAILS_ENV=production bin/delayed_job --queues=mailers -i "uptracker_mailer" start`
    end
  end

  desc "Sync fulfillments hourly"
  task sync_fulfillments: :environment do
    time = Time.now
    s = Source.active
    s.each do |source|
      job = Delayed::Job.where("handler like '%- #{source.id}\n%'").first
      next if job

      next unless source.shop

      sync_time = time.in_time_zone(source.shop.timezone)
      if source.next_schedule_time
        next_schedule_time = source.next_schedule_time.in_time_zone(source.shop.timezone)
      end
      if source.last_processing_time
        last_processing_time = source.last_processing_time.in_time_zone(source.shop.timezone)
      end
      default_day = 1.day
      if source.schedule_day == "weekday"
        default_day = 3.days
      end
      if (next_schedule_time && (next_schedule_time < sync_time)) || (last_processing_time && (last_processing_time < sync_time - default_day))
        if source.shop.active?
          source.set_queuing
          UpdateFulfillmentsJob.perform_later(source.id)
        else
          source.shop.deactivate_shop
        end
      end
    end
  end

  task check_missing_schedule: :environment do
    time = Time.now
    s = Source.all
    s.each do |source|
      callers = source.sync_logs.order("id desc").limit(3).collect(&:caller)
      if callers.include?("scheduler")
        source.sync_status = "started"
        source.save
      end
      if source.has_schedule_settings?
        if source.next_schedule_time
          if source.next_schedule_time < time
            source.next_schedule_time = source.find_next_schedule_time(time)
            source.save
            puts "#{source.id} reset schedule"
            # source.update_fulfillments(time)
          end
        else
          puts "no schedule: #{source.id}"
        end
      end
    end
  end

  desc "revert fulfillment"
  task revert_order: :environment do
    #     source = Source.find(262)
    #     source.shop.create_session
    #     #keys = 14519..14960
    #     keys = ["13957", "13983", "14005", "14018", "14020", "14021", "14049", "14057", "14080", "14065", "14104",
    # "14106", "14110", "14118", "14129", "14122", "14139", "14163", "14141", "14180", "14187", "14202",
    # "14222", "14250", "14279", "14291", "14299", "14303", "14331", "14339", "14345", "14353", "14368",
    # "14394", "14418", "14420", "14421", "14425", "14438", "14439", "14449", "14450", "14459", "14474"
    # , "14481", "14488", "14498", "14514"]

    #     keys = keys.collect{|a| "#{a.to_s}" }
    #     order_hash = source.shopify_all_data(keys)
    #     order_hash.values.each do | order |
    #       if f = order.fulfillments.first
    #         puts "unfulfilled #{order.order_number}"
    #         f.cancel
    #         sleep 1.second

    #       end
    #     end
  end

  desc "assign source email"
  task assign_source_email: :environment do
    Source.all.where(email: nil).each do |source|
      source.assign_source_email
      source.save
    end
  end

  desc "reset jobs"
  task restart_jobs: :environment do
    status_ids = Source.where(status: %w[running queuing]).collect(&:id)
    Source.where(status: %w[running queuing]).update_all("status = '' ")
    ids = Delayed::Job.where(queue: "update_fulfillments").all.collect { |a| a.handler[215..250].strip.to_i }.uniq
    puts (status_ids + ids).uniq.join(", ")
    puts "restart jobs"
    Delayed::Job.where(queue: "update_fulfillments").destroy_all
    `RAILS_ENV=production bin/delayed_job --queues=mailers -i "uptracker_mailer" restart`
    `RAILS_ENV=production bin/delayed_job --queues=update_fulfillments -i "uptracker_process.0" restart`
    `RAILS_ENV=production bin/delayed_job --queues=update_fulfillments -i "uptracker_process.1" restart`
    `RAILS_ENV=production bin/delayed_job --queues=update_fulfillments -i "uptracker_process.2" restart`
    puts "reload jobs"
    (status_ids + ids).uniq.each do |source_id|
      next unless source_id.to_i > 0

      begin
        source = Source.find(source_id)
        if source.shop.active?
          source.set_queuing
          puts "start job : source #{source.id}"
          UpdateFulfillmentsJob.perform_later(source.id)
        end
      rescue ActiveRecord::RecordNotFound
        puts "source #{source_id} deleted"
      end
    end
  end

  task update_shop_id_to_logs: :environment do
    Source.all.each do |source|
      shop_id = source.shop_id

      source.fulfillment_logs.update_all(shop_id: shop_id)
      source.sync_logs.update_all(shop_id: shop_id)
    end
  end

  task supplier_feeds: :environment do |_t, _args|
    lists = []
    Source.where.not("source_host LIKE ?", "%www.dropbox.com%").each do |source|
      log = source.sync_logs.where(status: "success").first
      next unless log

      domain = source.source_host
      domain = domain.gsub("https://", "")
      domain = domain.gsub("http://", "")
      domain = domain.gsub("ftp://", "")
      domain = domain.gsub("http;", "")
      domain = domain.gsub("google:httpps:", "")
      domain = domain.gsub(" docs.google.com", "docs.google.com")
      domain = domain.gsub("docs.goodocs.google.com", "docs.google.com")
      domain = domain.gsub("stock - sync.com", "stock-sync.com")
      domain = domain.gsub("ldrive.google.com", "drive.google.com")
      domain = domain.gsub("c167dd14d24a120b2b8e62ddc0395b72:<EMAIL>", "blondifox.myshopify.com")
      domain = domain.gsub("asd", "")
      domain = domain.gsub("a22222", "")
      domain = domain.gsub("ftp.", "")
      if domain.index("myshopify.com") && (domain.index("myshopify.com") > 0)
        next
      end

      next if domain.delete(".").to_i > 0

      domain.split("/")
      b = domain.split("/")
      next unless b.first

      latestdomain = b.first.downcase.strip
      latestdomain = latestdomain.split(":").first
      next if latestdomain.index(".").nil?
      if ["vendor.com", "dropbox.com", "ff", "us2.hostedcom", "hostedcom", "goo.gl", "cloudcoder.com"].include? latestdomain
        next
      end

      lists << latestdomain unless lists.include? latestdomain
    end
    lists = lists.uniq
    puts lists

    file = File.open("public/test.csv", "w")
    lists.each do |list|
      puts list
      file.write(list)
      file.write("\r\n")
    end
  end

  task copy_location_id: :environment do
    Source.where(platform: "shopify").each do |source|
      if source.location_id.blank?
        source.update(location_id: source.shop.primary_location_id)
        puts "Source #{source.id} copy success"
      end
    rescue
      puts "Source #{source.id} fail to copy"
    end
  end

  desc "mark fulfillment as complete"
  task complete_filfillments: :environment do
    s = Source.find(5223)
    s.shop.create_session
    s.fulfillment_logs.each_with_index do |log, idx|
      next if idx < 1248

      puts "#{idx} #{log.id} "
      count = 3
      begin
        order = ShopifyAPI::Order.find(id: log.shopify_order_id)
      rescue ActiveResource::ClientError => e
        puts "retry #{count}"
        count -= 1
        break if count < 0

        sleep 1.second
        retry
      end
      order.fulfillments.each do |fulfillment|
        next unless fulfillment.status != "success"

        count = 3
        begin
          puts "complete #{log.shopify_order_id} #{fulfillment.complete}"
        rescue ActiveResource::ClientError => e
          puts "retry complete #{count} #{e.message}"
          count -= 1
          break if count < 0

          sleep 1.second
          retry
        end
      end
    end
  end

  desc "update auto detect tracking"
  task update_auto_detect_tracking: :environment do
    Source.all.each do |source|
      source.auto_detect_tracking = source.tracking_url_mapping.nil? && source.tracking_url_default.nil? && source.tracking_company_mapping.nil? && source.tracking_company_default.nil?
      source.save
    end
  end
end

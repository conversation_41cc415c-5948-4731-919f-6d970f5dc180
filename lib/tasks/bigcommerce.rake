require "csv"

## bundle exec rake bigcommerce:update_tracking_number -- --store_hash "042b2" --access_token "24pnq612e6ghomjnor566kyv53iww9h" --file_path "./test.csv"

namespace :bigcommerce do
  desc "Update Tracking number"
  task :update_tracking_number do
    # store_hash     = "042b2"
    # access_token   = "24pnq612e6ghomjnor566kyv53iww9h"
    options = {}
    opts = OptionParser.new
    opts.on("--store_hash ARG", String) { |store_hash| options[:store_hash] = store_hash }
    opts.on("--access_token ARG", String) { |access_token| options[:access_token] = access_token }
    opts.on("--file_path ARG", String) { |file_path| options[:file_path] = file_path }
    args = opts.order!(ARGV) {}
    opts.parse!(args)

    csv_source = options[:file_path]
    if csv_source
      CSV.foreach(csv_source, headers: true, header_converters: :symbol) do |row|
        order_no = row[:customer_ref]
        tracking_no = row[:consignment_number]

        # get shipments
        begin
          shipment_url = "https://api.bigcommerce.com/stores/#{options[:store_hash]}/v2/orders/#{order_no}/shipments.json"
          shipment_response = Faraday.get(
            shipment_url, {}, {"X-Auth-Token" => options[:access_token], "Content-Type" => "application/json"}
          )
          puts shipment_url
          puts options[:access_token]
          puts shipment_response.inspect
          shipment_result = JSON.parse(shipment_response.body)
          if shipment_result.count > 1
            puts "order:#{order_no} has more than one shipment"
          else
            shipment = shipment_result.first
            shipment_id = shipment["id"]

            tracking_url = "https://api.bigcommerce.com/stores/#{options[:store_hash]}/v2/orders/#{order_no}/shipments/#{shipment_id}"
            tracking_payload = {
              tracking_number: tracking_no
            }
            tracking_response = Faraday.put(
              tracking_url, tracking_payload.to_json, {"X-Auth-Token" => options[:access_token], "Content-Type" => "application/json"}
            )

            puts "updated order:#{order_no},  tracking_number:#{tracking_no}" if tracking_response.success?
          end
        rescue JSON::ParserError
          puts "JSON parse error"
          next
        rescue URI::InvalidURIError
          next
        end
      end
    end
    exit
  end
end

namespace :backup do
  desc "Backup database"
  task :clean_db do
    monthago = Time.now.utc - 8.months

    max_log = FulfillmentLog.where("created_at < ?", monthago).order("id desc").first
    ActiveRecord::Base.connection.execute("insert into fulfillment_logs_archives select id, source_id, shopify_order_id, tracking_no, created_at, updated_at, order_number, tracking_company, sku, quantity, fulfillment_status, error_message, shopify_fulfillment_id, shop_id, financial_status, sync_log_id, original_values, bigcommerce_shipment_id from fulfillment_logs where id < #{max_log.id}")
    ActiveRecord::Base.connection.execute("delete from fulfillment_logs where id < #{max_log.id}")

    # Shop.all.each do | shop |
    #   FulfillmentLog.where(source_id: shop.sources.select(:id).collect(&:id)).where("fulfillment_logs.created_at < ?", 3.months.ago).delete_all

    #   SyncLog.where("created_at < ?", 3.months.ago).delete_all
    # end; nil
  end
end

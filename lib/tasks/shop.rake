namespace :shop do
  task populate_usd_amount: :environment do |_t, _args|
    # currencies = FulfillmentLog.distinct.select("currency").where.not(order_amount: nil).where.not(currency: nil).where(usd_amount: nil)

    uri = URI("http://data.fixer.io/api/latest?access_key=********************************&format=1")
    response = Net::HTTP.get(uri)
    rates_info = JSON.parse(response)

    uri = URI("https://api.exchangeratesapi.io/latest?symbols=USD&base=#{rates_info["base"]}")
    response = Net::HTTP.get(uri)
    base_rate = JSON.parse(response)["rates"]["USD"]

    rates = rates_info["rates"]

    FulfillmentLog.select("currency, order_amount").where.not(order_amount: nil).where.not(currency: nil).where(usd_amount: nil).each do |log|
      # puts "#{order.currency} #{order.order_amount}"
      log.update "usd_amount", (log.currency == "USD") ? log.order_amount : ((log.order_amount * (1 / rates[log.currency])) * base_rate)
      puts "#{log.currency} #{log.order_amount} #{log.usd_amount} #{rates[log.currency]} #{base_rate}"
      # log.save
    end
  end

  task check_payment: :environment do |_t, _args|
    count = 1
    Shop.all.each do |user|
      next if user.shopify_domain == "test-app-1.myshopify.com"

      begin
        # puts "#{user.id}"
        # session = ShopifyAPI::Session.new(user.shopify_domain, user.shopify_token)
        # session = ShopifyAPI::Session.new(domain: user.shopify_domain, token: user.shopify_token, api_version: Settings.api_version)
        # ShopifyAPI::Base.activate_session(session)
        session = ShopifyAPI::Auth::Session.new(shop: user.shopify_domain, access_token: user.shopify_token)
        ShopifyAPI::Context.activate_session(session)
        charge_all = ShopifyAPI::RecurringApplicationCharge.all

        if charge_all && !charge_all.empty?
          charge_all = charge_all.sort { |a, b| b.id <=> a.id }
          check_idx = 0
          charge_all.each_with_index do |charge, idx|
            if idx == check_idx
              if ["active"].include?(charge.status)
                if user.charge_id.blank?
                  puts "#{count}. #{user.shopify_domain} - need to update charge_id"
                  user.charge_id = charge.id
                  user.charged_at = Time.parse(charge.created_at)
                  user.save
                  count += 1
                end
              else
                check_idx += 1 unless user.charge_id.blank?
              end
            else
              break
            end
          end
          if check_idx == charge_all.count
            unless user.charge_id.blank?
              puts "#{count}. #{user.shopify_domain} - charge_id should be empty"
              user.reset_payment

              count += 1
            end
          end

        else
          unless user.charge_id.blank?
            puts "#{count}. #{user.shopify_domain} - remove charge_id"
            user.reset_payment
            count += 1
          end
        end
      rescue NoMethodError
        puts "no method found #{user.shopify_domain}"
      rescue ActiveResource::UnauthorizedAccess => e
        puts "#{count}. #{user.shopify_domain} - remove #{e.message}"
        user.deactivate_shop
        # user.reset_payment
        # user.save
      rescue ActiveResource::ResourceNotFound, ActiveResource::ClientError
        if user.charge_id
          puts "#{count}. #{user.shopify_domain} not found"
          count += 1
          user.reset_payment
        end
      end
    end
  end

  desc "Update user email"
  task update_shop_email: :environment do
    shops = Shop.where(email: nil)
    shops.each(&:update_details)
  end

  task update_shop_domain: :environment do
    shops = Shop.where(domain: nil)
    shops.each(&:update_details)
  end

  task update_notification_email: :environment do
    shops = Shop.where(notification_email: nil)
    shops.each do |shop|
      shop.update(notification_email: shop.email)
    end
  end

  task update_customer_email: :environment do
    Shop.where(customer_support_email: nil).each do |shop|
      puts "updating customer email for #{shop.id}: #{shop.email}"
      info = shop.get_shop_information
      shop.update(customer_support_email: info.customer_email)
    end
  end

  task fix_user_shop: :environment do
    Shop.all.each do |shop|
      if shop.user
      else
        user = User.where(email: shop.email).first
        if user
        end
      end
    end
  end

  task blast_new_uptracker_email: :environment do
    Shop.all.order("created_at asc").each do |shop|
      next if shop.email.blank?

      puts "sending to #{shop.id} : #{shop.email}"
      email = shop.email
      UserMailer.notify_new_uptracker(email).deliver_now
    end
  end

  task blast_annoucement: :environment do
    Shop.all.order("created_at asc").each do |shop|
      next if shop.email.blank?

      puts "sending to #{shop.id} : #{shop.email}"
      UserMailer.notify_api_change(shop).deliver_now
    end
  end

  task all_emails: :environment do
    emails = []
    User.all.each do |user|
      emails << user.email
    end
    Shop.all.each do |shop|
      emails << shop.email
      next unless shop.notification_email

      shop.notification_email.split(/[,;]/).each do |email|
        emails << email
      end
    end
    emails = emails.collect { |a| a.to_s.strip }.uniq
    csv_string = CSV.generate do |csv|
      emails.each do |email|
        csv << [email] if email
      end
    end
    File.write("emails.csv", csv_string)
  end

  task add_user_shop_location_id: :environment do
    Shop.where(provider: "Shopify").each do |shop|
      shop.create_session
      begin
        current_shop = ShopifyAPI::Shop.all.first
        shop.primary_location_id = current_shop.primary_location_id
        puts shop.primary_location_id
        shop.save
      rescue
      end
    end
  end

  task reset_webhook: :environment do
    User.where(provider: "Shopify").each do |shop|
      shop.create_session
      begin
        webhooks = ShopifyAPI::Webhook.all
        webhooks&.each do |webhook|
          next unless webhook.address.index("http://")

          webhook.address = webhook.address.sub("http://", "https://")

          puts "#{webhook.save} #{webhook.inspect}"
        end
      rescue => e
        puts e.message
      end
    end
  end

  task revert_fulfillment: :environment do
    source_id = 4317
    log_id = 47989155
    source = Source.find(source_id)
    shop = source.shop
    shop.create_session
    source.fulfillment_logs.where("id > ?", log_id).each do |log|
      if log.tracking_no.blank?
        f = ShopifyAPI::Fulfillment.find(id: log.shopify_fulfillment_id, params: {order_id: log.shopify_order_id})
        if f.cancel
          puts "#{log.shopify_fulfillment_id} cancelled"
        end
        shop.increment(:credit, 1)
      end
    end
  end

  task check_shopify_pending_subscription: :environment do
    Billing.where(status: "pending").each do |bill|
      next unless bill.shop_name.index(".myshopify.com")
      bill.check_subscription_status
    end
  end

  task update_export_webhook_new_url: :environment do |t, args|
    Shop.all.each do |shop|
      shop.create_session
      webhooks = ShopifyAPI::Webhook.all

      webhooks&.each do |webhook|
        puts "before: #{webhook.address}"
        unless webhook.address.include?("https://app")
          webhook.address = webhook.address.sub("https://", "https://app.")
          webhook.save
          puts "after: #{webhook.address}"
        end
      end
      shop.clear_session
    rescue ActiveResource::UnauthorizedAccess, ActiveRecord::RecordNotFound, ActiveResource::ForbiddenAccess, ActiveResource::ClientError
      puts "#{shop.shopify_domain}shop.id"
      next
    rescue => e
      Airbrake.notify(e)
    end
  end
end

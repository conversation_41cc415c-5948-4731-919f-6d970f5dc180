if defined?(Slackistrano::Messaging)
  module <PERSON><PERSON><PERSON><PERSON><PERSON>
    class CustomMessaging < Messaging::Base
      # See https://api.slack.com/docs/message-attachments
      def payload_for_updated
        {
          attachments: [{
            color: "good",
            title: "FulfillSync Deployed",
            fields: deployed_fields,
            fallback: super[:text]
          }]
        }
      end

      def payload_for_updating
        {
          attachments: [{
            color: "#66cbff",
            title: "Deploying FulfillSync",
            fields: deploying_fields,
            fallback: super[:text]
          }]
        }
      end

      private

      def deploy_message
        ENV.fetch("message", "None")
      end

      def deploying_fields
        [{
          title: "Environment",
          value: stage,
          short: true
        }, {
          title: "Branch",
          value: branch,
          short: true
        }, {
          title: "Deployer",
          value: deployer,
          short: true
        }, {
          title: "Deploy Reason",
          value: deploy_message,
          short: false
        }]
      end

      def deployed_fields
        [{
          title: "Environment",
          value: stage,
          short: true
        }, {
          title: "Branch",
          value: branch,
          short: true
        }, {
          title: "Deployer",
          value: deployer,
          short: true
        }, {
          title: "Time",
          value: elapsed_time,
          short: true
        }]
      end
    end
  end
end

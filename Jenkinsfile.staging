///Jenkinsfile to trigger build on FS

pipeline {
  agent any

  stages {
    stage('Build') {
      steps {
        script {
          sh '''#!/bin/bash -l
          rvm use ruby-3.3.6
          rvm ls
          . ~/.nvm/nvm.sh
          nvm use 20.19.0
          corepack enable
          pnpm i
          bundle install
          bundle exec cap staging deploy
          '''
        }
      }
    }
  }
}
